//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by SN Writer.rc
//
#define IDS_ABOUTBOX                    101
#define IDD_SNWRITER_DIALOG             102
#define IDR_MAINFRAME                   128
#define IDD_SCANDATA_DIALOG             129
#define IDD_SYSTEM_CONFIG_DIALOG        130
#define IDB_BITMAP_FAIL                 131
#define IDB_BITMAP_PASS                 133
#define IDR_SN_MENU                     134
#define IDD_ABOUTSN_DIALOG              141
#define IDD_IDENTIFY_VERIFY_DIALOG      142
#define IDD_CHANGE_PASSWD_DIALOG        143
#define IDD_FILE_LOAD_DIALOG            144
#define IDD_DIALOG1                     145
#define IDD_AUTOGEN_DIALOG              146
#define IDB_BITMAP_LOGO                 148
#define IDI_ICON1                       154
#define IDD_ENCRYPTION_DIALOG           155
#define IDB_EXIT_NORMAL                 157
#define IDB_LOGO                        158
#define IDB_MEUN_NORMAL                 159
#define IDB_MENU_NORMAL                 159
#define IDB_MIN_NORMAL                  160
#define IDB_LOGO2                       162
#define IDD_MESCONNECT_DIALOG           163
#define IDD_DIALOG2                     167
#define IDD_MESCONNECT_DIALOG2          168
#define IDD_CHECKFLAG_DIALOG            169
#define IDC_COUNT_TOTAL                 1000
#define IDC_BTN_SYSCONFIG               1001
#define IDC_BTN_START                   1002
#define IDC_BTN_QUIT                    1003
#define IDC_COUNT_PASS                  1004
#define IDC_COUNT_FAIL                  1005
#define IDC_PROGRESS_SN                 1006
#define IDC_COMBO_COMPORT               1007
#define IDC_COMBO_TARGET_TYPE           1008
#define IDC_MD_1_DBFILE_PATH            1009
#define IDC_MD_2_DBFILE_PATH            1010
#define IDC_AP_DBFILE_PATH              1011
#define IDC_BTN_MD1                     1012
#define IDC_BTN_MD2                     1013
#define IDC_BTN_AP                      1014
#define IDC_SECURITY_USB                1017
#define IDC_SWITCHTOOL_CHECK            1018
#define IDC_DUALTALK_CHECK              1019
#define IDC_WRITE_BARC_CHECK            1021
#define IDC_WRITE_IMEI_CHECK            1022
#define IDC_WRITE_WIFI_CHECK            1023
#define IDC_WRITE_BT_CHECK              1025
#define IDC_IMEI_CHECKSUM               1026
#define IDC_THREE_IMEI                  1027
#define IDC_IMEI_LOCK                   1028
#define IDC_DUAL_IMEI                   1029
#define IDC_FOUR_IMEI                   1030
#define IDC_BARC_HEADER_CHECK           1031
#define IDC_IMEI_3_HEADER_CHECK         1032
#define IDC_BT_HEADER_CHECK             1033
#define IDC_WIFI_HEADER_CHECK           1034
#define IDC_IMEI_1_HEADER_CHECK         1035
#define IDC_IMEI_2_HEADER_CHECK         1036
#define IDC_IMEI_4_HEADER_CHECK         1037
#define IDC_BARC_HEADER_STR             1038
#define IDC_BT_HEADER_STR               1039
#define IDC_WIFI_HEADER_STR             1040
#define IDC_IMEI_1_HEADER_STR           1041
#define IDC_IMEI_4_HEADER_STR           1042
#define IDC_IMEI_2_HEADER_STR           1043
#define IDC_IMEI_3_HEADER_STR           1044
#define IDC_DUAL_IMEI_SAME              1045
#define IDC_CHECK_INT                   1046
#define IDC_WRITE_ETHERNET_MAC_CHECK    1047
#define IDC_ETHERNET_HEADER_CHECK       1048
#define IDC_ETHERNET_HEADER_STR         1049
#define IDC_BARCODE_DATA                1050
#define IDC_WRITE_DRMKEY_MCID_CHECK     1051
#define IDC_BT_DATA                     1052
#define IDC_WRITE_MEID_CHECK            1052
#define IDC_DRMKEY_MCID_HEADER_CHECK    1053
#define IDC_WIFI_DATA                   1054
#define IDC_WRITE_ESN_CHECK             1054
#define IDC_DRMKEY_MCID_HEADER_STR      1055
#define IDC_IMEI_1_DATA                 1056
#define IDC_MEID_HEADER_STR             1056
#define IDC_SERIAL_NO_HEADER_CHECK      1057
#define IDC_IMEI_2_DATA                 1058
#define IDC_ESN_HEADER_STR              1058
#define IDC_SERIAL_NO_HEADER_STR        1059
#define IDC_IMEI_3_DATA                 1060
#define IDC_MEID_HEADER_CHECK           1060
#define IDC_IMEI_4_DATA                 1061
#define IDC_ESN_HEADER_CHECK            1061
#define IDC_BARC_HEADER_SCAN            1062
#define IDC_BT_HEADER_SCAN              1063
#define IDC_WIFI_HEADER_SCAN            1064
#define IDC_IMEI_1_HEADER_SCAN          1065
#define IDC_IMEI_2_HEADER_SCAN          1066
#define IDC_IMEI_3_HEADER_SCAN          1067
#define IDC_IMEI_4_HEADER_SCAN          1068
#define IDC_ETHERNET_HEADER_SCAN        1069
#define IDC_BTN_SCAN_CANCEL             1070
#define IDC_BTN_SCAN_OK                 1071
#define IDC_SCAN_MSG                    1072
#define IDC_STATIC_PASS                 1073
#define IDC_ETHERNET_MAC_DATA           1074
#define IDC_OPERATE_INFO                1075
#define IDC_DRMKEY_MCID_HEADER_SCAN     1076
#define IDC_DRMKEY_MCID_DATA            1077
#define IDC_BTN_INDITE                  1078
#define IDC_MEID_DATA                   1078
#define IDC_SERIAL_NO_DATA              1079
#define IDC_SERIAL_NO_HEADER_SCAN       1080
#define IDC_BTN_SAVE                    1081
#define IDC_MEID_HEADER_SCAN            1081
#define IDC_SN_VERSION                  1082
#define IDC_ESN_DATA                    1082
#define IDC_METACOREDLL_VERSION         1083
#define IDC_ESN_HEADER_SCAN             1083
#define IDC_METAAPPDLL_VERSION          1084
#define IDC_EDIT_VERIFY_PASSWD          1085
#define ID_BTN_VERIFY_OK                1086
#define ID_BTN_VERIFY_CANCEL            1087
#define IDC_EDIT_OLD_PASSWD             1088
#define IDC_EDIT_NEW_PASSWD             1089
#define IDC_EDIT_CONFIRM_PASSWD         1090
#define ID_CHG_PASSWD_CANCEL            1091
#define ID_CHG_PASSWD_OK                1092
#define IDC_SP_AUTH_FIEL_PATH           1094
#define IDC_DRMKEY_FIEL_PATH            1095
#define IDC_HDCP_BIN_FIEL_PATH          1096
#define IDC_HDCP_DATA_FIEL_PATH         1097
#define IDC_HDCP_CEK_FIEL_PATH          1098
#define IDC_BTN_SEL_SP_AUTH_FILE        1100
#define IDC_BTN_SEL_HDCP_BIN_FILE       1101
#define IDC_BTN_SEL_HDCP_DATAFILE       1102
#define IDC_BTN_SEL_HDCP_CEK_FILE       1103
#define IDC_BTN_SEL_DRMKEY_FILE         1104
#define IDC_AUTOGEN_BARC_CHECK          1105
#define IDC_AUTOGEN_IMEI_CHECK          1106
#define IDC_AUTOGEN_WIFI_CHECK          1107
#define IDC_AUTOGEN_BT_CHECK            1108
#define IDC_BARC_START_STR              1109
#define IDC_BARC_END_STR                1110
#define IDC_BARC_NEXT_STR               1111
#define IDC_IMEI_START_STR              1112
#define IDC_IMEI_END_STR                1113
#define IDC_IMEI_NEXT_STR               1114
#define IDC_BT_START_STR                1115
#define IDC_BT_END_STR                  1116
#define IDC_BT_NEXT_STR                 1117
#define IDC_WIFI_START_STR              1118
#define IDC_WIFI_END_STR                1119
#define IDC_WIFI_NEXT_STR               1120
#define IDC_BTN_AUTOGEN_OK              1121
#define IDC_BTN_AUTOGEN_CANCEL          1122
#define IDC_ETHERNET_START_STR          1123
#define IDC_ETHERNET_END_STR            1124
#define IDC_BARC_AUTOGEN_STEP           1125
#define IDC_IMEI_AUTOGEN_STEP           1126
#define IDC_BT_AUTOGEN_STEP             1128
#define IDC_ETHERNET_NEXT_STR           1129
#define IDC_WIFI_AUTOGEN_STEP           1130
#define IDC_ETHERNET_AUTOGEN_STEP       1131
#define IDC_MAINUI_TEMP2                1132
#define IDC_AUTOGEN_ETHERNET_CHECK      1134
#define IDC_MAINUI_TEMP1                1135
#define IDC_DRMKEY_MCID_START_STR       1136
#define IDC_DRMKEY_MCID_END_STR         1137
#define IDC_BARC_STATIC                 1138
#define IDC_DRMKEY_MCID_NEXT_STR        1139
#define IDC_BT_STATIC                   1140
#define IDC_DRMKEY_MCID_AUTOGEN_STEP    1141
#define IDC_WIFI_STATIC                 1142
#define IDC_EXTERN_MD_DOWNLOAD          1143
#define IDC_AUTOGEN_DRMKEY_MCID_CHECK   1144
#define IDC_PWD_DATA                    1145
#define IDC_IMEI_1_STATIC               1146
#define IDC_PWD_STATIC                  1147
#define IDC_IMEI_2_STATIC               1148
#define IDC_PWD_MSG                     1149
#define IDC_IMEI_3_STATIC               1150
#define IDC_IMEI_4_STATIC               1151
#define IDC_ETHERNET_STATIC             1152
#define IDC_MCID_STATIC                 1153
#define IDC_SERIAL_NO_STATIC            1154
#define IDC_DENARY_INCREASE             1155
#define IDC_MEID_STATIC                 1156
#define IDC_HEX_INCREASE                1157
#define IDC_ESN_STATIC                  1158
#define IDC_WRITE_SERIAL_NO_CHECK       1159
#define IDC_MDDB_FROM_DUT_CHECK         1160
#define IDC_APDB_FROM_DUT_CHECK         1161
#define IDC_LOG_DIR                     1162
#define IDC_BTN_LOG                     1163
#define IDC_LOCK_OTP                    1164
#define IDC_DIALOG_TITLE                1165
#define IDC_TITLEBAR_BG                 1166
#define IDC_MENU_BTN                    1169
#define IDC_MIN_BTN                     1170
#define IDC_CLOSE_BTN                   1171
#define IDC_SCAN_CODE_IMEI              1172
#define IDC_SCAN_CODE_BARCODE           1173
#define IDC_CHECK_IMEI_CHECK            1174
#define IDC_GWMC_COMBO                  1174
#define IDC_CHECK_VERSION               1174
#define IDC_CHECK_SERIAL_NO_CHECK       1175
#define IDC_COUNT_KEY                   1176
#define IDC_BARCODE_LOG_DIR             1176
#define IDC_BTN_BARCODE_LOG             1177
#define IDC_MESCONNECT_CHECK            1178
#define IDC_MESCONNECT_BUTTON           1179
#define IDC_EDIT_INPUT_SN               1179
#define IDC_GDH_EDIT                    1180
#define IDC_RESOURCE_EDIT               1180
#define IDC_STATIC_TIPS                 1180
#define IDC_CPMC_EDIT                   1181
#define IDC_CZR_EDIT                    1182
#define IDC_USERNAME                    1183
#define IDC_USERNAME_EDIT               1184
#define IDC_PASSWORD                    1185
#define IDC_EDIT2                       1186
#define IDC_PASSWORD_EDIT               1186
#define IDC_USEREDIT                    1186
#define IDC_FACTORY_RESET               1187
#define IDC_REBOOT                      1188
#define IDC_EDIT1                       1190
#define IDC_VERSION_STR                 1190
#define IDC_GDHEDIT                     1190
#define IDC_VERSION_STR2                1191
#define IDC_INT_STR                     1191
#define IDC_MESSELECT                   1192
#define IDC_XHEDIT                      1193
#define IDC_CHECKALL                    1195
#define IDC_CHECKMDM_FLAG               1196
#define IDC_CHECKCURRENT_FALG           1197
#define IDC_CHECKEFUSE_FLAG             1198
#define IDC_CHECKAGING_FLAG             1199
#define IDC_CHECKMMI1_FLAG              1200
#define IDC_CHECKHardwareInfo_FLAG      1201
#define IDC_CHECKMMI2_FLAG              1202
#define IDC_CHECKCOUPING_FLAG           1203
#define IDC_COMBO1                      1206
#define IDC_MDMFLAG_COMBOX              1206
#define IDC_CHECK1                      1207
#define IDC_WRITE_SIMEI_CHECK           1207
#define IDC_STATIC_ORDER                1208
#define IDC_ORDER_EDIT                  1209
#define IDR_OPTION_SWITCHTOOL           32772
#define IDR_OPTION_EXTMODEM             32773
#define IDR_OPTION_SECURITY_USB         32774
#define IDR_META_MODE                   32776
#define IDR_ATCMD_MODE                  32777
#define IDR_OPTION_HDCP                 32778
#define IDR_OPTION_DRMKEY               32779
#define IDR_ENG_TO_OPERATER             32780
#define IDR_CHANGE_PASSWD               32781
#define IDR_OPERATOR_TO_ENG             32782
#define IDR_ABOUT_SN                    32783
#define IDR_SP_AUTH_FILE                32785
#define IDR_OPTION_HDCPDATA             32786
#define IDR_ENABLE_AUTOGEN              32788
#define IDR_OPTION_CHECK_CAL_FLAG       32790
#define IDR_OPTION_CHECK_FT_FLAG        32791
#define IDR_OPTION_SKIP_WRITE_PRODINFO  32792
#define IDR_CHECK_BTWIFI                32793
#define IDR_OPTION_COMPOSITE_DEVICE     32794
#define IDR_BACKNV_TOPC                 32795
#define IDR_OEM_LOCK                    32796
#define IDR_OPTION_ALREADYINMETA        32797
#define IDR_OPTION_ATTESTATION          32798
#define IDC_ATTESTATION_KEY_FIEL        32799
#define IDC_BTN_SEL_ATTESTATION_KEY     32800
#define IDR_SUPPORT_TEE                 32899
#define ID_CHECKFLAG_CHECKFLAG          32900
#define IDR_OPTION_CHECKFLAG            32902
#define ID_KEYFILES_RKP                 32903
#define ID_RKP_GETCSRFROMDUT            32904
#define ID_RKP_UPLOADCSRTOYN            32905
#define ID_OPTION_GETCSRFROMDUT         32906

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        171
#define _APS_NEXT_COMMAND_VALUE         32907
#define _APS_NEXT_CONTROL_VALUE         1209
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
