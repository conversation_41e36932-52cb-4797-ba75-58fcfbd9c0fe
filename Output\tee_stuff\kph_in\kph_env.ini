; Configuration for KPHA

[core]
forceinit = true        ; Whether a device is allowed to be exported multiple times
do_upload = false
ifaa = false
soter = false
secondary_cert = true
vturkey = true
rkp = false
rkpforce = false

[log]
notice_silent = false   ; If `false`, popup a message box for notice happens (e.g. kpha_init completes)
err_silent = false      ; If `false`, popup a message box if error happens

[errata]
errata_1000001 = false  ; for service meta_tst not in late_start mode
errata_2000001 = false  ; for kph failing to work with non-rpmb device
errata_2000002 = false  ; for kph writing finish bit for export_dev_inf

[vturkey]
url = http://**************/vturkey
keybox_uuid = 10242_df379e336abf521aeaa010095d48f45a8ddba9926973e898d3000a4b6cbeccaa  ; keybox-uuid for vturkey request
install_keybox = true ; RKP and keybox must be enabled at the same time

[rkp]
test_mode   = false       ; whether to export CSR in test mode
readsn      = false        ; if value is true,read serialno;if value is false read barcode.default value is true
readimei    = true        ;
barcode_index = 0
saveAsFile  = false
savePath    = 

[kus]
server = https://pl.trustkernel.com/kus/   ; URL to key-upload-server
