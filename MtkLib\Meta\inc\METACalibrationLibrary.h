/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
/*****************************************************************************/
/*!
 * \file METACalibrationLibrary.h
 * \mainpage META Share Library Development Kit
 * \author MediaTek Inc.
 *
 * ******************************************
 *
 * \defgroup General General
 * This section describe the General functions
 *
 * \defgroup TriCalFlow Trigger calibration flow
 * This section describe the "Trigger calibration flow"  functions
 *
 * \defgroup TriCalFlowStruct Structure for triggering the calibration flow
 * \ingroup TriCalFlow
 * TriCalFlowStruct is a subgroup of TriCalFlow
 *
 * \defgroup TriTuningFlow Trigger tuning flow
 * This section describe the "Trigger tuning flow"  functions
 *
 * \defgroup TriTuningFlowStruct Structure for triggering the calibration flow
 * \ingroup TriTuningFlow
 * TriTuningFlowStruct is a subgroup of TriTuningFlow
 */


#ifndef __META_CALIBRATION_LIBRARY_H__
#define __META_CALIBRATION_LIBRARY_H__

#include "METAStatusDefinition.h"
#include "METACommonDefinition.h"

#ifdef __cplusplus
extern "C" {
#endif

//#define SUPPORT_UESIM  //define this macro to support UESIM

#if !defined(PRIVATE_UNLESS_UNITTEST) && !defined(PROTECTED_UNLESS_UNITTEST)

#ifdef _UNITTEST
#define PRIVATE_UNLESS_UNITTEST public
#define PROTECTED_UNLESS_UNITTEST public
#else
#define PRIVATE_UNLESS_UNITTEST private
#define PROTECTED_UNLESS_UNITTEST protected
#endif

#endif // !defined(PRIVATE_UNLESS_UNITTEST) && !defined(PROTECTED_UNLESS_UNITTEST)

/**
 * \ingroup General
 * \details The log format enum
 */
typedef enum
{
    E_FHC_FORMAT = 0,
    E_NSFT_FORMAT = 1,
    E_ET_LOG_FORMAT = 2
} E_FORMAT_TYPE;

/**
 * \ingroup General
 * \details The enumeration for log target
 */
typedef enum
{
    E_METACalibrationLibrary_LOG_TARGET_UNDEFINED = 0,
    E_METACalibrationLibrary_LOG_TARGET_FACTORY_LOG = 1,
    E_METACalibrationLibrary_LOG_TARGET_FACTORY_REPORT = 2,
    E_METACalibrationLibrary_LOG_TARGET_INST_LIB_SCIP = 3,
    E_METACalibrationLibrary_LOG_TARGET_META_CORE_DLL = 4,
    E_METACalibrationLibrary_LOG_TARGET_META_APP_DLL = 5,
    E_METACalibrationLibrary_LOG_TARGET_META_RF_TOOL = 6,
} E_METACalibrationLibrary_LOG_TARGET;

/**
 * \ingroup General
 * \details The enumeration for log level
 */
typedef enum
{
    E_METACalibrationLibrary_LOG_LEVEL_UNDEFINED = 0,
    E_METACalibrationLibrary_LOG_LEVEL_ERROR = 1,
    E_METACalibrationLibrary_LOG_LEVEL_WARNING = 2,
    E_METACalibrationLibrary_LOG_LEVEL_INFO = 3,
    E_METACalibrationLibrary_LOG_LEVEL_DEBUG = 4
} E_METACalibrationLibrary_LOG_LEVEL;
/**
 * \ingroup General
 * \details The control mask item for user interface
 */
typedef enum
{
    E_METACalibrationLibrary_LOG_UI_NO_ACTION = 0,
    E_METACalibrationLibrary_LOG_UI_CLEAR = 1 << 0,
} E_METACalibrationLibrary_LOG_UI_ACTION;
/**
 * \ingroup General
 * \details The log entity including target, level, message, length of the message
 */
typedef struct
{
    E_METACalibrationLibrary_LOG_TARGET     logTarget;        /**< the log target */
    E_METACalibrationLibrary_LOG_LEVEL      logLevel;         /**< the log level */
    unsigned short                          logBufferSize;    /**< the log buffer size */
    const wchar_t                           *logBuffer;        /**< the log string */
    unsigned int                            uiControlMask;    /**< the control mask of user interface, refer to E_METACalibrationLibrary_LOG_UI_ACTION */
    unsigned short year;
    unsigned short month;
    unsigned short day;
    unsigned short hour;
    unsigned short minute;
    unsigned short second;
    unsigned short milliSecond;
} S_METACalibrationLibrary_LOG_T;

/**
 * \ingroup General
 * \details The execution result for calibration Library functions
 */
typedef enum
{
    E_METACalibrationLibrary_RESULT_SUCCESS = 0, /**< function execution successfully */
    E_METACalibrationLibrary_RESULT_FAILED = 1,  /**< function execution failed */
    E_METACalibrationLibrary_RESULT_END = 65536  /**< unused*/
} E_METACalibrationLibrary_RESULT;
/**
 * \ingroup General
 * \details The Band No enumeration
 */
typedef enum
{
    GSM_BAND_850  = 1,
    GSM_BAND_900  = 2,
    GSM_BAND_1800 = 3,
    GSM_BAND_1900 = 4,

    TDS_BAND_A     = 1,
    TDS_BAND_E     = 2,
    TDS_BAND_F     = 3,

    WCDMA_BAND_1   = 1,

    LTE_BAND_1     = 1,

    CDMA_BAND_0    = 0,

    NR_BAND_N1     = 1,
    WIFI_2G4_ANT0 = 0,
    WIFI_2G4_ANT1 = 1,
    WIFI_5G_ANT0 = 2,
    WIFI_5G_ANT1 = 3,
    BT_ANT0 = 0,
    BT_ANT1 = 1,
} E_BAND_INDEX_T;

/**
 * \ingroup TriCalFlowStruct
 * \details GSM/GPRS/EDGE calibratoin item settings
 */
typedef struct
{
    bool b_gge_tadc_cal;           /**< temperature sensor calibration */
    bool b_gge_cap_id_cal;         /**< CAP ID calibration */
    bool b_gge_fb_dac_cal;         /**< FB DAC calibration */
    bool b_gge_slope_skew_cal;     /**< Slope Skew calibration */
    bool b_gge_afc_cal;            /**< AFC calibration */
    bool b_gge_afc_trx_offset_cal; /**< AFC TRX offset calibration */
    bool b_gge_agc_cal;            /**< AGC calibration */
    bool b_gge_apc_cal;            /**< APC calibration */
    bool b_gge_edge_apc_cal;       /**< EDGE APC calibration */
    bool b_gge_txiq_cal;           /**< TXIQ calibration */
    bool b_gge_fhc_cal;            /**< enable FHC or not */
    bool b_gge_tpc_subband_cal;    /**< enable APC sub-band calibration or not */
    bool b_gge_agc_w_cal;          /**< AGC W-coefficient calibration */
    bool b_gge_AD6546_apc_cal;     /**< AD6546 APC calibration */
    bool b_gge_apc_check;          /**< APC power check attribute */
    bool b_gge_edge_apc_check;     /**< EDGE APC power check attribute */
    bool b_gge_OGPC_apc_cal;       /**< Optimized GSM PCL calibration (works in b_gge_apc_cal = TRUE )*/
    bool b_gge_edge_OGPC_apc_cal;  /**< Optimized EDGE PCL calibration (works in b_gge_edge_apc_cal = TRUE )*/
    bool b_gge_apc_dc_offset_cal;  /**< APC DC offset calibration */
    bool b_gge_rxd_another_port_cal; /**< RX diversity calibrated by another port ( 2-port calibartion: no need the combiner) */
} S_METACalibrationLibrary_GGE_CAL_ITEM_T;
/**
 * \ingroup TriCalFlowStruct
 * \details GSM/GPRS/EDGE testing item settings
 */
typedef struct
{
    bool b_gge_nsft_gmsk;      /**< NSFT GMSK TX performance test */
    bool b_gge_nsft_ber;       /**< NSFT BER test */
    bool b_gge_nsft_epsk;      /**< NSFT EPSK TX performance test */
    bool b_gge_nsft_list_mode; /**< enable list mode or not */
} S_METACalibrationLibrary_GGE_NSFT_ITEM_T;
/**
 * \ingroup TriCalFlowStruct
 * \details WCDMA calibratoin item settings
 */
typedef struct
{
    bool b_wcdma_tadc_cal;      /**< temperature sensor calibration */
    bool b_wcdma_dcxo_afc_cal;  /**< DCXO AFC calibration */
    bool b_wcdma_afc_cal;       /**< AFC calibration */
    bool b_wcdma_agc_cal;       /**< AGC calibration */
    bool b_wcdma_apc_cal;       /**< APC calibration */
    bool b_wcdma_fhc_cal;       /**< enable FHC or not */
    bool b_wcdma_tpc_subband_cal;     /**< enable APC sub-band calibration or not */
    bool b_wcdma_rxd_pathloss_check;  /**< RX diversity check attribute */
    bool b_wcdma_rxd_another_port_cal;/**< RX diversity calibrated by another port ( 2-port calibartion: no need the combiner) */
    bool b_wcdma_dpd_cal;          /**< DPD calibration */
} S_METACalibrationLibrary_WCDMA_CAL_ITEM_T;
/**
 * \ingroup TriCalFlowStruct
 * \details WCDMA testing item settings
 */
typedef struct
{
    bool b_wcdma_nsft_tpc;  /**< NSFT TX performance test */
    bool b_wcdma_nsft_ber;  /**< NSFT BER test */
    bool b_wcdma_nsft_prach;/**< NSFT PRACH test  (open loop power) */
    bool b_wcdma_hsdpa_nsft;/**< NSFT HSDPA performance test */
    bool b_wcdma_hsupa_nsft;/**< NSFT HSUPA performance test */
    bool b_wcdma_list_mode; /**< enable list mode or not */
} S_METACalibrationLibrary_WCDMA_NSFT_ITEM_T;
/**
 * \ingroup TriCalFlowStruct
 * \details TDSCDMA calibratoin item settings
 */
typedef struct
{
    bool b_tda_tadc_cal;    /**< temperature sensor calibration */
    bool b_tda_cap_id_cal;  /**< CAP ID calibration */
    bool b_tda_afc_cal;     /**< AFC calibration */
    bool b_tda_rx_path_loss_cal; /**< AGC calibration */
    bool b_tda_tpc_cal;          /**< APC calibration */
    bool b_tda_tpc_subband_cal;  /**< enable APC sub-band calibration or not */
    bool b_tda_fhc_cal;          /**< enable FHC or not */
} S_METACalibrationLibrary_TD_CAL_ITEM_T;
/**
 * \ingroup TriCalFlowStruct
 * \details TDSCDMA testing item settings
 */
typedef struct
{
    bool b_tda_nsft_tpc;   /**< NSFT TX performance test */
    bool b_tda_nsft_ber;   /**< NSFT BER test */
    bool b_tda_ft_tpc;     /**< NSFT FT TX performance test */
    bool b_tda_ft_ber;     /**< NSFT FT TX BER test */
    bool b_tda_list_mode;  /**< enable list mode or not */
} S_METACalibrationLibrary_TD_NSFT_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details TAS calibratoin item settings
 */
typedef struct
{
    bool b_traditional_tas_test; /**< traditional mode */
    bool b_lte_tas_tx_test;     /**< LTE TAS TX test */
    bool b_lte_tas_rx_test;     /**< LTE TAS RX test */
    bool b_nr_tas_tx_test;      /**< NR TAS TX test */
    bool b_nr_tas_rx_test;      /**< NR TAS RX test */
    bool b_nr_fr2_tas_tx_test;  /**< NR FR2 TAS TX test */
    bool b_nr_fr2_tas_rx_test;  /**< NR FR2 TAS RX test */
    bool b_wcdma_tas_tx_test;   /**< WCDMA TAS TX test */
    bool b_wcdma_tas_rx_test;   /**< WCDMA TAS RX test */
    bool b_tdscdma_tas_tx_test; /**< TDSCDMA TAS TX test */
    bool b_tdscdma_tas_rx_test; /**< TDSCDMA TAS RX test */
    bool b_c2k_tas_tx_test;     /**< C2K TAS TX test */
    bool b_c2k_tas_rx_test;     /**< C2K TAS RX test */
    bool b_gge_tas_tx_test;     /**< GSM TAS TX test */
    bool b_gge_tas_rx_test;     /**< GSM TAS RX test */
    bool b_iot_ntn_tas_tx_test;     /**< GSM TAS TX test */
    bool b_iot_ntn_tas_rx_test;     /**< GSM TAS RX test */
} S_METACalibrationLibrary_TAS_TEST_ITEM_T;
/**
 * \ingroup TriCalFlowStruct
 * \details CDDC test item settings
 */
typedef struct
{
    bool b_cddc_test_apt;     /**< CDDC test */
} S_METACalibrationLibrary_CDDC_TEST_ITEM_T;

/**
* \ingroup TriCalFlowStruct
* \details Transceiver Power test item settings
*/
typedef struct
{
    bool b_lte_transceiver_power_tx_test;     /**< LTE Transceiver Power TX test */
    bool b_nr_transceiver_power_tx_test;      /**< NR Transceiver Power TX test */
    bool b_wcdma_transceiver_power_tx_test;   /**< WCDMA Transceiver Power TX test */
    bool b_tdscdma_transceiver_power_tx_test; /**< TDSCDMA Transceiver Power TX test */
    bool b_c2k_transceiver_power_tx_test;     /**< C2K Transceiver Power TX test */
    bool b_gge_transceiver_power_tx_test;     /**< GSM Transceiver Power TX test */
    bool b_traditional_transceiver_power_tx_test; /**< Traditional Transceiver Power TX test */
} S_METACalibrationLibrary_TRANSCEIVER_POWER_TEST_ITEM_T;

/**
* \ingroup TriCalFlowStruct
* \details MIPI RF Front End Check test item settings
*/
typedef struct
{
    bool b_mipi_typeA_rffe_check_test;      /**< MIPI-A RFFE Check test */
    bool b_mipi_typeD_rffe_check_test;      /**< MIPI-D RFFE Check test */
} S_METACalibrationLibrary_RFFE_CHECK_TEST_ITEM_T;

/**
* \ingroup TriCalFlowStruct
* \details OSL calibration
*/
typedef struct
{
    bool b_osl_cal;      /**< osl calibration */
} S_METACalibrationLibrary_OSL_CAL_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details Other calibratoin item settings
 */
typedef struct
{
    bool b_others_GPSCOCLOCK;   /**< GPS CO-TMS calibration  */
    unsigned char others_cotms_solution; /**< GPS CO-TMS calibration selection (0: GSM, 1: LTE) */
} S_METACalibrationLibrary_Others_ITEM_T;

#if defined(__META_LTE__)
/**
 * \ingroup TriCalFlowStruct
 * \details LTE calibratoin item settings
 */
typedef struct
{
    bool b_lte_tadc_cal;                /**< temperature sensor calibration */
    bool b_lte_cap_id_cal;              /**< CAP ID calibration */
    bool b_lte_afc_cal;                 /**< AFC calibration */
    bool b_lte_rx_path_loss_cal;        /**< AGC calibration */
    bool b_lte_tpc_cal;                 /**< APC calibration */
    bool b_lte_tpc_subband_cal;         /**< enable APC sub-band calibration or not */
    bool b_lte_fhc_cal;                 /**< enable FHC calibration or not */
    bool b_lte_rxd_another_port_cal;    /**< RX diversity calibrated by another port ( 2-port calibartion: no need the combiner) */
    bool b_lte_rxd_another_port_cal_main_path_enabled; /**< enable main path calibration ( works in b_lte_rxd_another_port_cal = TRUE) */
    bool b_lte_rxd_another_port_cal_div_path_enabled;  /**< enable diversity path calibration ( works in b_lte_rxd_another_port_cal = TRUE) */
    bool b_lte_srs_cal;                 /**< enable SRS calibration or not */
    bool b_lte_dpd_cal;                 /**< DPD calibration */
    bool b_lte_et_cal;                  /**< ET calibration */
    bool b_lte_multi_cpl_cal;           /**< Multi CPL calibration */
    bool b_lte_tas_rx_path_loss_cal;    /**< enable TAS rx calibration */
    bool b_lte_tas_tpc_cal;             /**< enable TAS tx calibration */
} S_METACalibrationLibrary_LTE_CAL_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details LTE PreTest item settings
 */
typedef struct
{
    bool b_lte_preTest;  /**< enable LTE PreTest */
} S_METACalibrationLibrary_LTE_PRETEST_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details LTE testing item settings
 */
typedef struct
{
    bool b_lte_nsft_tx;   /**< NSFT TX performance test */
    bool b_lte_nsft_rx;   /**< NSFT RX performance test */
    bool b_lte_rxd_another_port_test;  /**< RX diversity test by another port ( 2-port calibartion: no need the combiner) */
    bool b_lte_traditional_nsft;       /**< enable traditional NSFT or not */
} S_METACalibrationLibrary_LTE_NSFT_ITEM_T;
#endif //#if defined(__META_LTE__)
/**
 * \ingroup TriCalFlowStruct
 * \details NR calibratoin item settings
 */
typedef struct
{
    bool b_nr_rx_path_loss_cal;     /**< AGC calibration */
    bool b_nr_tpc_cal;              /**< APC calibration */
    bool b_nr_tpc_subband_cal;      /**< enable APC sub-band calibration or not */
    bool b_nr_multiport_cal;        /**< enable multiport calibraiton or not */
    bool b_nr_srs_cal;              /**< enable SRS calibraiton or not */
    bool b_nr_dpd_cal;              /**< enable DPD calibraiton or not */
    bool b_nr_fr2_dpd_cal;          /**< enable FR2 DPD calibraiton or not */
    bool b_nr_et_cal;               /**< enable ET calibraiton or not */
    bool b_nr_multi_cpl_cal;        /**< enable Multi CPL calibration or not */
    bool b_nr_tas_rx_path_loss_cal; /**< enable TAS rx calibration */
    bool b_nr_tas_tpc_cal;          /**< enable TAS tx calibration */
    bool b_nr_afc_cal;              /**< AFC calibration */
    bool b_nr_cap_id_cal;           /**< CAP ID Calibration */
} S_METACalibrationLibrary_NR_CAL_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details NR PreTest item settings
 */
typedef struct
{
    bool b_nr_preTest;  /**< enable NR PreTest */
} S_METACalibrationLibrary_NR_PRETEST_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details NR testing item settings
 */
typedef struct
{
    bool b_nr_nsft_tx;  /**< NSFT TX performance test */
    bool b_nr_nsft_rx;  /**< NSFT RX performance test */
    bool b_nr_traditional_nsft; /**< enable traditional NSFT or not */
    bool b_nr_multiport_nsft; /**< enable multiport NSFT or not */
} S_METACalibrationLibrary_NR_NSFT_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details NR FR2 testing item settings
 */
typedef struct
{
    bool b_nr_fr2_nsft_tx;  /**< NSFT TX performance test */
    bool b_nr_fr2_nsft_rx;  /**< NSFT RX performance test */
    bool b_nr_fr2_traditional_nsft; /**< enable traditional NSFT or not */
    bool b_nr_fr2_multiport_nsft; /**< enable multiport NSFT or not */
} S_METACalibrationLibrary_NR_FR2_NSFT_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details IoT-NTN calibratoin item settings
 */
typedef struct
{
    bool b_iot_ntn_rx_path_loss_cal;  /**< RX path loss calibration */
    bool b_iot_ntn_tpc_cal;           /**< TPC profile calibration */
    bool b_iot_ntn_multiport_cal;     /**< multi-port calibration */
    bool b_iot_ntn_tas_rx_path_loss_cal;    /**< enable TAS rx calibration */
    bool b_iot_ntn_tas_tpc_cal;             /**< enable TAS tx calibration */
} S_METACalibrationLibrary_IOT_NTN_CAL_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details IoT-NTN testing item settings
 */
typedef struct
{
    bool b_iot_ntn_nsft_tx;  /**< NSFT TX performance test */
    bool b_iot_ntn_nsft_rx;  /**< NSFT RX performance test */
} S_METACalibrationLibrary_IOT_NTN_NSFT_ITEM_T;

#if defined(__META_C2K__)
/**
 * \ingroup TriCalFlowStruct
 * \details C2K calibratoin item settings
 */
typedef struct
{
    bool b_c2k_tadc_cal;     /**< temperature sensor calibration */
    bool b_c2k_dcxo_afc_cal; /**< DCXO AFC calibration */
    bool b_c2k_afc_cal;      /**< AFC calibration */
    bool b_c2k_agc_cal;      /**< AGC calibration */
    bool b_c2k_apc_cal;      /**< APC calibration */
    bool b_c2k_fhc_cal;      /**< enable FHC calibration or not */
    bool b_c2k_tpc_subband_cal;      /**< enable APC sub-band calibration or not */
    bool b_c2k_rxd_pathloss_check;   /**< enable RXD pathloss check or not */
    bool b_c2k_rxd_another_port_cal; /**< RX diversity calibrated by another port ( 2-port calibartion: no need the combiner) */
    bool b_c2k_dpd_cal;             /**< enable DPD calibraiton or not */
} S_METACalibrationLibrary_C2K_CAL_ITEM_T;
/**
 * \ingroup TriCalFlowStruct
 * \details C2K testing item settings
 */
typedef struct
{
    bool b_c2k_nsft_tpc;  /**< NSFT TX performance test */
    bool b_c2k_nsft_fer;  /**< NSFT RX FER test */
    bool b_c2k_nsft_list_mode; /**< enable list mode or not */
    bool b_c2k_evdo_nsft;      /**< NSFT EVDO performance test */
} S_METACalibrationLibrary_C2K_NSFT_ITEM_T;
#endif //#if defined(__META_C2K__)
#if !defined(__NO_WCN_TEST__)
/**
 * \ingroup TriCalFlowStruct
 * \details WCN testing item settings
 */
typedef struct
{
    bool b_wcn_bt_test;   /**< Bluetooth test */
    bool b_wcn_wifi_test; /**< WIFI test */
    bool b_wcn_gps_test;  /**< GPS test */
    bool b_wcn_wifi_cal;  /**< WIFI TX power cal */
    bool b_wcn_bt_cal;  /**< Bluetooth TX power cal */
    bool b_wcn_bt_crystal_trim;  /**< Bluetooth crystal trim */
    bool b_wcn_wifi_crystal_trim;  /**< WIFI crystal trim */
    bool b_wcn_wifi_hw_limit_cal;  /**< WIFI TX power HW limit cal */
    bool b_wcn_gps_cal;/**< GPS CO-TMS cal */
#if defined(__WCN_FM_SUPPORT__)
    bool b_wcn_fm_test;   /**< FM test */
#endif
} S_METACalibrationLibrary_WCN_TEST_ITEM_T;
#endif

/**
 * \ingroup TriCalFlowStruct
 * \details Self calibratoin item settings
 */
typedef struct
{
    bool b_pre_self_cal_fr1;          /**< Pre FR1 self calibration */
    bool b_pre_self_cal_fr2;          /**< Pre FR2 self calibration */
    bool b_pre_self_cal_fr2_aif;      /**< Pre FR2 AIF self calibration */
    bool b_pre_self_cal_fr2_arf;      /**< Pre FR2 ARF self calibration */
    bool b_post_self_cal_fr1;         /**< Post FR1 self calibration */
    bool b_gge_pre_self_cal;          /**< Pre GGE self calibraiton*/
    bool b_gge_post_self_cal;         /**< Post GGE self calibraiton*/
    bool b_wcdma_pre_self_cal;        /**< Pre WCDMA self calibraiton*/
    bool b_wcdma_post_self_cal;       /**< Post WCDMA self calibraiton*/
    bool b_tdscdma_pre_self_cal;      /**< Pre TDSCDMA self calibraiton*/
    bool b_tdscdma_post_self_cal;     /**< Post TDSCDMA self calibraiton*/
    bool b_c2k_pre_self_cal;          /**< Pre C2K self calibraiton*/
    bool b_c2k_post_self_cal;         /**< Post C2K self calibraiton*/
    bool b_lte_pre_self_cal;          /**< Pre LTE self calibraiton*/
    bool b_lte_post_self_cal;         /**< Post LTE self calibraiton*/
} S_METACalibrationLibrary_SELF_CAL_ITEM_T;

/**
 * \ingroup TriCalFlowStruct
 * \details Co-TMS calibraiton item settings
 */
typedef struct
{
    bool b_gge_cotms_cal;     /**< GSM co-TMS calibraiton */
    bool b_lte_cotms_cal;     /**< LTE co-TMS calibraiton */
    bool b_nr_cotms_cal;      /**< NR co-TMS calibraiton */
} S_METACalibrationLibrary_COTMS_CAL_ITEM_T;
/**
 * \ingroup TriTuningFlowStruct
 * \details DPD path delay srarch item settings
 */
typedef struct
{
    bool b_lte_dpd_path_delay_search;   /**< LTE DPD path delay search */
    bool b_wcdma_dpd_path_delay_search; /**< WCDMA DPD path delay search */
    bool b_c2k_dpd_path_delay_search;   /**< C2K DPD path delay search */
} S_METACalibrationLibrary_DPD_DELAY_SEARCH_LAB_ITEM_T;
/**
 * \ingroup TriTuningFlowStruct
 * \details T-Put item settings
 */
typedef struct
{
    bool b_wcdma_tput_tuning;    /**< LTE DPD T-Put tuning */
} S_METACalibrationLibrary_TPUT_TUNING_ITEM_T;
/**
 * \ingroup TriTuningFlowStruct
 * \details Crystal AFC sweep item settings
 */
typedef struct
{
    bool b_gsm_crystal_afc_sweep; /**< GSM crystal AFC sweep flow */
    bool b_lte_crystal_afc_sweep; /**< LTE crystal AFC sweep flow */
} S_METACalibrationLibrary_CRYSTAL_AFC_SWEEP_LAB_ITEM_T;
/**
 * \ingroup TriTuningFlowStruct
 * \details Saturate and noise floor tuning item settings
 */
typedef struct
{
    bool b_gsm_saturate_and_noise_floor; /**< GSM saturate and noise floor */
} S_METACalibrationLibrary_SATURATE_AND_NOISE_FLOOR_LAB_ITEM_T;
/**
 * \ingroup TriTuningFlowStruct
 * \details Triggering the tuning flow request parameters
 */
typedef struct
{
    ///add by yanxuqian on 20150910
    int nThreadNumber; /**< the thread number for multi-thread scenario */
    char *cfg_path;    /**< configuration file path (*.cfg) */
    char *ini_path;    /**< initialization file path (*.ini) */
    char *log_path;    /**< calibration log path (*.log) */
    char *output_path; /**< calibration data output path (*.cal) */
    char *cal_result_path;              /**< calibration report output folder ( for *.csv) */
    char *sp_nvram_database_path;       /**< smart phone NVRAM database path */
    char *md_nvram_database_path;       /**< MD NVRAM database path */
    bool  resetTester;                  /**< reset the instrument or not */
    int   i_tester_rf_port;             /**< RF port of instrument */
    bool  resetPSU;                     /**< reset the power supply or not */
    int   i_psu_device_type;            /**< power supply instrument type */
    int   i_device_type;                /**< the GSM/GPRS/EDGE instrument type */
    int   i_device_type_wcdma;          /**< the WCDMA instrument type */
    int   i_device_type_tdscdma;        /**< the TDSCDMA instrument type */
#if defined(__META_LTE__)
    int   i_device_type_lte;            /**< the LTE instrument type */
    bool  b_lte_et_gc_delay_search;     /**< LTE ET GC delay search */
    bool  b_lte_et_bias_tuning;         /**< LTE ET bias delay search */
    bool  b_lte_apt_bias_tuning;        /**< LTE APT bias delay search */
    bool  b_lte_dpd_bias_tuning;        /**< LTE DPD bias delay search */
    bool  b_lte_et_gc_search;           /**< LTE ET GC search */
    bool  b_lte_et_path_delay_search;   /**< LTE ET path delay search */
    bool  b_lte_et_rf_gain_index_search;/**< LTE ET RF gain index search */
#endif //#if defined(__META_LTE__)   
    S_METACalibrationLibrary_DPD_DELAY_SEARCH_LAB_ITEM_T dpdPathDelaySearchItems; /**< DPD delay search items settings */
    S_METACalibrationLibrary_TPUT_TUNING_ITEM_T          tPutTuningItems;         /**< T-Put items settings */
    S_METACalibrationLibrary_CRYSTAL_AFC_SWEEP_LAB_ITEM_T           crystalAfcSweep;       /**< crystal AFC sweep item settings */
    S_METACalibrationLibrary_SATURATE_AND_NOISE_FLOOR_LAB_ITEM_T    saturateAndNoiseFloor; /**< saturate and noise floor tuning item settings */
} METACalibrationLibrary_LabTuning_COMMON_CFG_T;
/**
 * \ingroup TriTuningFlowStruct
 * \details Triggering the tuning flow request parameters
 */
typedef struct
{
    ///add by yanxuqian on 20150910
    int nThreadNumber;          /**< the thread number for multi-thread scenario */
    const wchar_t *cfg_path;    /**< configuration file path (*.cfg) */
    const wchar_t *ini_path;    /**< initialization file path (*.ini) */
    const wchar_t *log_path;    /**< calibration log path (*.log) */
    const wchar_t *output_path; /**< calibration data output path (*.cal) */
    const wchar_t *cal_result_path;        /**< calibration report output folder ( for *.csv) */
    const wchar_t *sp_nvram_database_path; /**< smart phone NVRAM database path */
    const wchar_t *md_nvram_database_path; /**< MD NVRAM database path */
    bool  resetTester;                     /**< reset the instrument or not */
    int   i_tester_rf_port;                /**< RF port of instrument */
    bool  resetPSU;                        /**< reset the power supply or not */
    int   i_psu_device_type;               /**< power supply instrument type */
    int   i_device_type;                   /**< the GSM/GPRS/EDGE instrument type */
    int   i_device_type_wcdma;             /**< the WCDMA instrument type */
    int   i_device_type_tdscdma;           /**< the TDSCDMA instrument type */
#if defined(__META_LTE__)
    int   i_device_type_lte;               /**< the LTE instrument type */
    bool  b_lte_et_gc_delay_search;        /**< LTE ET GC delay search */
    bool  b_lte_et_bias_tuning;            /**< LTE ET bias delay search */
    bool  b_lte_apt_bias_tuning;           /**< LTE APT bias delay search */
    bool  b_lte_dpd_bias_tuning;           /**< LTE DPD bias delay search */
    bool  b_lte_et_gc_search;              /**< LTE ET GC search */
    bool  b_lte_et_path_delay_search;      /**< LTE ET path_delay search */
    bool  b_lte_et_rf_gain_index_search;   /**< LTE ET RF gain index search */
#endif //#if defined(__META_LTE__)   
    S_METACalibrationLibrary_DPD_DELAY_SEARCH_LAB_ITEM_T dpdPathDelaySearchItems; /**< DPD delay search items settings */
    S_METACalibrationLibrary_TPUT_TUNING_ITEM_T          tPutTuningItems;         /**< T-Put items settings */
    S_METACalibrationLibrary_CRYSTAL_AFC_SWEEP_LAB_ITEM_T           crystalAfcSweep;       /**< crystal AFC sweep item settings */
    S_METACalibrationLibrary_SATURATE_AND_NOISE_FLOOR_LAB_ITEM_T    saturateAndNoiseFloor; /**< saturate and noise floor tuning item settings */
    bool  b_nr_et_gc_search;              /**< NR ET GC search */
    bool  b_nr_et_path_delay_search;      /**< NR ET path_delay search */
    bool  b_nr_et_bias_tuning;            /**< NR ET pa bias tuning */
    bool  b_multiport_tuning;             /**< LTE/NR multiport tuning */
    bool  b_nr_dpd_bias_tuning;           /**< NR DPD pa bias tuning */
    bool  b_et_lut_subband_reduction;     /**< ET LUT subband reduction */
    bool  b_et_lte_initial_group_search;  /**< LTE ET smart char initial group search */
    bool  b_et_lte_pre_gc_search;         /**< LTE ET smart char pre GC search */
    bool  b_et_lte_etm_bias_search;       /**< LTE ET smart char ETM bias search */
    bool  b_et_lte_pa_bias_search;        /**< LTE ET smart char PA bias search */
    bool  b_et_lte_gc_search;             /**< LTE ET smart char GC search */
    bool  b_et_lte_final_group_search;    /**< LTE ET smart char final group search */
    bool  b_et_lte_vpa_lut_temp_comp_ht;    /**< LTE ET VPA LUT temperature compensation for HT */
    bool  b_et_lte_vpa_lut_temp_comp_lt;    /**< LTE ET VPA LUT temperature compensation for LT */
    bool  b_et_lte_factory_gc_search;     /**< LTE ET smart char factory GC search */
    bool  b_et_lte_gain_dispersion_and_peaking_calculation;     /**< LTE ET Gain Dispersion and Peaking Calculation */
    bool  b_et_nr_initial_group_search;   /**< NR ET smart char initial group search */
    bool  b_et_nr_pre_gc_search;          /**< NR ET smart char pre GC search */
    bool  b_et_nr_etm_bias_search;        /**< NR ET smart char ETM bias search */
    bool  b_et_nr_pa_bias_search;         /**< NR ET smart char PA bias search */
    bool  b_et_nr_gc_search;              /**< NR ET smart char GC search */
    bool  b_et_nr_final_group_search;     /**< NR ET smart char final group search */
    bool  b_et_nr_vpa_lut_temp_comp_ht;    /**< NR ET VPA LUT temperature compensation for HT */
    bool  b_et_nr_vpa_lut_temp_comp_lt;    /**< NR ET VPA LUT temperature compensation for LT */
    bool  b_et_nr_vpa_lut_temp_comp_nt;    /**< NR ET VPA LUT temperature compensation for NT */
    bool  b_et_nr_factory_gc_search;      /**< NR ET smart char factory GC search */
    bool  b_et_nr_gain_dispersion_and_peaking_calculation;     /**< NR ET Gain Dispersion and Peaking Calculation */
    bool  b_et_nr_bpsk_cheq_comp_nt;  /**< NR ET BPSK CHEQ compensation for NT */
    bool  b_et_nr_bpsk_cheq_comp_ht;  /**< NR ET BPSK CHEQ compensation for HT */
    bool  b_et_nr_bpsk_cheq_comp_lt;  /**< NR ET BPSK CHEQ compensation for LT */
    bool  b_lte_dpd_smart_char_check;     /**< LTE DPD SMART CHAR functionality check */
    bool  b_lte_dpd_smart_char_pa_bias_search;  /**< LTE DPD SMART CHAR PA bias search */
    bool  b_lte_dpd_smart_char_vpa_search;      /**< LTE DPD SMART CHAR VPA search */
    bool  b_lte_dpd_smart_char_initial_dpd_lut;      /**< LTE DPD SMART CHAR Initial DPD LUT */
    bool  b_lte_dpd_smart_char_cheq_temp_comp_nt;    /**< LTE DPD CHEQ temperature compensation for NT */
    bool  b_lte_dpd_smart_char_cheq_temp_comp_ht;    /**< LTE DPD CHEQ temperature compensation for HT */
    bool  b_lte_dpd_smart_char_cheq_temp_comp_lt;    /**< LTE DPD CHEQ temperature compensation for LT */
    bool  b_nr_dpd_smart_char_check;      /**< NR DPD SMART CHAR functionality check */
    bool  b_nr_dpd_smart_char_pa_bias_search;   /**< NR DPD SMART CHAR PA bias search */
    bool  b_nr_dpd_smart_char_vpa_search;       /**< NR DPD SMART CHAR VPA search */
    bool  b_nr_dpd_smart_char_initial_dpd_lut;      /**< NR DPD SMART CHAR Initial DPD LUT */
    bool  b_nr_dpd_smart_char_cheq_temp_comp_nt;    /**< NR DPD CHEQ temperature compensation for NT */
    bool  b_nr_dpd_smart_char_cheq_temp_comp_ht;    /**< NR DPD CHEQ temperature compensation for HT */
    bool  b_nr_dpd_smart_char_cheq_temp_comp_lt;    /**< NR DPD CHEQ temperature compensation for LT */
    bool  b_lte_et_functionality_check;     /**< LTE ET functionality check */
    bool  b_nr_et_functionality_check;      /**< NR ET functionality check */
    bool  b_lte_apt_smart_char_pa_bias_search;  /**< LTE APT SMART CHAR PA bias search */
    bool  b_lte_apt_smart_char_vpa_search;      /**< LTE APT SMART CHAR VPA search */
    bool  b_nr_apt_smart_char_pa_bias_search;   /**< NR APT SMART CHAR PA bias search */
    bool  b_nr_apt_smart_char_vpa_search;       /**< NR APT SMART CHAR VPA search */
    bool  b_lte_apt_pa_gain_slope_cal;              /**< Calculate the PA Gain for LTE APT*/
    bool  b_lte_dpd_pa_gain_slope_cal;              /**< Calculate the PA Gain for LTE DPD*/
    bool  b_lte_tas_pa_gain_slope_cal;              /**< Calculate the PA Gain for LTE TAS*/
    bool  b_nr_apt_pa_gain_slope_cal;               /**< Calculate the PA Gain for NR APT*/
    bool  b_nr_dpd_pa_gain_slope_cal;               /**< Calculate the PA Gain for NR DPD*/
    bool  b_nr_tas_pa_gain_slope_cal;               /**< Calculate the PA Gain for NR TAS*/
} METACalibrationLibrary_LabTuning_COMMON_CFG_TW;

#if defined(LONG_MAX) && (LONG_MAX > 0x7FFFFFFFL)
typedef unsigned int        UInt32;
#else
typedef unsigned long       UInt32;
#endif


/**
 * \ingroup TriCalFlowStruct
 * \details Triggering the calibration flow request parameters
 */
typedef struct
{
    ///add by yanxuqian on 20150910
    int nThreadNumber;            /**< the thread number for multi-thread scenario */
    char *cfg_path;               /**< configuration file path (*.cfg) */
    char *ini_path;               /**< initialization file path (*.ini) */
    char *log_path;               /**< calibration log path (*.log) */
    char *output_path;            /**< calibration data output path (*.cal) */
    char *cal_result_path;        /**< calibration report output folder ( for *.csv) */
    char *sp_nvram_database_path; /**< smart phone NVRAM database path */
    char *md_nvram_database_path; /**< MD NVRAM database path */
    char *golden_data_ini_path;   /**< golden calibration data output path (*.ini) */
    int   i_device_type;          /**< the GSM/GPRS/EDGE instrument type */
    int   i_device_type_wcdma;    /**< the WCDMA instrument type */
    int   i_device_type_tdscdma;  /**< the TDSCDMA instrument type */
#if defined(__META_LTE__)
    int   i_device_type_lte;      /**< the LTE instrument type */
#endif //#if defined(__META_LTE__)
    int   i_device_type_nr;       /**< the NR instrument type */
    int   i_device_type_nr_fr2;       /**< the NR FR2 instrument type */
    S_METACalibrationLibrary_NR_CAL_ITEM_T       nrCalibrationItems;       /**< NR calibratoin item settings */
    S_METACalibrationLibrary_NR_PRETEST_ITEM_T   nrPreTestItems;           /**< NR PreTest item settings */
    S_METACalibrationLibrary_NR_NSFT_ITEM_T      nrNsftItems;              /**< NR testing item settings */
    S_METACalibrationLibrary_SELF_CAL_ITEM_T     selfCalibrationItems;     /**< Self calibratoin item settings */
    S_METACalibrationLibrary_NR_FR2_NSFT_ITEM_T  nrFr2NsftItems;           /**< NR FR2 testing item settings */
#if defined(__META_C2K__)
    int   i_device_type_c2k;      /**< the C2K instrument type */
#endif //#if defined(__META_C2K__)
#if !defined(__NO_WCN_TEST__)
    int   i_device_type_wcn_bt;   /**< the Bluetooth instrument type */
    int   i_device_type_wcn_wifi; /**< the WIFI instrument type */
    int   i_device_type_wcn_gps;  /**< the GPS instrument type */
#endif
    int   i_device_type_iot_ntn;      /**< the IoT-NTN instrument type */
    bool  resetTester;            /**< reset the instrument or not */
    int   i_tester_rf_port;       /**< RF port of instrument */
    S_METACalibrationLibrary_GGE_CAL_ITEM_T      ggeCalibrationItems;       /**< GSM/GPRS/EDGE calibratoin item settings */
    S_METACalibrationLibrary_GGE_NSFT_ITEM_T     ggeNsftItems;              /**< GSM/GPRS/EDGE testing item settings */
    S_METACalibrationLibrary_WCDMA_CAL_ITEM_T    wcdmaCalibrationItems;     /**< WCDMA calibratoin item settings */
    S_METACalibrationLibrary_WCDMA_NSFT_ITEM_T   wcdmaNsftItems;            /**< WCDMA/GPRS/EDGE testing item settings (R99: TPC/BER/PRACH, R5, R6) */
    S_METACalibrationLibrary_TD_CAL_ITEM_T       tdscdmaCalibrationItems;   /**< TDSCDMA calibratoin item settings */
    S_METACalibrationLibrary_TD_NSFT_ITEM_T      tdscdmaNsftItems;          /**< TDSCDMA testing item settings */
#if defined(__META_LTE__)
    S_METACalibrationLibrary_LTE_CAL_ITEM_T      lteCalibrationItems;        /**< LTE calibratoin item settings */
    S_METACalibrationLibrary_LTE_PRETEST_ITEM_T  ltePreTestItems;            /**< LTE PreTest item settings */
    S_METACalibrationLibrary_LTE_NSFT_ITEM_T     lteNsftItems;               /**< LTE testing item settings */
#endif //#if defined(__META_LTE__)
#if defined(__META_C2K__)
    S_METACalibrationLibrary_C2K_CAL_ITEM_T      c2kCalibrationItems;        /**< C2K calibratoin item settings */
    S_METACalibrationLibrary_C2K_NSFT_ITEM_T     c2kNsftItems;               /**< C2K testing item settings */
#endif //#if defined(__META_C2K__)
#if !defined(__NO_WCN_TEST__)
    S_METACalibrationLibrary_WCN_TEST_ITEM_T     wcnTestItems;               /**< WCN testing item settings */
#endif
    S_METACalibrationLibrary_TAS_TEST_ITEM_T     tasTestItems;               /**< TAS testing item settings */
    S_METACalibrationLibrary_Others_ITEM_T       othersCalibrationItems;     /**< Others calibratoin item settings */
    S_METACalibrationLibrary_COTMS_CAL_ITEM_T    coTmsCalibrationItems;      /**<  Co-TMS calibration item settings NOTICE: Only for new entry (METACalibrationLibrary_StartEx) */
    S_METACalibrationLibrary_CDDC_TEST_ITEM_T    cddcTestItems;               /**< CDDC testing item settings */
    S_METACalibrationLibrary_TRANSCEIVER_POWER_TEST_ITEM_T    transceiverPowerTestItems;               /**< Transceiver Power testing item settings */
    S_METACalibrationLibrary_IOT_NTN_CAL_ITEM_T      iotNtnCalibrationItems;         /**< IoT-NTN calibratoin item settings */
    S_METACalibrationLibrary_IOT_NTN_NSFT_ITEM_T     iotNtnNsftItems;                /**< IoT-NTN testing item settings */
    S_METACalibrationLibrary_RFFE_CHECK_TEST_ITEM_T rffeCheckTestItems;        /**< RFFE Scan testing item settings */
    S_METACalibrationLibrary_OSL_CAL_ITEM_T      oslCalibrationItems;         /**< OSL calibratoin item settings */
} METACalibrationLibrary_COMMON_CFG_T;
/**
 * \ingroup TriCalFlowStruct
 * \details Triggering the calibration flow request parameters
 */
typedef struct
{
    //add by yanxuqian on 20150910
    int nThreadNumber;          /**< the thread number for multi-thread scenario */
    const wchar_t *cfg_path;    /**< configuration file path (*.cfg) */
    const wchar_t *ini_path;    /**< initialization file path (*.ini) */
    const wchar_t *log_path;    /**< calibration log path (*.log) */
    const wchar_t *output_path; /**< calibration data output path (*.cal) */
    const wchar_t *cal_result_path;        /**< calibration report output folder ( for *.csv) */
    const wchar_t *sp_nvram_database_path; /**< smart phone NVRAM database path */
    const wchar_t *md_nvram_database_path; /**< MD NVRAM database path */
    const wchar_t *golden_data_ini_path;   /**< golden calibration data output path (*.ini) */
    int   i_device_type;          /**< the GSM/GPRS/EDGE instrument type */
    int   i_device_type_wcdma;    /**< the WCDMA instrument type */
    int   i_device_type_tdscdma;  /**< the TDSCDMA instrument type */
#if defined(__META_LTE__)
    int   i_device_type_lte;      /**< the LTE instrument type */
#endif //#if defined(__META_LTE__)
    int   i_device_type_nr;       /**< the NR instrument type */
    int   i_device_type_nr_fr2;       /**< the NR FR2 instrument type */
    S_METACalibrationLibrary_NR_CAL_ITEM_T       nrCalibrationItems;       /**< NR calibratoin item settings */
    S_METACalibrationLibrary_NR_PRETEST_ITEM_T   nrPreTestItems;           /**< NR PreTest item settings */
    S_METACalibrationLibrary_NR_NSFT_ITEM_T      nrNsftItems;              /**< NR testing item settings */
    S_METACalibrationLibrary_SELF_CAL_ITEM_T     selfCalibrationItems;     /**< Self calibratoin item settings */
    S_METACalibrationLibrary_NR_FR2_NSFT_ITEM_T  nrFr2NsftItems;           /**< NR FR2 testing item settings */
#if defined(__META_C2K__)
    int   i_device_type_c2k;      /**< the C2K instrument type */
#endif //#if defined(__META_C2K__)
#if !defined(__NO_WCN_TEST__)
    int   i_device_type_wcn_bt;   /**< the Bluetooth instrument type */
    int   i_device_type_wcn_wifi; /**< the WIFI instrument type */
    int   i_device_type_wcn_gps;  /**< the GPS instrument type */
#endif
    int   i_device_type_iot_ntn;      /**< the IoT-NTN instrument type */
    bool  resetTester;           /**< reset the instrument or not */
    int   i_tester_rf_port;      /**< RF port of instrument */
    S_METACalibrationLibrary_GGE_CAL_ITEM_T      ggeCalibrationItems;        /**< GSM/GPRS/EDGE calibratoin item settings */
    S_METACalibrationLibrary_GGE_NSFT_ITEM_T     ggeNsftItems;               /**< GSM/GPRS/EDGE testing item settings */
    S_METACalibrationLibrary_WCDMA_CAL_ITEM_T    wcdmaCalibrationItems;      /**< WCDMA calibratoin item settings */
    S_METACalibrationLibrary_WCDMA_NSFT_ITEM_T   wcdmaNsftItems;             /**< WCDMA/GPRS/EDGE testing item settings (R99: TPC/BER/PRACH, R5, R6) */
    S_METACalibrationLibrary_TD_CAL_ITEM_T       tdscdmaCalibrationItems;    /**< TDSCDMA calibratoin item settings */
    S_METACalibrationLibrary_TD_NSFT_ITEM_T      tdscdmaNsftItems;           /**< TDSCDMA testing item settings */
#if defined(__META_LTE__)
    S_METACalibrationLibrary_LTE_CAL_ITEM_T      lteCalibrationItems;        /**< LTE calibratoin item settings */
    S_METACalibrationLibrary_LTE_PRETEST_ITEM_T  ltePreTestItems;            /**< LTE PreTest item settings */
    S_METACalibrationLibrary_LTE_NSFT_ITEM_T     lteNsftItems;               /**< LTE testing item settings */
#endif //#if defined(__META_LTE__)
#if defined(__META_C2K__)
    S_METACalibrationLibrary_C2K_CAL_ITEM_T      c2kCalibrationItems;        /**< C2K calibratoin item settings */
    S_METACalibrationLibrary_C2K_NSFT_ITEM_T     c2kNsftItems;               /**< C2K testing item settings */
#endif //#if defined(__META_C2K__)
#if !defined(__NO_WCN_TEST__)
    S_METACalibrationLibrary_WCN_TEST_ITEM_T     wcnTestItems;               /**< WCN testing item settings */
#endif
    S_METACalibrationLibrary_TAS_TEST_ITEM_T     tasTestItems;               /**< TAS testing item settings */
    S_METACalibrationLibrary_Others_ITEM_T       othersCalibrationItems;     /**< Others calibratoin item settings */
    S_METACalibrationLibrary_COTMS_CAL_ITEM_T    coTmsCalibrationItems;      /**<  Co-TMS calibration item settings NOTICE: Only for new entry (METACalibrationLibrary_StartEx) */
    S_METACalibrationLibrary_CDDC_TEST_ITEM_T    cddcTestItems;               /**< CDDC testing item settings */
    S_METACalibrationLibrary_TRANSCEIVER_POWER_TEST_ITEM_T    transceiverPowerTestItems;               /**< Transceiver Power testing item settings */
    S_METACalibrationLibrary_IOT_NTN_CAL_ITEM_T      iotNtnCalibrationItems;         /**< IoT-NTN calibratoin item settings */
    S_METACalibrationLibrary_IOT_NTN_NSFT_ITEM_T     iotNtnNsftItems;                /**< IoT-NTN testing item settings */
    S_METACalibrationLibrary_RFFE_CHECK_TEST_ITEM_T rffeCheckTestItems;        /**< RFFE Scan testing item settings */
    S_METACalibrationLibrary_OSL_CAL_ITEM_T      oslCalibrationItems;         /**< OSL calibratoin item settings */
} METACalibrationLibrary_COMMON_CFG_TW;

//add by yanxuqian on 2015-06-08
/**
 * \ingroup TriCalFlowStruct
 * \details the callback function parameters for multi-thread calibration runtime status
 */
typedef struct
{
    void *pObject;   /**< the upper layer instance (user-defined) */
    char *strLogBuf;                          /**< log information */
} S_CalibrationLibraryCallBackParameter_T;

/**
 * \ingroup TriCalFlowStruct
 * \details the test criteria of test by freqency apis.
 */
typedef struct
{
    E_RAT_INDEX_T     testRat;          /**< Test RAT enum value, in one turn all rat should be the same. */
    unsigned short    testBand;         /**< Test band enum value, ref to E_BAND_INDEX_T */
    unsigned int      testChannelFreq;  /**< Uplink channel or freq, for G/W/T/C is channel, for LTE unit is 100KHz, for NR unit is 1KHz. */
} METACalibrationLibrary_TEST_FREQUENCY;

/**
 * \ingroup General
 * \details The test item(conditon/context) of one test result.
 */
typedef struct
{
    unsigned int testcase;      /**< Test case index. Max value of uint mean unused. */
    char rat[64];               /**< RAT string, GSM, WCDMA, TDSCDMA, C2K, LTE, NR. Empty string mean unused. */
    unsigned int band;          /**< Band number, GSM: 850, 900 ...; TDS: 96(A), 70(F); NLWC: 1, 2, ... Max value of uint mean unused. */
    int channel;                /**< Channel number. Channel or frequency can just set one. Max value of int mean unused. */
    double freq;                /**< Frequency, unit: MHz. Positive meeans uplink, negative means downlink. Max value of double mean unused. */
    unsigned short cID;       /**< CID number. Max value of ushort mean unused. */
    char modulation[16];        /**< Modulation method name. Empty string "" mean unused. */
    double power;               /**< Power value, unit: dBm. Max value of double mean unused. */
    unsigned char powerIdx;     /**< Power index for repeated power point. Max value of uchar mean unused. */
    unsigned char carkit;       /**< Carkit number. Max value of uchar mean unused. */
    unsigned short route;       /**< Route number. Max value of ushort mean unused. */
    unsigned short path;        /**< Path, for Gen93: bypass path, filter path; Gen97: ANT path logic. Max value of ushort mean unused. */
    double bw;                  /**< Bandwidth, unit: MHz. Max value of double mean unused. */
    unsigned short rbOffset;    /**< RB offset. Max value of ushort mean unused. */
    unsigned short rbLen;       /**< RB length. Max value of ushort mean unused. */
    unsigned char scs;          /**< SCS, unit: KHz. Max value of uchar mean unused. */
    unsigned char tasState;     /**< TAS state index. Max value of uchar mean unused. */
    double rxDesenseTxPower; /**< Desense Tx Power of Sensitivity test. Max value of double mean unused. */
    char item[128];              /**< Test spec name or additional information, Power, ACLR, .etc */
} S_CalibrationLibrary_ReportItem;

/**
 * \ingroup General
 * \details The test item(conditon/context) of one test result.
 */
typedef struct
{
    char fhcCase[128];          /**< FHC Case string. Empty string mean unused. */
    unsigned short calIndex;    /**< Cal Index number. Max value of ushort mean unused. */
    unsigned short bwIndex;          /**< Bandwidth Index number. Max value of ushort mean unused. */
    unsigned char isAnchor;     /**< Is Rx anchor case. Bool value, 0: no; other: yes */
    unsigned short powerStep;   /**< Power Step number. Max value of uchar mean unused. */
    unsigned short fe;          /**< FE index number. Max value of ushort mean unused. */
    char sx[16];                /**< SX group string. Empty string mean unused. */
    char carkitGroup[16];       /**< carkit group string. Empty string mean unused. */
    unsigned short antenna;     /**< Antenna index number. Max value of ushort mean unused. */
    unsigned short rxT2Path;    /**< Rx Type2 partial path number. Max value of ushort mean unused. */
    unsigned short txPowerMode; /**< Tx Power Mode number. Max value of ushort mean unused. */
    unsigned short rxPowerMode; /**< Rx Power Mode number. Max value of ushort mean unused. */
} S_CalibrationLibrary_ReportItem_V2;

/**
 * \ingroup General
 * \details The callback function parameters for test result report
 */
typedef struct _S_CalibrationLibrary_ReportInfo
{
    S_CalibrationLibrary_ReportItem item; /**< Structured item */
    char name[512];     /**< Formated item string */
    double value;       /**< Result value */
    double limitL;      /**< Check limit. Max value of double mean unused. */
    double limitU;      /**< Check limit. Max value of double mean unused. */
    char unit[16];      /**< Unit string */
    unsigned char pass; /**< Check result. Bool value, 0: fail; other: pass */
} S_CalibrationLibrary_ReportInfo;

/**
 * \ingroup General
 * \details The callback function parameters for test result report extended
 */
typedef struct _S_CalibrationLibrary_ReportInfo_Ex : public S_CalibrationLibrary_ReportInfo
{
    unsigned char ver;  /**< Report Info extended version */
    S_CalibrationLibrary_ReportItem_V2 item_ex; /**< Structured extended item */
} S_CalibrationLibrary_ReportInfo_Ex;

/**
 * \ingroup TriCalFlowStruct
 * \details the callback function for multi-thread calibration log
 */
typedef void (__stdcall *METACalibrationLibrary_Log_Display_CallBackForMultiThread)(const char *logBuf, void *pThreadHandle);
/**
 * \ingroup TriCalFlowStruct
 * \details the callback function for calibration log
 */
typedef void (__stdcall *METACalibrationLibrary_Log_Display_CallBack)(const char *logBuf);
/**
 * \ingroup TriCalFlowStruct
 * \details the callback function for multi-thread calibration runtime status
 */
typedef void (__stdcall *METACalibrationLibrary_STATUS_CallBackForMultiThread)(S_CalibrationLibraryCallBackParameter_T *pParam);
/**
 * \ingroup TriCalFlowStruct
 * \details the callback function for test report data
 */
typedef void (__stdcall *METACalibrationLibrary_Report_CallBack)(void *userdata, const S_CalibrationLibrary_ReportInfo *info);
/**
 * \ingroup General
 * \details Register callback function for multi-thread usage to retrieve runtime log
 *
 * \param [in] meta_handle target context
 * \param [in] cb the callback for the runtime log
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_RegisterCallBackForMultiThread(const int meta_handle, const METACalibrationLibrary_Log_Display_CallBackForMultiThread cb);

/**
 * \ingroup General
 * \details the callback function for overall logs integration.
 */
typedef void (__stdcall *METACalibrationLibrary_LogIntegration_CallBack)(const S_METACalibrationLibrary_LOG_T logEntity);

/**
 * \ingroup TriCalFlowStruct
 * \details the callback function parameters for switch carkit handler
 */
typedef struct
{
    char carkit;
    unsigned char antType; // TX, RXM, RXD
    unsigned char instrumentPort;
} S_CalibrationLibraryCarkitCallBackParam_T;

/**
 * \ingroup TriCalFlowStruct
 * \details the array of S_CalibrationLibraryCarkitCallBackParam_T
 */
typedef struct
{
    unsigned char callBackParamArraySize;
    S_CalibrationLibraryCarkitCallBackParam_T callBackParam[16];
} S_CalibrationLibraryCarkitCallBackParamArray_T;

/**
 * \ingroup TriCalFlowStruct
 * \details the callback function for switch carkit handler
 */
typedef int (__stdcall *METACalibrationLibrary_CarkitUsage_CallBack)(void *userdata, const S_CalibrationLibraryCarkitCallBackParam_T *pCarkitInfo);

/**
 * \ingroup General
 * \details Register callback function to handle carkit switch event
 *
 * \param [in] meta_handle target context
 * \param [in] userdata the parameter of the callback function
 * \param [in] cb the callback function
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_RegisterCallBack_SwtichCarkitHandler(const int meta_handle, void *userdata, const METACalibrationLibrary_CarkitUsage_CallBack cb);

/**
* \ingroup General
* \details Register callback function for runtime status(progress or error code)
*
* \param [in] meta_handle target context
* \param [in] pObject the pointer provided by invoker, which will be pass in callback function directly to access invoker's data.
* \param [in] cb the callback function pointer which be used to receive status information
*
* \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
* \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
*/
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StatusCallBackRegister(const int meta_handle, void *object,
                                                                                        const META_Status_CallBack cb);
/**
 * \ingroup General
 * \details Register callback function to retrieve runtime log
 * \sa For multi-thread case, METACalibrationLibrary_RegisterCallBackForMultiThread
 *
 * \param [in] meta_handle target context
 * \param [in] cb the callback for the runtime log
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * Example Usage:
 * \code
 *    static void stdcall SharelibCB(const char* msg)
 *    {
 *          printf("%s\n", msg);
 *    }
 *
 *    void main()
 *    {
 *          int metaHandle = 1; // you can fetch META handle from exported function in METACore
 *        METACalibrationLibrary_Init(metaHandle);
 *        METACalibrationLibrary_RegisterCallBack(metaHandle, SharelibCB)
 *        //...
 *        METACalibrationLibrary_DeInit(metaHandle);
 *    }
 *
 * \endcode
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_RegisterCallBack(const int meta_handle, const METACalibrationLibrary_Log_Display_CallBack cb);
/**
 * \ingroup General
 * \details Register callback function for test report
 *
 * \param [in] meta_handle target context
 * \param [in] userdata the pointer provided by invoker which will be pass in callback function directly to access invoker's data.
 * \param [in] cb the callback function pointer which be used to receive report information
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_RegisterCallBack_Report(const int meta_handle, void *userdata, const METACalibrationLibrary_Report_CallBack cb);
/**
 * \ingroup General
 * \details Register callback function for overall logs
 *
 * \param [in] meta_handle target context
 * \param [in] cb the callback function pointer which be used to receive overall logs
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_RegisterCallBack_LogIntegration(const int meta_handle, const METACalibrationLibrary_LogIntegration_CallBack cb);
/**
 * \ingroup General
 * \details Initialize the META share library context variable.
 *
 * \param [in] meta_handle target context
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * \note you must call initialization function first before calling any other calibration functions.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_Init(const int meta_handle);
/**
 * \ingroup General
 * \details Clear up the META share library context variable.
 *
 * \param [in] meta_handle target context
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_DeInit(const int meta_handle);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META calibration and testing flow.
 * The status is noticed asynchronously by callback functions.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_StartW
 * \sa Support the multi-thread functions, METACalibrationLibrary_StartForMultiThread
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_Start(const int meta_handle,
                                                                       const METACalibrationLibrary_COMMON_CFG_T *cfg,
                                                                       int *pStopFlag);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META calibration and testing flow with wide character format supported.
 * The status is noticed asynchronously by callback functions.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameter
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartW(const int meta_handle,
                                                                        const METACalibrationLibrary_COMMON_CFG_TW *cfg,
                                                                        int *pStopFlag);
/**
 * <pre>
 * \ingroup TriTuningFlow
 * \details Start META lab tuning flow.
 * The status is noticed asynchronously by callback functions.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_LabTuning_StartW
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the tuning items configuration parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_LabTuning_Start(const int meta_handle,
                                                                                 const METACalibrationLibrary_LabTuning_COMMON_CFG_T *cfg,
                                                                                 int *pStopFlag);
/**
 * <pre>
 * \ingroup TriTuningFlow
 * \details Start META lab tuning flow with wide character format supported.
 * The status is noticed asynchronously by callback functions.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the tuning items configuration parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_LabTuning_StartW(const int meta_handle,
                                                                                  const METACalibrationLibrary_LabTuning_COMMON_CFG_TW *cfg,
                                                                                  int *pStopFlag);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META multi-thread calibration flow.
 * The status is noticed asynchronously by callback functions.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_StartForMultiThreadW
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] cb the callback function for calibration runtime status report
 * \param [in,out] pCalibrationCallBackParam the callback function parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartForMultiThread(const int meta_handle,
                                                                                     const METACalibrationLibrary_COMMON_CFG_T *cfg,
                                                                                     const METACalibrationLibrary_STATUS_CallBackForMultiThread cb,
                                                                                     S_CalibrationLibraryCallBackParameter_T *pCalibrationCallBackParam,
                                                                                     int *pStopFlag);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META multi-thread calibration flow with wide character format supported.
 * The status is noticed asynchronously by callback functions.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] cb the callback function for calibration runtime status report
 * \param [in,out] pCalibrationCallBackParam the callback function parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartForMultiThreadW(const int meta_handle,
                                                                                      const METACalibrationLibrary_COMMON_CFG_TW *cfg,
                                                                                      const METACalibrationLibrary_STATUS_CallBackForMultiThread cb,
                                                                                      S_CalibrationLibraryCallBackParameter_T *pCalibrationCallBackParam,
                                                                                      int *pStopFlag);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META calibration flow with wide character format supported.
 * The status is noticed synchronously at the function return.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartWithRetrunedStatusCodeW(const int meta_handle,
                                                                                              const METACalibrationLibrary_COMMON_CFG_TW *cfg,
                                                                                              int *pStopFlag);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META calibration flow.
 * The status is noticed synchronously at the function return.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_StartWithRetrunedStatusCodeW
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartWithRetrunedStatusCode(const int meta_handle,
                                                                                             const METACalibrationLibrary_COMMON_CFG_T *cfg,
                                                                                             int *pStopFlag);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META multi-thread calibration flow.
 * The status is noticed synchronously at the function return.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_StartWithRetrunedStatusCodeForMultiThreadW
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] pCalibrationCallBackParam for register the upper layer context
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartWithRetrunedStatusCodeForMultiThread(const int meta_handle,
                                                                                                           const METACalibrationLibrary_COMMON_CFG_T *cfg,
                                                                                                           S_CalibrationLibraryCallBackParameter_T *pCalibrationCallBackParam,
                                                                                                           int *pStopFlag);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META multi-thread calibration flow with wide character format supported.
 * The status is noticed synchronously at the function return.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] pCalibrationCallBackParam for register the upper layer context
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartWithRetrunedStatusCodeForMultiThreadW(const int meta_handle,
                                                                                                            const METACalibrationLibrary_COMMON_CFG_TW *cfg,
                                                                                                            S_CalibrationLibraryCallBackParameter_T *pCalibrationCallBackParam,
                                                                                                            int *pStopFlag);
/**
 * <pre>
 * \ingroup General
 * \details Get the calibration Library version
 *
 * \param [in,out] major_ver the major version
 * \param [in,out] minor_ver the minor version
 * \param [in,out] build_num the build number
 * \param [in,out] patch_num patch build number
 *
 * \retval 0 successfully
 * \retval others failed
 *
 * Version description sample:
 * \code
 *
 *    // sample V10.1924.1.6
 *    major_ver: 10
 *    minor_ver: 1924
 *    build_num: 1
 *    patch_num: 6
 *
 * \endcode
 *
 * </pre>
 */
int __stdcall METACalibrationLibrary_GetDLLVer(unsigned int *major_ver, unsigned int *minor_ver, unsigned int *build_num, unsigned int *patch_num);

/**
 * <pre>
 * \ingroup General
 * \details Get full version information from METACalibrationLibrary
 *
 * \param [in]  in_version_struct_type  specifies the library version structure type, which affects how to cast `out_version_info`
 * \param [out] out_version_info        pointer to the structure where the library version information will be stored
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The given version type and version info are not matched. Failed to get version information.
 *
 * \code
 * #include "METAVersionDefinition.h"
 * void example() {
 *     METALibraryVersionInfo info;
 *     E_METACalibrationLibrary_RESULT result = METACalibrationLibrary_GetLibraryVersionInfo(META_LIBRARY_VERSION_V1, &info);
 *     if (result == E_METACalibrationLibrary_RESULT_SUCCESS) {
 *         // Read value from info
 *     }
 * }
 * \endcode
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_GetLibraryVersionInfo(int in_version_struct_type, void *out_version_info);
/**
 * <pre>
 * \ingroup General
 * \details Get the last status encode information.
 * \sa METACalibrationLibrary_GetLastStatusEncodeValue
 *
 * \param [in] meta_handle target context
 * \retval the new status encode value
 * </pre>
 */
const unsigned long long __stdcall METACalibrationLibrary_GetLastStatusEncodeValue(const int metaHandle);
/**
 * <pre>
 * \ingroup General
 * \details Get the last status information.
 * \sa METACalibrationLibrary_GetLastStatus
 *
 * \param [in] meta_handle target context
 * \retval S_META_STATUS_T the new status code and status description
 * </pre>
 */
const S_META_STATUS_T *__stdcall METACalibrationLibrary_GetLastStatus(const int metaHandle);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META multi-thread calibration flow with instrument handle assignment.
 * The status is noticed asynchronously by callback functions.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_StartWithViHandleForMultiThreadW
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] cb the callback function for calibration runtime status report
 * \param [in,out] pCalibrationCallBackParam the callback function parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 * \param [in] viHandle instrument context
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartWithViHandleForMultiThread(const int meta_handle,
                                                                                                 const METACalibrationLibrary_COMMON_CFG_T *cfg,
                                                                                                 const METACalibrationLibrary_STATUS_CallBackForMultiThread cb,
                                                                                                 S_CalibrationLibraryCallBackParameter_T *pCalibrationCallBackParam,
                                                                                                 int *pStopFlag,
                                                                                                 UInt32 viHandle);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META multi-thread calibration flow with instrument handle assignment and wide character format supported.
 * The status is noticed asynchronously by callback functions.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] cb the callback function for calibration runtime status report
 * \param [in,out] pCalibrationCallBackParam the callback function parameters
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 * \param [in] viHandle instrument context
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartWithViHandleForMultiThreadW(const int meta_handle,
                                                                                                  const METACalibrationLibrary_COMMON_CFG_TW *cfg,
                                                                                                  const METACalibrationLibrary_STATUS_CallBackForMultiThread cb,
                                                                                                  S_CalibrationLibraryCallBackParameter_T *pCalibrationCallBackParam,
                                                                                                  int *pStopFlag,
                                                                                                  UInt32 viHandle);

/**
 * \ingroup General
 * \details Initialize the test with frequency related META share library context variable.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg config file and other log files path, instrument type, all test item will be needed for init instrument
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * \note you must call initialization function first before calling any other calibration functions.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_Init(const int meta_handle, const METACalibrationLibrary_COMMON_CFG_T *cfg, int *pStopFlag);
/**
 * \ingroup General
 * \details Initialize the test with frequency related META share library context variable.
 * \sa Support the wide character format (unicode character set) of input parameter.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg config file and other log files path, instrument type, all test item will be needed for init instrument
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * \note you must call initialization function first before calling any other calibration functions.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_InitW(const int meta_handle, const METACalibrationLibrary_COMMON_CFG_TW *cfg, int *pStopFlag);
/**
 * \ingroup General
 * \details Initialize the test with frequency related META share library context variable.
 * \sa Support the wide character format (unicode character set) of input parameter.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg config file and other log files path, instrument type, all test item will be needed for init instrument
 * \param [in] userData customization callback identifier
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * \note you must call initialization function first before calling any other calibration functions.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_InitMetaHandleW(const int meta_handle, const METACalibrationLibrary_COMMON_CFG_TW *cfg, void *userData);
/**
 * \ingroup General
 * \details Initialize the test with frequency related META share library context variable.
 * \sa Support the wide character format (unicode character set) of input parameter.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg config file and other log files path, instrument type, all test item will be needed for init instrument
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * \note you must call initialization function first before calling any other calibration functions.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_InitCFGAndRCTW(const int meta_handle, const METACalibrationLibrary_COMMON_CFG_TW *cfg, int *pStopFlag);
/**
 * \ingroup General
 * \details Clear up the test with frequency related META share library context variable.
 *
 * \param [in] meta_handle target context
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_DeInit(const int meta_handle);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META calibration flow.
 * The status is noticed asynchronously by callback functions.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_TestWithFrequency_StartCalibrationW
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_StartCalibration(const int meta_handle,
                                                                                                    const METACalibrationLibrary_COMMON_CFG_T *cfg);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META calibration flow with wide character format supported.
 * The status is noticed asynchronously by callback functions.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_StartCalibrationW(const int meta_handle,
                                                                                                     const METACalibrationLibrary_COMMON_CFG_TW *cfg);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META testing flow.
 * The status is noticed asynchronously by callback functions.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_TestWithFrequency_StartTestW
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] freqCount of array testFreq
 * \param [in] testFreq information array for test criteria
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_StartTest(const int meta_handle,
                                                                                             const METACalibrationLibrary_COMMON_CFG_T *cfg,
                                                                                             const int freqCount,
                                                                                             METACalibrationLibrary_TEST_FREQUENCY testFreq[]);
/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META testing flow with wide character format supported.
 * The status is noticed asynchronously by callback functions.
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] freqCount of array testFreq
 * \param [in] testFreq information array for test criteria
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_TestWithFrequency_StartTestW(const int meta_handle,
                                                                                              const METACalibrationLibrary_COMMON_CFG_TW *cfg,
                                                                                              const int freqCount,
                                                                                              METACalibrationLibrary_TEST_FREQUENCY testFreq[]);
/**
 * <pre>
 * \ingroup General
 * \details Enable WIFI/BT tx/rx test.
 *
 * \param [in] meta_handle target context
 * \param [in] enable_wifi_tx_test enable wifi tx test
 * \param [in] enable_wifi_rx_test enable wifi rx test
 * \param [in] enable_bt_tx_test enable bt tx test
 * \param [in] enable_bt_rx_test enable bt rx test
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * \note call the api after METACalibrationLibrary_Init or METACalibrationLibrary_TestWithFrequency_Init
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_SetParameter_WCNTxRxTest(const int meta_handle,
                                                                                          bool  enable_wifi_tx_test,
                                                                                          bool  enable_wifi_rx_test,
                                                                                          bool  enable_bt_tx_test,
                                                                                          bool  enable_bt_rx_test);
/**
 * \ingroup General
 * \details The option of instrument control
 */
typedef struct _S_CalibrationLibrary_InstControlOption
{
    bool   skipInstInitialize;    /**< Skip instrument initialization option. True: Yes, False: No */
    UInt32 instHandle;           /**< Instrument handle for usage. if [skipInstInitialize] is true, must provide a vaild handle for instrumenet.  */
} S_CalibrationLibrary_InstControlOption;

/**
 * \ingroup General
 * \details The option of instrument control
 */
typedef struct _S_CalibrationLibrary_TestByFrequencyOption
{
    bool                                              enableByFreqTest;    /**< Divide testing sequence by single frequency. True: Yes, False: No */
    unsigned short                                     testFreqCount;       /**< Testing by how much frequency. */
    METACalibrationLibrary_TEST_FREQUENCY                *testFreqInfoArray;    /**< Testing option include RAT/Band/Freq */
} S_CalibrationLibrary_TestByFrequencyOption;
/**
 * \ingroup General
 * \details The option for calibration library scenario control
 */
typedef struct _S_CalibrationLibrary_TestOptionControl
{
    S_CalibrationLibrary_InstControlOption          instControl;
    S_CalibrationLibrary_TestByFrequencyOption       testByFreqControl;
    const wchar_t                                *testingSequence;    /**< configuration for the cal/test item execution sequence */
} S_CalibrationLibrary_TestOptionControl;

/**
 * \ingroup General
 * \details The option for calibration library scenario control
 */
typedef struct _S_CalibrationLibrary_TestTaskContext
{
    unsigned short    taskId;
    char             taskName[64];
    char             taskDescription[256];
    char             dependencyStr[64];
} S_CalibrationLibrary_TestTaskContext;

/**
 * \ingroup General
 * \details The enumeration for calibration and NSFT testing items definition.
 */
typedef enum
{
    MULTIMODE_PRE_SELF_CALIBRATION              = 0
    , MULTIMODE_POST_SELF_CALIBRATION
    , FR2_PRE_SELF_CALIBRATION
    , FR2_POST_SELF_CALIBRATION
    , GGE_PRE_SELF_CALIBRATION
    , GGE_POST_SELF_CALIBRATION
    , WCDMA_PRE_SELF_CALIBRATION
    , WCDMA_POST_SELF_CALIBRATION
    , TDSCDMA_PRE_SELF_CALIBRATION
    , TDSCDMA_POST_SELF_CALIBRATION
    , C2K_PRE_SELF_CALIBRATION
    , C2K_POST_SELF_CALIBRATION
    , LTE_PRE_SELF_CALIBRATION
    , LTE_POST_SELF_CALIBRATION
    , WCDMA_DPD_CALIBRATION
    , C2K_DPD_CALIBRATION
    , LTE_DPD_CALIBRATION
    , NR_DPD_CALIBRATION
    , NR_FR2_DPD_CALIBRATION
    , LTE_ET_CALIBRATION
    , NR_ET_CALIBRATION
    , TADC_CALIBRATION
    , AFC_CALIBRATION
    , LTE_CO_TMS_CALIBRATION
    , GGE_CO_TMS_CALIBRATION
    , GGE_CALIBRATION
    , WCDMA_CALIBRATION
    , C2K_CALIBRATION
    , TDSCDMA_CALIBRATION
    , LTE_CALIBRATION
    , NR_CALIBRATION
    , NR_SRS_CALIBRATION
    , GGE_NSFT
    , WCDMA_NSFT
    , C2K_NSFT
    , TDSCDMA_NSFT
    , LTE_NSFT
    , NR_NSFT
    , NR_FR2_NSFT
    , TAS_TEST
    , WCN_NSFT
    , GGE_CO_TMS_MEASUREMENT
    , CDDC_TEST
    , TRANSCEIVER_POWER_TEST
    , LTE_SRS_CALIBRATION
    , LTE_MULTI_CPL_CALIBRATION
    , NR_MULTI_CPL_CALIBRATION
    , LTE_TAS_CALIBRATION
    , NR_TAS_CALIBRATION
    , WCN_WIFI_CRYSTAL_TRIM
    , WCN_WIFI_POWER_CAL
    , WCN_BT_CRYSTAL_TRIM
    , WCN_BT_POWER_CAL
    , NR_CO_TMS_CALIBRATION
    , ET_DPD_CALIBRATION
    , IOT_NTN_CALIBRATION
    , IOT_NTN_TAS_CALIBRATION
    , IOT_NTN_NSFT
    , RFFE_CHECK_TEST
    , COMBINE_SELF_CAL_CALIBRATION
    , LTE_TRANSCEIVER_POWER_CALIBRATION
    , NR_TRANSCEIVER_POWER_CALIBRATION
    , WCDMA_TRANSCEIVER_POWER_CALIBRATION
    , WCN_WIFI_HW_LIMIT_CAL
    , LTE_PRETEST
    , NR_PRETEST
    , WCN_GPS_COTMS_CAL
    , OSL_CALIBRATION
    , LTE_LM_CALIBRATION
    , NR_LM_CALIBRATION
    //--------------------------------------------------//
    // Add new item below, the existance sequence could not be modified.
    , E_METACalibrationLibrary_TestTaskContext_NUM
} E_METACalibrationLibrary_TestTaskContext;

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Get default META calibration and testing flow context for this meta handle(DUT)
 *
 * \param [in] meta_handle target context
 * \param [in,out] counter of the task number
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_GetDefaultTestTaskContextCount(const int meta_handle,
                                                                                                unsigned short &taskCount);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Get default META calibration and testing flow context for this meta handle(DUT)
 *
 * \param [in] meta_handle target context
 * \param [in] counter of the task number
 * \param [in,out] array pointer to return the conext )
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_GetDefaultTestTaskContextArray(const int meta_handle,
                                                                                                const unsigned short &taskCount,
                                                                                                S_CalibrationLibrary_TestTaskContext *pTaskContext);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Start META calibration and testing flow.
 * The status is noticed asynchronously by callback functions.
 * \sa Support the wide character format of input parameter, METACalibrationLibrary_StartW
 * \sa Support the multi-thread functions, METACalibrationLibrary_StartForMultiThread
 *
 * \param [in] meta_handle target context
 * \param [in] cfg the calibration items configuration parameters
 * \param [in] configurate the flow control detail
 * \param [in,out] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_StartEx(const int meta_handle,
                                                                         const S_CalibrationLibrary_TestOptionControl *testOptionCtrl,
                                                                         const METACalibrationLibrary_COMMON_CFG_TW *cfg,
                                                                         int *pStopFlag);

/**
 * \ingroup General
 * \details The information for each calibration data supportted in DUT
 */
typedef struct _S_CalibrationLibrary_CalDataItemContext
{
    char itemId; /**< Calibration item ID */
    char hwType; /**< Hardware type, 0:invalid, 1:FR1, 2:FR2 */
    char name[64]; /**< Calibration item name */
    char category[32]; /**< Calibration item category */
    char description[256]; /**< Calibration item description */
} S_CalibrationLibrary_CalDataItemContext;

/**
 * \ingroup General
 * \details The option for execute calibration data operation
*/
typedef struct _S_CalibrationLibrary_CalDataOperationOption
{
    bool isFailedStop; /**< Stop calibration operations while one them failed */
} S_CalibrationLibrary_CalDataOperationOption;

/**
 * \ingroup General
 * \details The request for execute calibration data operations
*/
typedef struct _S_CalibrationLibrary_CalDataOperationReq
{
    wchar_t *logPath;   /**< log path of cal-data operations */
    unsigned int   itemIdCount; /**< count of itemIdArray */
    char *itemIdArray;/**< calibration item id array */
} S_CalibrationLibrary_CalDataOperationReq;


/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Initialize cal-data operation.(Gen99 only)
 *
 * \param [in] metaHandle target context
 * \param [in] logPath log path of cal-data operations
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_InitCalDataOperation(const int meta_handle, wchar_t *logPath);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details De-initialize cal-data operation.(Gen99 only)
 *
 * \param [in] metaHandle target context
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_DeInitCalDataOperation(const int meta_handle);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Get supported META calibration item count from DUT.(Gen99 only)
 *
 * \param [in] metaHandle target context
 *
 * \retval The count of total calibration items supportted in DUT. -1: not supported, -2: invalid handle
 * </pre>
 */
int __stdcall METACalibrationLibrary_GetTargetCalDataItemCount(const int metaHandle);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Get supported META calibration item information from DUT.(Gen99 only)
 *
 * \param [in] meta_handle target context
 * \param [in,out] pCalItemCount count of pCalDataItem
 * \param [in,out] pCalDataItem context array of supported calibration item
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_GetTargetCalDataItemArray(const int meta_handle,
                                                                                           int *pCalItemCount,
                                                                                           S_CalibrationLibrary_CalDataItemContext *pCalDataItem);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Get supported META calibration item count from file.(Gen99 only)
 *
 * \param [in] meta_handle target context
 * \param [in] iniPath file path (*.ini)
 *
 * \retval The count of total calibration items supportted in file. , -1: not supported, -2: invalid handle, -3: invalid iniPath
 * </pre>
 */
int __stdcall METACalibrationLibrary_GetFileCalDataItemCount(const int meta_handle, const wchar_t *iniPath);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Get supported META calibration item information from file.(Gen99 only)
 *
 * \param [in] meta_handle target context
 * \param [in] iniPath file path (*.ini)
 * \param [in,out] pCalItemCount count of pCalDataItem
 * \param [in,out] pCalDataItem context array of supported calibration item
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_GetFileCalDataItemArray(const int meta_handle,
                                                                                         const wchar_t *iniPpath,
                                                                                         int *pCalItemCount,
                                                                                         S_CalibrationLibrary_CalDataItemContext *pCalDataItem);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Read calibration data from DUT.(Gen99 only)
 *
 * \param [in] meta_handle target context
 * \param [in] req request calibration items with read from target operation
 * \param [in] reqOption option setting for calibration request
 * \param [in] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_ReadCalDataFromTarget(const int metaHandle,
                                                                                       const S_CalibrationLibrary_CalDataOperationReq *req,
                                                                                       const S_CalibrationLibrary_CalDataOperationOption *reqOption,
                                                                                       int *pStopFlag);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Write calibration data to DUT.(Gen99 only)
 *
 * \param [in] meta_handle target context
 * \param [in] req request calibration items with write to target operation
 * \param [in] reqOption option setting for calibration request
 * \param [in] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_WriteCalDataToTarget(const int metaHandle,
                                                                                      const S_CalibrationLibrary_CalDataOperationReq *req,
                                                                                      const S_CalibrationLibrary_CalDataOperationOption *reqOption,
                                                                                      int *pStopFlag);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Import calibration data from file.(Gen99 only)
 *
 * \param [in] meta_handle target context
 * \param [in] iniPath file path (*.ini)
 * \param [in] req request calibration items with import from file operation
 * \param [in] reqOption option setting for calibration request
 * \param [in] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_ImportCalDataFromFile(const int metaHandle,
                                                                                       const wchar_t *iniPath,
                                                                                       const S_CalibrationLibrary_CalDataOperationReq *req,
                                                                                       const S_CalibrationLibrary_CalDataOperationOption *reqOption,
                                                                                       int *pStopFlag);

/**
 * <pre>
 * \ingroup TriCalFlow
 * \details Export calibration data to file.(Gen99 only)
 *
 * \param [in] meta_handle target context
 * \param [in] iniPath file path (*.ini)
 * \param [in] req request calibration items with export to file operation
 * \param [in] reqOption option setting for calibration request
 * \param [in] pStopFlag for monitoring if you terminate the calibration flow. (set the 0x9876 to terminate calibration flow)
 *
 * \retval E_METACalibrationLibrary_RESULT_SUCCESS The operation was completed successfully.
 * \retval E_METACalibrationLibrary_RESULT_FAILED  The function was failed.
 * </pre>
 */
E_METACalibrationLibrary_RESULT __stdcall METACalibrationLibrary_ExportCalDataToFile(const int metaHandle,
                                                                                     const wchar_t *iniPath,
                                                                                     const S_CalibrationLibrary_CalDataOperationReq *req,
                                                                                     const S_CalibrationLibrary_CalDataOperationOption *reqOption,
                                                                                     int *pStopFlag);


#ifdef __cplusplus
}
#endif
#endif  // end if __META_CALIBRATION_LIBRARY_H__
