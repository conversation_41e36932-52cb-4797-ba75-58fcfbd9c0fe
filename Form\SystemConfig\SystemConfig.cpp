// SystemConfig.cpp : implementation file
//

#include "stdafx.h"
#include "SN Writer.h"
#include "SN WriterDlg.h"
#include "SystemConfig.h"
#include "MesConnectDlg.h"
#include "MesConnectDlg2.h"

#include <shlwapi.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSystemConfig dialog


CSystemConfig::CSystemConfig(CWnd* pParent /*=NULL*/)
: CDialog(CSystemConfig::IDD, pParent)
, m_iScanCodeType(FALSE)
{
    //{{AFX_DATA_INIT(CSystemConfig)
    //***********C2K *************
    m_strMeidHD = _T("");
    m_strEsnHD  = _T("");
    //***********C2K *************
    m_strLogDir = _T("");
    m_strWifiHD = _T("");
    m_strMD_2_DBPath = _T("");
    m_strMD_1_DBPath = _T("");
    m_strIMEI_4_HD = _T("");
    m_strIMEI_3_HD = _T("");
    m_strIMEI_2_HD = _T("");
    m_strIMEI_1_HD = _T("");
    m_strBTHD = _T("");
    m_strBarcHD = _T("");
    m_strAPDbPath = _T("");
    m_bWriteEthernetMac = FALSE;
    m_bCheckEthernetHD = FALSE;
    m_strEthernetHD = _T("");
    m_bCheckDrmkeyMCID_HD = FALSE;
    m_strDrmkeyMCID_HD = _T("");
    m_bWriteDrmkeyMCID = FALSE;
    m_bWriteSerialNo = FALSE;
    m_bCheckSerialNoHD = FALSE;
    m_strSerialNoHD = _T("");
    m_bAPDBFromDUT = FALSE;
    m_bMDDBFromDUT = FALSE;
    //}}AFX_DATA_INIT
    m_pToolTip = NULL;
	m_mesconnectcheck = FALSE;
	m_factoryreset = FALSE;
	m_reboot = FALSE;
	m_checkversion = FALSE;
	m_CheckInt = FALSE;
	m_strversion = _T("");
	m_strint = _T("");
	//add peiiang
	m_bWriteSIMEI = FALSE;

}


void CSystemConfig::DoDataExchange(CDataExchange* pDX)
{
    CDialog::DoDataExchange(pDX);
    //{{AFX_DATA_MAP(CSystemConfig)
    //*************C2K***************
    DDX_Text(pDX, IDC_MEID_HEADER_STR, m_strMeidHD);
    DDV_MaxChars(pDX, m_strMeidHD, 8);
    DDX_Text(pDX, IDC_ESN_HEADER_STR, m_strEsnHD);
    DDV_MaxChars(pDX, m_strEsnHD, 6);
    DDX_Check(pDX, IDC_MEID_HEADER_CHECK, m_bCheckMeidHD);
    DDX_Check(pDX, IDC_ESN_HEADER_CHECK, m_bCheckEsnHD);
    DDX_Check(pDX, IDC_WRITE_MEID_CHECK, m_bWriteMeid);
    DDX_Check(pDX, IDC_WRITE_ESN_CHECK, m_bWriteEsn);
    //*************C2K***************
    DDX_Text(pDX, IDC_LOG_DIR, m_strLogDir);
    DDX_Text(pDX, IDC_WIFI_HEADER_STR, m_strWifiHD);
    DDV_MaxChars(pDX, m_strWifiHD, 8);
    DDX_Text(pDX, IDC_MD_2_DBFILE_PATH, m_strMD_2_DBPath);
    DDV_MaxChars(pDX, m_strMD_2_DBPath, 256);
    DDX_Text(pDX, IDC_MD_1_DBFILE_PATH, m_strMD_1_DBPath);
    DDV_MaxChars(pDX, m_strMD_1_DBPath, 256);
    DDX_Text(pDX, IDC_IMEI_4_HEADER_STR, m_strIMEI_4_HD);
    DDV_MaxChars(pDX, m_strIMEI_4_HD, 8);
    DDX_Text(pDX, IDC_IMEI_3_HEADER_STR, m_strIMEI_3_HD);
    DDV_MaxChars(pDX, m_strIMEI_3_HD, 8);
    DDX_Text(pDX, IDC_IMEI_2_HEADER_STR, m_strIMEI_2_HD);
    DDV_MaxChars(pDX, m_strIMEI_2_HD, 8);
    DDX_Text(pDX, IDC_IMEI_1_HEADER_STR, m_strIMEI_1_HD);
    DDV_MaxChars(pDX, m_strIMEI_1_HD, 8);
    DDX_Text(pDX, IDC_BT_HEADER_STR, m_strBTHD);
    DDV_MaxChars(pDX, m_strBTHD, 8);
    DDX_Text(pDX, IDC_BARC_HEADER_STR, m_strBarcHD);
    DDV_MaxChars(pDX, m_strBarcHD, 8);
    DDX_Text(pDX, IDC_AP_DBFILE_PATH, m_strAPDbPath);
    DDV_MaxChars(pDX, m_strAPDbPath, 256);
    DDX_Check(pDX, IDC_BARC_HEADER_CHECK, m_bCheckBarcHD);
    DDX_Check(pDX, IDC_BT_HEADER_CHECK, m_bCheckBTHD);
    DDX_Check(pDX, IDC_IMEI_1_HEADER_CHECK, m_bCheckIMEI_1_HD);
    DDX_Check(pDX, IDC_IMEI_2_HEADER_CHECK, m_bCheckIMEI_2_HD);
    DDX_Check(pDX, IDC_IMEI_3_HEADER_CHECK, m_bCheckIMEI_3_HD);
    DDX_Check(pDX, IDC_IMEI_4_HEADER_CHECK, m_bCheckIMEI_4_HD);
    DDX_Check(pDX, IDC_DUAL_IMEI, m_bDualIMEI);
    DDX_Check(pDX, IDC_DUAL_IMEI_SAME, m_bDualIMEISame);
    DDX_Check(pDX, IDC_FOUR_IMEI, m_bFourIMEI);
    DDX_Check(pDX, IDC_IMEI_CHECKSUM, m_bIMEICheckSum);
    DDX_Check(pDX, IDC_THREE_IMEI, m_bThreeIMEI);
    DDX_Check(pDX, IDC_WIFI_HEADER_CHECK, m_bCheckWifiHD);
    DDX_Check(pDX, IDC_WRITE_BARC_CHECK, m_bWriteBarc);
    DDX_Check(pDX, IDC_WRITE_BT_CHECK, m_bWriteBTAddr);
    DDX_Check(pDX, IDC_WRITE_IMEI_CHECK, m_bWriteIMEI);
    DDX_Check(pDX, IDC_WRITE_WIFI_CHECK, m_bWriteWifi);
	//add peiqiang
	DDX_Check(pDX, IDC_WRITE_SIMEI_CHECK, m_bWriteSIMEI);
    DDX_Check(pDX, IDC_IMEI_LOCK, m_bIMEILock);
    DDX_Check(pDX, IDC_WRITE_ETHERNET_MAC_CHECK, m_bWriteEthernetMac);
    DDX_Check(pDX, IDC_ETHERNET_HEADER_CHECK, m_bCheckEthernetHD);
    DDX_Text(pDX, IDC_ETHERNET_HEADER_STR, m_strEthernetHD);
    DDV_MaxChars(pDX, m_strEthernetHD, 8);
    DDX_Check(pDX, IDC_DRMKEY_MCID_HEADER_CHECK, m_bCheckDrmkeyMCID_HD);
    DDX_Text(pDX, IDC_DRMKEY_MCID_HEADER_STR, m_strDrmkeyMCID_HD);
    DDV_MaxChars(pDX, m_strDrmkeyMCID_HD, 8);
    DDX_Check(pDX, IDC_WRITE_DRMKEY_MCID_CHECK, m_bWriteDrmkeyMCID);
    DDX_Check(pDX, IDC_WRITE_SERIAL_NO_CHECK, m_bWriteSerialNo);
    DDX_Check(pDX, IDC_SERIAL_NO_HEADER_CHECK, m_bCheckSerialNoHD);
    DDX_Text(pDX, IDC_SERIAL_NO_HEADER_STR, m_strSerialNoHD);
    //	DDX_Check(pDX, IDC_EXTERN_MD_DOWNLOAD, m_bExternMDDownload);
    DDV_MaxChars(pDX, m_strSerialNoHD, 8);
    //}}AFX_DATA_MAP

    DDX_Check(pDX, IDC_MESCONNECT_CHECK, m_mesconnectcheck);
	DDX_Check(pDX, IDC_FACTORY_RESET, m_factoryreset);
	DDX_Check(pDX, IDC_REBOOT, m_reboot);
	DDX_Check(pDX, IDC_CHECK_VERSION, m_checkversion);
	DDX_Text(pDX, IDC_VERSION_STR, m_strversion);
	DDX_Check(pDX, IDC_CHECK_INT, m_CheckInt);
	DDX_Text(pDX, IDC_INT_STR, m_strint);
    DDV_MaxChars(pDX, m_strBTHD, 40);
    DDX_Check(pDX, IDC_APDB_FROM_DUT_CHECK, m_bAPDBFromDUT);
    DDX_Check(pDX, IDC_MDDB_FROM_DUT_CHECK, m_bMDDBFromDUT);
    DDX_Control(pDX, IDC_BTN_MD1, m_Load_MD1DB_BTN);
    DDX_Control(pDX, IDC_BTN_MD2, m_Load_MD2DB_BTN);
    DDX_Control(pDX, IDC_BTN_AP, m_Load_APDB_BTN);
    DDX_Control(pDX, IDC_BTN_LOG, m_LogDir_BTN);
    DDX_Control(pDX, IDC_BTN_SAVE, m_SaveBTN);
    DDX_Radio(pDX, IDC_SCAN_CODE_IMEI, m_iScanCodeType);
	DDX_Control(pDX, IDC_MESSELECT, m_szmesstr);
}


BEGIN_MESSAGE_MAP(CSystemConfig, CDialog)
    //{{AFX_MSG_MAP(CSystemConfig)
    ON_BN_CLICKED(IDC_BARC_HEADER_CHECK, OnBarcHeaderCheck)
    ON_BN_CLICKED(IDC_BT_HEADER_CHECK, OnBtHeaderCheck)
    ON_BN_CLICKED(IDC_BTN_AP, OnBtnAp)
    ON_BN_CLICKED(IDC_BTN_MD1, OnBtnMd1)
    ON_BN_CLICKED(IDC_BTN_MD2, OnBtnMd2)
    ON_BN_CLICKED(IDC_DUAL_IMEI, OnDualImei)
    ON_BN_CLICKED(IDC_DUAL_IMEI_SAME, OnDualImeiSame)
    ON_BN_CLICKED(IDC_FOUR_IMEI, OnFourImei)
    ON_BN_CLICKED(IDC_IMEI_1_HEADER_CHECK, OnImei1HeaderCheck)
    ON_BN_CLICKED(IDC_IMEI_2_HEADER_CHECK, OnImei2HeaderCheck)
    ON_BN_CLICKED(IDC_IMEI_3_HEADER_CHECK, OnImei3HeaderCheck)
    ON_BN_CLICKED(IDC_IMEI_4_HEADER_CHECK, OnImei4HeaderCheck)
    ON_BN_CLICKED(IDC_IMEI_CHECKSUM, OnImeiChecksum)
    ON_BN_CLICKED(IDC_IMEI_LOCK, OnImeiLock)
    ON_BN_CLICKED(IDC_THREE_IMEI, OnThreeImei)
    ON_BN_CLICKED(IDC_WIFI_HEADER_CHECK, OnWifiHeaderCheck)
    ON_BN_CLICKED(IDC_WRITE_BARC_CHECK, OnWriteBarcCheck)
    ON_BN_CLICKED(IDC_WRITE_BT_CHECK, OnWriteBtCheck)
    ON_BN_CLICKED(IDC_WRITE_IMEI_CHECK, OnWriteImeiCheck)
    ON_BN_CLICKED(IDC_WRITE_WIFI_CHECK, OnWriteWifiCheck)
    ON_BN_CLICKED(IDC_BTN_SAVE, OnBtnSave)
    ON_BN_CLICKED(IDC_WRITE_ETHERNET_MAC_CHECK, OnWriteEthernetMacCheck)
    ON_BN_CLICKED(IDC_WRITE_DRMKEY_MCID_CHECK, OnWriteDrmkeyMcidCheck)
    ON_BN_CLICKED(IDC_DRMKEY_MCID_HEADER_CHECK, OnDrmkeyMcidHeaderCheck)
    ON_BN_CLICKED(IDC_WRITE_SERIAL_NO_CHECK, OnWriteSerialNoCheck)
    ON_BN_CLICKED(IDC_SERIAL_NO_HEADER_CHECK, OnSerialNoHeaderCheck)
    //C2K
    ON_BN_CLICKED(IDC_WRITE_MEID_CHECK, OnWriteMeidCheck)
    ON_BN_CLICKED(IDC_WRITE_ESN_CHECK, OnWriteEsnCheck)
    ON_BN_CLICKED(IDC_MEID_HEADER_CHECK, OnMeidHeaderCheck)
    ON_BN_CLICKED(IDC_ESN_HEADER_CHECK, OnEsnHeaderCheck)
    ON_BN_CLICKED(IDC_ETHERNET_HEADER_CHECK, OnEthernetHeaderCheck)

    ON_BN_CLICKED(IDC_APDB_FROM_DUT_CHECK, OnAPDBFromDUTCheck)
    ON_BN_CLICKED(IDC_MDDB_FROM_DUT_CHECK, OnMDDBFromDUTCheck)
    ON_BN_CLICKED(IDC_BTN_LOG, OnLogDir)
    //}}AFX_MSG_MAP
	ON_BN_CLICKED(IDC_MESCONNECT_BUTTON, OnBnClickedMesconnectButton)
	ON_BN_CLICKED(IDC_MESCONNECT_CHECK, OnBnClickedMesconnectCheck)
	ON_EN_CHANGE(IDC_ESN_HEADER_STR, &CSystemConfig::OnEnChangeEsnHeaderStr)
	ON_BN_CLICKED(IDC_FACTORY_RESET, &CSystemConfig::OnBnClickedFactoryReset)
	ON_BN_CLICKED(IDC_REBOOT, &CSystemConfig::OnBnClickedReboot)
	ON_BN_CLICKED(IDC_CHECK_INT, &CSystemConfig::OnBnClickedCheckInt)
	ON_BN_CLICKED(IDC_CHECK_VERSION, &CSystemConfig::OnBnClickedCheckVersion)
	ON_CBN_SELCHANGE(IDC_MESSELECT, &CSystemConfig::OnCbnSelchangeMesselect)
	ON_WM_VSCROLL()
	ON_WM_MOUSEWHEEL()
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSystemConfig message handlers

BOOL CSystemConfig::OnInitDialog()
{
    CDialog::OnInitDialog();

    // TODO: Add extra initialization here
    m_Load_MD1DB_BTN.SetShade(CShadeButtonST::SHS_SOFTBUMP);
    m_Load_MD2DB_BTN.SetShade(CShadeButtonST::SHS_SOFTBUMP);
    m_Load_APDB_BTN.SetShade(CShadeButtonST::SHS_SOFTBUMP);
    m_LogDir_BTN.SetShade(CShadeButtonST::SHS_SOFTBUMP);
    m_SaveBTN.SetShade(CShadeButtonST::SHS_SOFTBUMP);

    if (g_sMetaComm.eTargetType == SMART_PHONE_DUALMODEM)
    {
        g_sMetaComm.sIMEIOption.bDualIMEI = true;
        g_sMetaComm.sIMEIOption.iImeiNums = DUAL_IMEI;
    }

    if(!g_sMetaComm.EnableSerialNo)
    {
        GetDlgItem(IDC_WRITE_SERIAL_NO_CHECK)->ShowWindow(FALSE);
        g_sMetaComm.sWriteOption.bWriteSerialNo = false;
    }
	if(!g_sMetaComm.bEnableSignatureIMEI)
	{
		GetDlgItem(IDC_WRITE_SIMEI_CHECK)->ShowWindow(FALSE);
		g_sMetaComm.sWriteOption.bWriteSIMEI = false;
	}
    if (g_sMetaComm.eTargetType != SMART_PHONE)
    {
        g_sMetaComm.sDBFileOption.bAPDBFromDUT = FALSE;
        g_sMetaComm.sDBFileOption.bMDDBFromDUT = FALSE;
    }

    if (!g_sMetaComm.bEnableLockOtp)
        GetDlgItem(IDC_LOCK_OTP)->ShowWindow(SW_HIDE);
    else
        ((CButton *)GetDlgItem(IDC_LOCK_OTP))->SetCheck(g_sMetaComm.sIMEIOption.bLockOtp ? BST_CHECKED : BST_UNCHECKED);

    UpdateConfigUIOption();

    // Tool Tip
    m_pToolTip = new CToolTipCtrl;
    m_pToolTip->Create(this, TTS_NOPREFIX);
    m_pToolTip->SetDelayTime(TTDT_INITIAL, 50);
    m_pToolTip->SetDelayTime(TTDT_AUTOPOP, 2000);
    m_pToolTip->AddTool(GetDlgItem(IDC_APDB_FROM_DUT_CHECK), _T("Only support mt6763 etc. new platforms."));
    m_pToolTip->AddTool(GetDlgItem(IDC_MDDB_FROM_DUT_CHECK), _T("Only support mt6750/55/97/57 etc. new platforms."));

	//
	SCROLLINFO scrollinfo;
	GetScrollInfo(SB_VERT,&scrollinfo,SIF_ALL);
	scrollinfo.nPage=10; //设置滑块大小
	scrollinfo.nMax=120; //设置滚动条的最大位置0–100
	SetScrollInfo(SB_VERT,&scrollinfo,SIF_ALL);

	CRect rc;
	int scrWidth,scrHeight;
	GetClientRect(rc);
	//获得屏幕尺寸
	scrWidth = GetSystemMetrics(SM_CXSCREEN);
	scrHeight = GetSystemMetrics(SM_CYSCREEN);
	//取得窗口尺寸
	GetWindowRect(&rc);
	//重新设置rect里的值
	rc.left = (scrWidth-rc.right)/2;
	rc.top = (scrHeight-rc.bottom)/2;
	rc.bottom = rc.top + 520;
	rc.right = rc.left + 600;
	MoveWindow(rc);
	//

    return TRUE;  // return TRUE unless you set the focus to a control
    // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CSystemConfig::PreTranslateMessage(MSG* pMsg)
{
    // TODO: Add your specialized code here and/or call the base class

    // ToolTip
    if (m_pToolTip != NULL && m_pToolTip->m_hWnd != NULL)
        m_pToolTip->RelayEvent(pMsg);

    // not close dialog on press "Return" On "Esc" key
    if (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)
        return TRUE;

    return CDialog::PreTranslateMessage(pMsg);
}

void CSystemConfig::OnBarcHeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sBarcHeader_Option.bCheckHeader = (m_bCheckBarcHD == TRUE);
    ((CWnd*)GetDlgItem(IDC_BARC_HEADER_STR))->EnableWindow(m_bCheckBarcHD);
}

void CSystemConfig::OnBtHeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sBTHeader_Option.bCheckHeader = (m_bCheckBTHD == TRUE);
    ((CWnd*)GetDlgItem(IDC_BT_HEADER_STR))->EnableWindow(m_bCheckBTHD);
}

void CSystemConfig::OnWifiHeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWifiHeader_Option.bCheckHeader = (m_bCheckWifiHD == TRUE);
    ((CWnd*)GetDlgItem(IDC_WIFI_HEADER_STR))->EnableWindow(m_bCheckWifiHD);
}

void CSystemConfig::OnBtnAp()
{
    // TODO: Add your control notification handler code here
    char szFilter[]="Database files(*.*)|*.*||";

    CFileDialog dlg(TRUE, NULL, NULL,
        OFN_FILEMUSTEXIST|OFN_EXPLORER|OFN_ENABLESIZING|0x10000000/*OFN_FORCESHOWHIDDEN*/,
        szFilter, this);
    dlg.m_ofn.lpstrTitle = _T("Select AP database file...");
    if(dlg.DoModal() == IDOK)
    {
        g_sMetaComm.sDBFileOption.bDBInitAP = false;
        m_strAPDbPath = dlg.GetPathName();
        // jiali add begin 自动选择DB文件
        CString szDBSearch(m_strAPDbPath.Left(m_strAPDbPath.ReverseFind('\\')));
        szDBSearch += TEXT("\\BPLG*");
        CFileFind finder;
        if (finder.FindFile(szDBSearch))
        {
            finder.FindNextFile();
            g_sMetaComm.sDBFileOption.bDBInitModem_1 = false;
            m_strMD_1_DBPath = finder.GetFilePath();
            CEdit* pEdit = (CEdit*)GetDlgItem(IDC_AP_DBFILE_PATH);
            pEdit->SetWindowText(m_strAPDbPath);
            pEdit = (CEdit*)GetDlgItem(IDC_MD_1_DBFILE_PATH);
            pEdit->SetWindowText(m_strMD_1_DBPath);
            MessageBox(TEXT("Auto selected MD1_DB file!\n已自动选择 MD1_DB文件!"), TEXT("Tip"), MB_OK);
        }
		else
		{
			CString szDBSearch1(m_strAPDbPath.Left(m_strAPDbPath.ReverseFind('\\')));
			szDBSearch1 += TEXT("\\MDDB.META*");
			if (finder.FindFile(szDBSearch1))
			{
				finder.FindNextFile();
				g_sMetaComm.sDBFileOption.bDBInitModem_1 = false;
				m_strMD_1_DBPath = finder.GetFilePath();
				CEdit* pEdit = (CEdit*)GetDlgItem(IDC_AP_DBFILE_PATH);
				pEdit->SetWindowText(m_strAPDbPath);
				pEdit = (CEdit*)GetDlgItem(IDC_MD_1_DBFILE_PATH);
				pEdit->SetWindowText(m_strMD_1_DBPath);
				MessageBox(TEXT("Auto selected MD1_DB file!\n已自动选择 MD1_DB文件!"), TEXT("Tip"), MB_OK);
			}
		}
        // jiali add end
    }

    CEdit* pEdit = (CEdit*)GetDlgItem(IDC_AP_DBFILE_PATH);
    pEdit->SetWindowText(m_strAPDbPath);
}

void CSystemConfig::OnBtnMd1()
{
    // TODO: Add your control notification handler code here
    char szFilter[]="Database files(*.*)|*.*";

    CFileDialog dlg(TRUE, NULL, NULL,
        OFN_FILEMUSTEXIST|OFN_EXPLORER|OFN_ENABLESIZING|0x10000000/*OFN_FORCESHOWHIDDEN*/,
        szFilter, this);
    dlg.m_ofn.lpstrTitle = _T("Select Modem_1 database file...");
    if(dlg.DoModal() == IDOK)
    {
        g_sMetaComm.sDBFileOption.bDBInitModem_1 = false;
        m_strMD_1_DBPath = dlg.GetPathName();
        // jiali add begin 自动选择DB文件
        CString szDBSearch(m_strMD_1_DBPath.Left(m_strMD_1_DBPath.ReverseFind('\\')));
        szDBSearch += TEXT("\\APDB*");
        CFileFind finder;
        if (finder.FindFile(szDBSearch))
        {
            finder.FindNextFile();
            g_sMetaComm.sDBFileOption.bDBInitAP = false;
            m_strAPDbPath = finder.GetFilePath();
            CEdit* pEdit = (CEdit*)GetDlgItem(IDC_AP_DBFILE_PATH);
            pEdit->SetWindowText(m_strAPDbPath);
            pEdit = (CEdit*)GetDlgItem(IDC_MD_1_DBFILE_PATH);
            pEdit->SetWindowText(m_strMD_1_DBPath);
            MessageBox(TEXT("Auto selected AP_DB file!\n已自动选择 AP_DB文件!"), TEXT("Tip"), MB_OK);
        }
        // jiali add end
    }

    CEdit* pEdit = (CEdit*)GetDlgItem(IDC_MD_1_DBFILE_PATH);
    pEdit->SetWindowText(m_strMD_1_DBPath);
}

void CSystemConfig::OnBtnMd2()
{
    // TODO: Add your control notification handler code here
    char szFilter[]="Database files(*.*)|*.*";

    CFileDialog dlg(TRUE, NULL, NULL,
        OFN_FILEMUSTEXIST|OFN_EXPLORER|OFN_ENABLESIZING|0x10000000/*OFN_FORCESHOWHIDDEN*/,
        szFilter, this);
    dlg.m_ofn.lpstrTitle = _T("Select Modem_2 database file...");
    if(dlg.DoModal()==IDOK)
    {
        g_sMetaComm.sDBFileOption.bDBInitModem_2 = false;
        m_strMD_2_DBPath = dlg.GetPathName();
    }

    CEdit* pEdit = (CEdit*)GetDlgItem(IDC_MD_2_DBFILE_PATH);
    pEdit->SetWindowText(m_strMD_2_DBPath);
}

void CSystemConfig::OnDualImei()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIOption.bDualIMEI = (m_bDualIMEI == TRUE);
    if(m_bDualIMEI)
    {
        g_sMetaComm.sIMEIOption.iImeiNums  = DUAL_IMEI;
        g_sMetaComm.sIMEIOption.bDualIMEI  = true;
        g_sMetaComm.sIMEIOption.bThreeIMEI = false;
        g_sMetaComm.sIMEIOption.bFourIMEI  = false;
        CheckDlgButton(IDC_THREE_IMEI, FALSE);
        CheckDlgButton(IDC_FOUR_IMEI, FALSE);
        ((CWnd*)GetDlgItem(IDC_DUAL_IMEI_SAME))->EnableWindow(TRUE);
        UpDateIMEIHeaderOption(DUAL_IMEI, TRUE);
    }
    else
    {
        g_sMetaComm.sIMEIOption.iImeiNums = ONE_IMEI;
        g_sMetaComm.sIMEIOption.bDualIMEI = false;
        ((CWnd*)GetDlgItem(IDC_DUAL_IMEI_SAME))->EnableWindow(FALSE);
        UpDateIMEIHeaderOption(DUAL_IMEI, FALSE);
    }
}

void CSystemConfig::OnDualImeiSame()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIOption.bDualIMEISame = (m_bDualIMEISame == TRUE);
    (CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_STR)->EnableWindow(m_bCheckIMEI_2_HD);
    (CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_CHECK)->EnableWindow(!m_bDualIMEISame);
}


void CSystemConfig::OnFourImei()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIOption.bFourIMEI = (m_bFourIMEI == TRUE);
    if(IsDlgButtonChecked(IDC_FOUR_IMEI))
    {
        g_sMetaComm.sIMEIOption.iImeiNums = FOUR_IMEI;
        g_sMetaComm.sIMEIOption.bDualIMEI = false;
        g_sMetaComm.sIMEIOption.bDualIMEISame = false;
        g_sMetaComm.sIMEIOption.bThreeIMEI = false;
        g_sMetaComm.sIMEIOption.bFourIMEI = true;
        CheckDlgButton(IDC_THREE_IMEI, FALSE);
        CheckDlgButton(IDC_DUAL_IMEI, FALSE);
        ((CWnd*)GetDlgItem(IDC_DUAL_IMEI_SAME))->EnableWindow(FALSE);
        UpDateIMEIHeaderOption(FOUR_IMEI, TRUE);
    }
    else
    {
        g_sMetaComm.sIMEIOption.iImeiNums = ONE_IMEI;
        g_sMetaComm.sIMEIOption.bFourIMEI = false;
        //UpDateIMEIHeaderOption(ONE_IMEI, TRUE);
        UpDateIMEIHeaderOption(FOUR_IMEI, FALSE);
    }
}

void CSystemConfig::OnImei1HeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIHeader_Option[0].bCheckHeader = (m_bCheckIMEI_1_HD == TRUE);
    ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_STR))->EnableWindow(m_bCheckIMEI_1_HD);
}

void CSystemConfig::OnImei2HeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIHeader_Option[1].bCheckHeader = (m_bCheckIMEI_2_HD == TRUE);
    ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_STR))->EnableWindow(m_bCheckIMEI_2_HD);
}

void CSystemConfig::OnImei3HeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIHeader_Option[2].bCheckHeader = (m_bCheckIMEI_3_HD == TRUE);
    ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_STR))->EnableWindow(m_bCheckIMEI_3_HD);
}

void CSystemConfig::OnImei4HeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIHeader_Option[3].bCheckHeader = (m_bCheckIMEI_4_HD == TRUE);
    ((CWnd*)GetDlgItem(IDC_IMEI_4_HEADER_STR))->EnableWindow(m_bCheckIMEI_4_HD);
}

void CSystemConfig::OnImeiChecksum()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIOption.bCheckSum = (m_bIMEICheckSum == TRUE);
}

void CSystemConfig::OnImeiLock()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIOption.bLockIMEI = (m_bIMEILock == TRUE);
}

void CSystemConfig::OnThreeImei()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sIMEIOption.bThreeIMEI = (m_bThreeIMEI == TRUE);
    if(IsDlgButtonChecked(IDC_THREE_IMEI))
    {
        g_sMetaComm.sIMEIOption.iImeiNums = THREE_IMEI;
        g_sMetaComm.sIMEIOption.bDualIMEI = false;
        g_sMetaComm.sIMEIOption.bDualIMEISame = false;
        g_sMetaComm.sIMEIOption.bThreeIMEI = true;
        g_sMetaComm.sIMEIOption.bFourIMEI = false;
        CheckDlgButton(IDC_DUAL_IMEI, FALSE);
        CheckDlgButton(IDC_FOUR_IMEI, FALSE);
        ((CWnd*)GetDlgItem(IDC_DUAL_IMEI_SAME))->EnableWindow(FALSE);
        UpDateIMEIHeaderOption(THREE_IMEI, TRUE);
    }
    else
    {
        g_sMetaComm.sIMEIOption.iImeiNums = ONE_IMEI;
        g_sMetaComm.sIMEIOption.bThreeIMEI = false;
        UpDateIMEIHeaderOption(THREE_IMEI, FALSE);
    }
}


void CSystemConfig::OnWriteBarcCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWriteOption.bWriteBarcode = (m_bWriteBarc == TRUE);
    ((CWnd*)GetDlgItem(IDC_BARC_HEADER_CHECK))->EnableWindow(m_bWriteBarc);
    if (m_bWriteBarc)
    {
        ((CWnd*)GetDlgItem(IDC_BARC_HEADER_STR))->EnableWindow(m_bCheckBarcHD);
        //		((CWnd*)GetDlgItem(IDC_EXTERN_MD_DOWNLOAD))->EnableWindow(TRUE);
    }
    else
    {
        ((CWnd*)GetDlgItem(IDC_BARC_HEADER_STR))->EnableWindow(FALSE);
        //		((CWnd*)GetDlgItem(IDC_EXTERN_MD_DOWNLOAD))->EnableWindow(FALSE);
    }
}

void CSystemConfig::OnWriteBtCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWriteOption.bWriteBT = (m_bWriteBTAddr == TRUE);
    ((CWnd*)GetDlgItem(IDC_BT_HEADER_CHECK))->EnableWindow(m_bWriteBTAddr);
    if (m_bWriteBTAddr)
    {
        ((CWnd*)GetDlgItem(IDC_BT_HEADER_STR))->EnableWindow(m_bCheckBTHD);
    }
    else
    {
        ((CWnd*)GetDlgItem(IDC_BT_HEADER_STR))->EnableWindow(FALSE);
    }
}

void CSystemConfig::OnWriteImeiCheck()
{
	OnWriteBtCheck();
	OnWriteWifiCheck();
	OnWriteSerialNoCheck();
    // TODO: Add your control notification handler code here
	g_sMetaComm.sWriteOption.bWriteIMEI = g_sMetaComm.sWriteOption.bWriteWifi = 
		g_sMetaComm.sWriteOption.bWriteBT = g_sMetaComm.sWriteOption.bWriteSerialNo = 
		(m_bWriteIMEI == TRUE);
	UpdateData(TRUE);
    UpDateIMEIOption(m_bWriteIMEI);

    if(g_sMetaComm.sWriteOption.bWriteIMEI)
    {
        if(g_sMetaComm.sIMEIOption.bDualIMEI)
        {
            UpDateIMEIHeaderOption(DUAL_IMEI, TRUE);
        }
        else if(g_sMetaComm.sIMEIOption.bThreeIMEI)
        {
            UpDateIMEIHeaderOption(THREE_IMEI, TRUE);
        }
        else if(g_sMetaComm.sIMEIOption.bFourIMEI)
        {
            UpDateIMEIHeaderOption(FOUR_IMEI, TRUE);
        }
        else
        {
            UpDateIMEIHeaderOption(ONE_IMEI, TRUE);

        }
		// UpDateIMEIHeaderOption(ONE_IMEI, TRUE);
    }
    else
    {
        UpDateIMEIHeaderOption(NONE_IMEI, FALSE);
    }
}

void CSystemConfig::OnWriteWifiCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWriteOption.bWriteWifi = (m_bWriteWifi == TRUE);
    ((CWnd*)GetDlgItem(IDC_WIFI_HEADER_CHECK))->EnableWindow(m_bWriteWifi);
    if (m_bWriteWifi)
    {
        ((CWnd*)GetDlgItem(IDC_WIFI_HEADER_STR))->EnableWindow(m_bCheckWifiHD);
    }
    else
    {
        ((CWnd*)GetDlgItem(IDC_WIFI_HEADER_STR))->EnableWindow(FALSE);
    }
}

#if 0
void CSystemConfig::OnExternMDDLCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sBarcodeOption.bExtMdDl = m_bExternMDDownload;
}
#endif
void CSystemConfig::GetUIEditData()
{
    UpdateData(TRUE);

    //C2K
    strcpy_s(g_sMetaComm.sMeidHeader_Option.strHeader, m_strMeidHD.GetString());
    strcpy_s(g_sMetaComm.sEsnHeader_Option.strHeader, m_strEsnHD.GetString());

    strcpy_s(g_sMetaComm.sBarcHeader_Option.strHeader, m_strBarcHD.GetString());
    strcpy_s(g_sMetaComm.sIMEIHeader_Option[0].strHeader, m_strIMEI_1_HD.GetString());
    strcpy_s(g_sMetaComm.sIMEIHeader_Option[1].strHeader, m_strIMEI_2_HD.GetString());
    strcpy_s(g_sMetaComm.sIMEIHeader_Option[2].strHeader, m_strIMEI_3_HD.GetString());
    strcpy_s(g_sMetaComm.sIMEIHeader_Option[3].strHeader, m_strIMEI_4_HD.GetString());
    strcpy_s(g_sMetaComm.sSerialNoHeader_Option.strHeader, m_strSerialNoHD.GetString());
    strcpy_s(g_sMetaComm.sEthernetMacHeader_Option.strHeader, m_strEthernetHD.GetString());
    strcpy_s(g_sMetaComm.sDrmkeyMCIDHeader_Option.strHeader, m_strDrmkeyMCID_HD.GetString());
    strcpy_s(g_sMetaComm.sBTHeader_Option.strHeader, m_strBTHD.GetString());
    strcpy_s(g_sMetaComm.sWifiHeader_Option.strHeader, m_strWifiHD.GetString());
    g_sMetaComm.sDBFileOption.bAPDBFromDUT = (m_bAPDBFromDUT == TRUE);
    g_sMetaComm.sDBFileOption.bMDDBFromDUT = (m_bMDDBFromDUT == TRUE);
    strcpy_s(g_sMetaComm.sDBFileOption.strMD1Dbpath, m_strMD_1_DBPath.GetString());
    strcpy_s(g_sMetaComm.sDBFileOption.strMD2Dbpath, m_strMD_2_DBPath.GetString());
    strcpy_s(g_sMetaComm.sDBFileOption.strAPDbpath, m_strAPDbPath.GetString());

    strcpy_s(g_sMetaComm.strLogDir, m_strLogDir);
    g_sMetaComm.eScanCodeType = (E_SCAN_CODE_TYPE)m_iScanCodeType; // jiali add

	strcpy_s(g_sMetaComm.version, m_strversion.GetString());
	strcpy_s(g_sMetaComm.intnumber, m_strint.GetString());
}

void CSystemConfig::UpDateIMEIHeaderOption(E_IMEI_NUM iIMEINums, BOOL bEnable)
{
    if (bEnable == TRUE)
    {
        g_sMetaComm.sIMEIOption.iImeiNums = iIMEINums;
    }
    else if(g_sMetaComm.sWriteOption.bWriteIMEI)//此处易出错，会将数值重新赋值，如果后续有写其他IMEI码的需求要修改此处
    {
		if(g_sMetaComm.sIMEIOption.bDualIMEI)
		{
			g_sMetaComm.sIMEIOption.iImeiNums = DUAL_IMEI;
			iIMEINums = DUAL_IMEI;
		}
		else
		{
        g_sMetaComm.sIMEIOption.iImeiNums = ONE_IMEI;
        iIMEINums = ONE_IMEI;
		}
    }

    ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_CHECK))->EnableWindow(FALSE);
    ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_CHECK))->EnableWindow(FALSE);
    ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_CHECK))->EnableWindow(FALSE);
    ((CWnd*)GetDlgItem(IDC_IMEI_4_HEADER_CHECK))->EnableWindow(FALSE);

    ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_STR))->EnableWindow(FALSE);
    ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_STR))->EnableWindow(FALSE);
    ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_STR))->EnableWindow(FALSE);
    ((CWnd*)GetDlgItem(IDC_IMEI_4_HEADER_STR))->EnableWindow(FALSE);

    switch (iIMEINums)
    {
    case NONE_IMEI:

        ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_CHECK))->EnableWindow(FALSE);
        ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_CHECK))->EnableWindow(FALSE);
        ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_CHECK))->EnableWindow(FALSE);
        ((CWnd*)GetDlgItem(IDC_IMEI_4_HEADER_CHECK))->EnableWindow(FALSE);

        ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_STR))->EnableWindow(FALSE);
        ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_STR))->EnableWindow(FALSE);
        ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_STR))->EnableWindow(FALSE);
        ((CWnd*)GetDlgItem(IDC_IMEI_4_HEADER_STR))->EnableWindow(FALSE);
        break;

    case ONE_IMEI:

        ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_CHECK))->EnableWindow(TRUE);
        if (g_sMetaComm.sIMEIHeader_Option[0].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_STR))->EnableWindow(bEnable);
        }
        break;

    case DUAL_IMEI:

        ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_CHECK))->EnableWindow(bEnable);
        ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_CHECK))->EnableWindow(bEnable);
        if (g_sMetaComm.sIMEIHeader_Option[0].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_STR))->EnableWindow(bEnable);
        }

        if (g_sMetaComm.sIMEIHeader_Option[1].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_STR))->EnableWindow(bEnable);
        }
        break;

    case THREE_IMEI:

        ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_CHECK))->EnableWindow(bEnable);
        ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_CHECK))->EnableWindow(bEnable);
        ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_CHECK))->EnableWindow(bEnable);

        if (g_sMetaComm.sIMEIHeader_Option[0].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_STR))->EnableWindow(bEnable);
        }

        if (g_sMetaComm.sIMEIHeader_Option[1].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_STR))->EnableWindow(bEnable);
        }

        if (g_sMetaComm.sIMEIHeader_Option[2].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_STR))->EnableWindow(bEnable);
        }
        break;

    case FOUR_IMEI:

        ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_CHECK))->EnableWindow(bEnable);
        ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_CHECK))->EnableWindow(bEnable);
        ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_CHECK))->EnableWindow(bEnable);
        ((CWnd*)GetDlgItem(IDC_IMEI_4_HEADER_CHECK))->EnableWindow(bEnable);
        if (g_sMetaComm.sIMEIHeader_Option[0].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_1_HEADER_STR))->EnableWindow(bEnable);
        }

        if (g_sMetaComm.sIMEIHeader_Option[1].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_2_HEADER_STR))->EnableWindow(bEnable);
        }
        if (g_sMetaComm.sIMEIHeader_Option[2].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_3_HEADER_STR))->EnableWindow(bEnable);
        }
        if (g_sMetaComm.sIMEIHeader_Option[3].bCheckHeader)
        {
            ((CWnd*)GetDlgItem(IDC_IMEI_4_HEADER_STR))->EnableWindow(bEnable);
        }
        break;
    }
}

void CSystemConfig::UpDateIMEIOption(BOOL bWriteIMEIEnable)
{
    //GetDlgItem(IDC_IMEI_CHECKSUM)->EnableWindow(bWriteIMEIEnable);
    if(g_sMetaComm.ImeiLockHidden == true)
    {
        GetDlgItem(IDC_IMEI_LOCK)->EnableWindow(FALSE);
        GetDlgItem(IDC_IMEI_LOCK)->ShowWindow(SW_HIDE);
    }
    else
    {
        GetDlgItem(IDC_IMEI_LOCK)->EnableWindow(bWriteIMEIEnable ? TRUE: FALSE);
    }
    //GetDlgItem(IDC_DUAL_IMEI)->EnableWindow(bWriteIMEIEnable);
    //GetDlgItem(IDC_DUAL_IMEI_SAME)->EnableWindow(bWriteIMEIEnable);
   // GetDlgItem(IDC_THREE_IMEI)->EnableWindow(bWriteIMEIEnable);
   // GetDlgItem(IDC_FOUR_IMEI)->EnableWindow(bWriteIMEIEnable);
}

void CSystemConfig::UpdateConfigUIOption()
{
	m_factoryreset  = g_sMetaComm.bfactoryreset;
	m_reboot  = g_sMetaComm.breboot;
	m_checkversion  = g_sMetaComm.bcheckversion;
	m_CheckInt = g_sMetaComm.bcheckint;
	m_mesconnectcheck  = g_sMetaComm.smesconnect.mesconnectcheck;
    m_bAPDBFromDUT     = g_sMetaComm.sDBFileOption.bAPDBFromDUT;
    m_bMDDBFromDUT     = g_sMetaComm.sDBFileOption.bMDDBFromDUT;
    m_strMD_1_DBPath = g_sMetaComm.sDBFileOption.strMD1Dbpath;
    m_strMD_2_DBPath = g_sMetaComm.sDBFileOption.strMD2Dbpath;
    m_strAPDbPath    = g_sMetaComm.sDBFileOption.strAPDbpath;

    m_bWriteBarc     = g_sMetaComm.sWriteOption.bWriteBarcode;
    m_bWriteIMEI     = g_sMetaComm.sWriteOption.bWriteIMEI;
	//add peiqiang
	m_bWriteSIMEI    = g_sMetaComm.sWriteOption.bWriteSIMEI;

    m_bWriteSerialNo = g_sMetaComm.sWriteOption.bWriteSerialNo;
    m_bWriteBTAddr   = g_sMetaComm.sWriteOption.bWriteBT;
    m_bWriteWifi     = g_sMetaComm.sWriteOption.bWriteWifi;
    m_bWriteEthernetMac = g_sMetaComm.sWriteOption.bWriteEthernetMac;
    m_bWriteDrmkeyMCID = g_sMetaComm.sWriteOption.bWriteDrmkeyMCID;
    //C2K
    m_bWriteMeid     = g_sMetaComm.sWriteOption.bWriteMeid;
    m_bWriteEsn      = g_sMetaComm.sWriteOption.bWriteEsn;

    m_bIMEICheckSum  = g_sMetaComm.sIMEIOption.bCheckSum;
    m_bIMEILock      = g_sMetaComm.sIMEIOption.bLockIMEI;
    m_bDualIMEI      = g_sMetaComm.sIMEIOption.bDualIMEI;
    m_bDualIMEISame  = g_sMetaComm.sIMEIOption.bDualIMEISame;
    m_bThreeIMEI      = g_sMetaComm.sIMEIOption.bThreeIMEI;
    m_bFourIMEI       = g_sMetaComm.sIMEIOption.bFourIMEI;

    m_bCheckIMEI_1_HD = g_sMetaComm.sIMEIHeader_Option[0].bCheckHeader;
    m_bCheckIMEI_2_HD = g_sMetaComm.sIMEIHeader_Option[1].bCheckHeader;
    m_bCheckIMEI_3_HD = g_sMetaComm.sIMEIHeader_Option[2].bCheckHeader;
    m_bCheckIMEI_4_HD = g_sMetaComm.sIMEIHeader_Option[3].bCheckHeader;
    m_strIMEI_1_HD  = g_sMetaComm.sIMEIHeader_Option[0].strHeader;
    m_strIMEI_2_HD  = g_sMetaComm.sIMEIHeader_Option[1].strHeader;
    m_strIMEI_3_HD  = g_sMetaComm.sIMEIHeader_Option[2].strHeader;
    m_strIMEI_4_HD  = g_sMetaComm.sIMEIHeader_Option[3].strHeader;

    m_bCheckSerialNoHD   = g_sMetaComm.sSerialNoHeader_Option.bCheckHeader;
    m_strSerialNoHD  = g_sMetaComm.sSerialNoHeader_Option.strHeader;

    m_bCheckEthernetHD = g_sMetaComm.sEthernetMacHeader_Option.bCheckHeader;
    m_strEthernetHD = g_sMetaComm.sEthernetMacHeader_Option.strHeader;

    m_bCheckBarcHD   = g_sMetaComm.sBarcHeader_Option.bCheckHeader;
    m_strBarcHD      = g_sMetaComm.sBarcHeader_Option.strHeader;

    //C2K
    m_bCheckMeidHD   = g_sMetaComm.sMeidHeader_Option.bCheckHeader;
    m_strMeidHD      = g_sMetaComm.sMeidHeader_Option.strHeader;

    m_bCheckEsnHD   = g_sMetaComm.sEsnHeader_Option.bCheckHeader;
    m_strEsnHD      = g_sMetaComm.sEsnHeader_Option.strHeader;

    m_bCheckBTHD     = g_sMetaComm.sBTHeader_Option.bCheckHeader;
    m_strBTHD        = g_sMetaComm.sBTHeader_Option.strHeader;

    m_bCheckWifiHD   = g_sMetaComm.sWifiHeader_Option.bCheckHeader;
    m_strWifiHD      = g_sMetaComm.sWifiHeader_Option.strHeader;

    m_bCheckDrmkeyMCID_HD   = g_sMetaComm.sDrmkeyMCIDHeader_Option.bCheckHeader;
    m_strDrmkeyMCID_HD      = g_sMetaComm.sDrmkeyMCIDHeader_Option.strHeader;

    m_bUsbEnable     = g_sMetaComm.bUsbEnable;

    m_strLogDir      = g_sMetaComm.strLogDir;

    m_iScanCodeType = g_sMetaComm.eScanCodeType; // jiali add

	m_strversion      = g_sMetaComm.version;

	m_strint         =  g_sMetaComm.intnumber;

	int nIndex_mes = m_szmesstr.SelectString( 0,g_sMetaComm.smesconnect.strMes );
	m_szmesstr.SetCurSel(nIndex_mes);
	m_szmesstr.GetLBText(m_szmesstr.GetCurSel(), g_sMetaComm.smesconnect.strMes);

    //Synchronization to UI
    UpdateData(FALSE);

    OnWriteBarcCheck();
    OnWriteImeiCheck();
    OnWriteSerialNoCheck();
    OnWriteBtCheck();
    OnWriteWifiCheck();
    OnWriteEthernetMacCheck();
    OnWriteDrmkeyMcidCheck();
    OnWriteMeidCheck();
    OnWriteEsnCheck();
    EnableDBFileOption();
}

void CSystemConfig::EnableDBFileOption()
{
    switch(g_sMetaComm.eTargetType)
    {
    case TABLET_WIFI_ONLY:
        GetDlgItem(IDC_APDB_FROM_DUT_CHECK)->EnableWindow(FALSE);
        GetDlgItem(IDC_MDDB_FROM_DUT_CHECK)->EnableWindow(FALSE);
        (CWnd*)GetDlgItem(IDC_MD_1_DBFILE_PATH)->EnableWindow(FALSE);
        (CWnd*)GetDlgItem(IDC_BTN_MD1)->EnableWindow(FALSE);
        (CWnd*)GetDlgItem(IDC_MD_2_DBFILE_PATH)->EnableWindow(FALSE);
        (CWnd*)GetDlgItem(IDC_BTN_MD2)->EnableWindow(FALSE);
        (CWnd*)GetDlgItem(IDC_AP_DBFILE_PATH)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_BTN_AP)->EnableWindow(TRUE);
        break;

    case SMART_PHONE_DUALMODEM:
        GetDlgItem(IDC_APDB_FROM_DUT_CHECK)->EnableWindow(FALSE);
        GetDlgItem(IDC_MDDB_FROM_DUT_CHECK)->EnableWindow(FALSE);
        (CWnd*)GetDlgItem(IDC_MD_1_DBFILE_PATH)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_BTN_MD1)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_MD_2_DBFILE_PATH)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_BTN_MD2)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_AP_DBFILE_PATH)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_BTN_AP)->EnableWindow(TRUE);
        break;

    case RNDIG_DONGLE:
        GetDlgItem(IDC_APDB_FROM_DUT_CHECK)->EnableWindow(FALSE);
        GetDlgItem(IDC_MDDB_FROM_DUT_CHECK)->EnableWindow(FALSE);
    case SMART_PHONE:
        (CWnd*)GetDlgItem(IDC_MD_1_DBFILE_PATH)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_BTN_MD1)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_MD_2_DBFILE_PATH)->EnableWindow(FALSE);
        (CWnd*)GetDlgItem(IDC_BTN_MD2)->EnableWindow(FALSE);
        (CWnd*)GetDlgItem(IDC_AP_DBFILE_PATH)->EnableWindow(TRUE);
        (CWnd*)GetDlgItem(IDC_BTN_AP)->EnableWindow(TRUE);
        break;
    }
}

BOOL CSystemConfig::DestroyWindow()
{
    GetUIEditData();

    if (g_sMetaComm.eTargetType == SMART_PHONE_DUALMODEM)
    {
        g_sMetaComm.sIMEIOption.bDualIMEI = true;
        g_sMetaComm.sIMEIOption.iImeiNums = DUAL_IMEI;
    }
    SaveParaToSetupFile();

    // release ToolTip Control
    if (m_pToolTip != NULL)
    {
        m_pToolTip->DestroyToolTipCtrl();
        //delete m_pToolTip; // destroy has delete it
        m_pToolTip = NULL;
    }

    return CDialog::DestroyWindow();
}

void CSystemConfig::OnBtnSave()
{
    g_sMetaComm.sIMEIOption.bLockOtp = ((CButton *)GetDlgItem(IDC_LOCK_OTP))->GetCheck() == BST_CHECKED;
	//add peiqiang
	g_sMetaComm.sWriteOption.bWriteSIMEI = ((CButton *)GetDlgItem(IDC_WRITE_SIMEI_CHECK))->GetCheck() == BST_CHECKED;
    CDialog::OnOK();
}

void CSystemConfig::OnWriteEthernetMacCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWriteOption.bWriteEthernetMac =  (m_bWriteEthernetMac == TRUE);

    ((CWnd*)GetDlgItem(IDC_ETHERNET_HEADER_CHECK))->EnableWindow(m_bWriteEthernetMac);
    if (m_bWriteEthernetMac)
    {
        ((CWnd*)GetDlgItem(IDC_ETHERNET_HEADER_STR))->EnableWindow(m_bCheckEthernetHD);
    }
    else
    {
        ((CWnd*)GetDlgItem(IDC_ETHERNET_HEADER_STR))->EnableWindow(FALSE);
    }
}

void CSystemConfig::OnEthernetHeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sEthernetMacHeader_Option.bCheckHeader = (m_bCheckEthernetHD == TRUE);
    ((CWnd*)GetDlgItem(IDC_ETHERNET_HEADER_STR))->EnableWindow(m_bCheckEthernetHD);
}

void CSystemConfig::OnWriteDrmkeyMcidCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWriteOption.bWriteDrmkeyMCID = (m_bWriteDrmkeyMCID == TRUE);

    ((CWnd*)GetDlgItem(IDC_DRMKEY_MCID_HEADER_CHECK))->EnableWindow(m_bWriteDrmkeyMCID);
    if (m_bWriteDrmkeyMCID)
    {
        ((CWnd*)GetDlgItem(IDC_DRMKEY_MCID_HEADER_STR))->EnableWindow(m_bCheckDrmkeyMCID_HD);
    }
    else
    {
        ((CWnd*)GetDlgItem(IDC_DRMKEY_MCID_HEADER_STR))->EnableWindow(FALSE);
    }
}

void CSystemConfig::OnDrmkeyMcidHeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sDrmkeyMCIDHeader_Option.bCheckHeader = (m_bCheckDrmkeyMCID_HD == TRUE);
    ((CWnd*)GetDlgItem(IDC_DRMKEY_MCID_HEADER_STR))->EnableWindow(m_bCheckDrmkeyMCID_HD);
}

void CSystemConfig::OnWriteSerialNoCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWriteOption.bWriteSerialNo = (m_bWriteSerialNo == TRUE);

    GetDlgItem(IDC_SERIAL_NO_HEADER_CHECK)->EnableWindow(m_bWriteSerialNo);
    if (m_bWriteSerialNo)
    {
        GetDlgItem(IDC_SERIAL_NO_HEADER_STR)->EnableWindow(m_bCheckSerialNoHD);
        if(g_sMetaComm.SerialNumFromIMEI == true)
        {
            ((CButton *)GetDlgItem(IDC_WRITE_IMEI_CHECK))->SetCheck(TRUE);
            m_bWriteIMEI = TRUE;
            g_sMetaComm.sWriteOption.bWriteIMEI = true;
            OnWriteImeiCheck();
        }
    }
    else
    {
        GetDlgItem(IDC_SERIAL_NO_HEADER_STR)->EnableWindow(FALSE);
    }
}

void CSystemConfig::OnSerialNoHeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sSerialNoHeader_Option.bCheckHeader = (m_bCheckSerialNoHD == TRUE);
    ((CWnd*)GetDlgItem(IDC_SERIAL_NO_HEADER_STR))->EnableWindow(m_bCheckSerialNoHD);
}

void CSystemConfig::OnWriteMeidCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWriteOption.bWriteMeid = (m_bWriteMeid == TRUE);

    ((CWnd*)GetDlgItem(IDC_MEID_HEADER_CHECK))->EnableWindow(m_bWriteMeid);
    if (m_bWriteMeid)
    {
        //((CWnd*)GetDlgItem(IDC_WRITE_ESN_CHECK))->EnableWindow(FALSE);
        g_sMetaComm.sWriteOption.bWriteEsn = FALSE;
        ((CButton *)GetDlgItem(IDC_WRITE_ESN_CHECK))->SetCheck(BST_UNCHECKED);
        ((CWnd*)GetDlgItem(IDC_MEID_HEADER_STR))->EnableWindow(m_bCheckMeidHD);
    }
    else
    {
        //((CWnd*)GetDlgItem(IDC_WRITE_ESN_CHECK))->EnableWindow(TRUE);
        ((CWnd*)GetDlgItem(IDC_MEID_HEADER_STR))->EnableWindow(FALSE);
    }
}

void CSystemConfig::OnMeidHeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sMeidHeader_Option.bCheckHeader = (m_bCheckMeidHD == TRUE);
    ((CWnd*)GetDlgItem(IDC_MEID_HEADER_STR))->EnableWindow(m_bCheckMeidHD);

}

void CSystemConfig::OnEsnHeaderCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sEsnHeader_Option.bCheckHeader = (m_bCheckEsnHD == TRUE);
    ((CWnd*)GetDlgItem(IDC_ESN_HEADER_STR))->EnableWindow(m_bCheckEsnHD);
}

void CSystemConfig::OnWriteEsnCheck()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.sWriteOption.bWriteEsn = (m_bWriteEsn == TRUE);
    ((CWnd*)GetDlgItem(IDC_ESN_HEADER_CHECK))->EnableWindow(m_bWriteEsn);
    if (m_bWriteEsn)
    {
        //((CWnd*)GetDlgItem(IDC_WRITE_MEID_CHECK))->EnableWindow(FALSE);
        g_sMetaComm.sWriteOption.bWriteMeid = FALSE;
        ((CButton *)GetDlgItem(IDC_WRITE_MEID_CHECK))->SetCheck(BST_UNCHECKED);
        ((CWnd*)GetDlgItem(IDC_ESN_HEADER_STR))->EnableWindow(m_bCheckEsnHD);
    }
    else
    {
        //((CWnd*)GetDlgItem(IDC_WRITE_MEID_CHECK))->EnableWindow(TRUE);
        ((CWnd*)GetDlgItem(IDC_ESN_HEADER_STR))->EnableWindow(FALSE);
    }
}

void CSystemConfig::OnAPDBFromDUTCheck()
{
    g_sMetaComm.sDBFileOption.bDBInitAP = false;
    memset(g_sMetaComm.sDBFileOption.strAPDbPath_DUT, 0, sizeof(g_sMetaComm.sDBFileOption.strAPDbPath_DUT));
}

void CSystemConfig::OnMDDBFromDUTCheck()
{
    g_sMetaComm.sDBFileOption.bDBInitModem_1 = false;
    memset(g_sMetaComm.sDBFileOption.strMD1DbPath_DUT, 0, sizeof(g_sMetaComm.sDBFileOption.strMD1DbPath_DUT));
}

void CSystemConfig::OnLogDir()
{
    TCHAR szFolderPath[MAX_PATH] = {0};

    BROWSEINFO sInfo;
    LPITEMIDLIST lpidlBrowse;

    memset(&sInfo, 0, sizeof(sInfo));
    sInfo.hwndOwner = GetSafeHwnd();
    sInfo.pidlRoot   = 0;
    sInfo.lpszTitle  = "Please select Log Root folder...";
    sInfo.ulFlags    = BIF_DONTGOBELOWDOMAIN | BIF_RETURNONLYFSDIRS | BIF_EDITBOX | 0x41/*BIF_NEWDIALOGSTYLE*/;
    sInfo.lpfn       = NULL;

    // popup directory browser dialog
    lpidlBrowse = ::SHBrowseForFolder(&sInfo);
    if (lpidlBrowse == NULL)
        return;

    // get folder path name
    if (::SHGetPathFromIDList(lpidlBrowse, szFolderPath) == TRUE)
    {
        ::PathAddBackslash(szFolderPath);
        m_strLogDir = szFolderPath;
        SetDlgItemText(IDC_LOG_DIR, szFolderPath);
    }

    ::CoTaskMemFree(lpidlBrowse);
}
void CSystemConfig::OnBnClickedMesconnectButton()
{
	if(g_sMetaComm.smesconnect.strMes[0] == 'L')
	{
		CMesConnectDlg dlg;
		dlg.DoModal();
	}
	else
	{
		CMesConnectDlg2 dlg;
		dlg.DoModal();
	}
}


void CSystemConfig::OnBnClickedMesconnectCheck()
{
	UpdateData(TRUE);
	g_sMetaComm.smesconnect.mesconnectcheck= (m_mesconnectcheck == TRUE);
}


void CSystemConfig::OnEnChangeEsnHeaderStr()
{
	// TODO:  如果该控件是 RICHEDIT 控件，它将不
	// 发送此通知，除非重写 CDialog::OnInitDialog()
	// 函数并调用 CRichEditCtrl().SetEventMask()，
	// 同时将 ENM_CHANGE 标志“或”运算到掩码中。

	// TODO:  在此添加控件通知处理程序代码
}


void CSystemConfig::OnBnClickedFactoryReset()
{
	UpdateData(TRUE);
	g_sMetaComm.bfactoryreset= (m_factoryreset == TRUE);
}


void CSystemConfig::OnBnClickedReboot()
{
	UpdateData(TRUE);
	g_sMetaComm.breboot= (m_reboot == TRUE);
}


void CSystemConfig::OnBnClickedCheckVersion()
{
	UpdateData(TRUE);
	g_sMetaComm.bcheckversion= (m_checkversion == TRUE);
	((CWnd*)GetDlgItem(IDC_VERSION_STR))->EnableWindow(m_checkversion);
}

void CSystemConfig::OnBnClickedCheckInt()
{
	UpdateData(TRUE);
	g_sMetaComm.bcheckint= (m_CheckInt == TRUE);
	((CWnd*)GetDlgItem(IDC_INT_STR))->EnableWindow(m_CheckInt);
}

void CSystemConfig::OnCbnSelchangeMesselect()
{
	UpdateData(TRUE);
	m_szmesstr.GetLBText(m_szmesstr.GetCurSel(), g_sMetaComm.smesconnect.strMes);
}


void CSystemConfig::OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar)
{
	// TODO: 在此添加消息处理程序代码和/或调用默认值

    SCROLLINFO scrollinfo;
    GetScrollInfo(SB_VERT,&scrollinfo,SIF_ALL);
    int unit=3;        
    switch (nSBCode)  
    {      
    case SB_LINEUP:          //Scroll one line up
        scrollinfo.nPos -= 1;  
        if (scrollinfo.nPos<scrollinfo.nMin)
        {  
            scrollinfo.nPos = scrollinfo.nMin;  
            break;  
        }  
        SetScrollInfo(SB_VERT,&scrollinfo,SIF_ALL);  
        ScrollWindow(0,unit); 
        break;  
    case SB_LINEDOWN:           //Scroll one line down
        scrollinfo.nPos += 1;  
        if (scrollinfo.nPos+scrollinfo.nPage>scrollinfo.nMax)  //此处一定要注意加上滑块的长度，再作判断
        {  
            scrollinfo.nPos = scrollinfo.nMax;  
            break;  
        }  
        SetScrollInfo(SB_VERT,&scrollinfo,SIF_ALL);  
        ScrollWindow(0,-unit);  
        break;  
    case SB_PAGEUP:            //Scroll one page up.
        scrollinfo.nPos -= 5;  
        if (scrollinfo.nPos<=scrollinfo.nMin)
        {  
            scrollinfo.nPos = scrollinfo.nMin;  
            break;  
        }  
        SetScrollInfo(SB_VERT,&scrollinfo,SIF_ALL);  
        ScrollWindow(0,unit*5);  
        break;  
    case SB_PAGEDOWN:        //Scroll one page down        
        scrollinfo.nPos += 5;  
        if (scrollinfo.nPos+scrollinfo.nPage>=scrollinfo.nMax)  //此处一定要注意加上滑块的长度，再作判断
        {  
            scrollinfo.nPos = scrollinfo.nMax;  
            break;  
        }  
        SetScrollInfo(SB_VERT,&scrollinfo,SIF_ALL);  
        ScrollWindow(0,-unit*5);  
        break;  
    case SB_ENDSCROLL:      //End scroll     
        break;  
    case SB_THUMBPOSITION:  //Scroll to the absolute position. The current position is provided in nPos
        break;  
    case SB_THUMBTRACK:                  //Drag scroll box to specified position. The current position is provided in nPos
        ScrollWindow(0,(scrollinfo.nPos-nPos)*unit);  
        scrollinfo.nPos = nPos;  
        SetScrollInfo(SB_VERT,&scrollinfo,SIF_ALL);
        break;  
    }

    CDialog::OnVScroll(nSBCode, nPos, pScrollBar);
}

BOOL CSystemConfig::OnMouseWheel(UINT nFlags, short zDelta, CPoint pt)
{
    // 获取当前滚动条信息
    SCROLLINFO scrollinfo;
    GetScrollInfo(SB_VERT, &scrollinfo, SIF_ALL);

    // 计算滚动步长
    int nScrollLines = 30;

    // 根据滚轮方向确定滚动方向
    // zDelta > 0 表示向上滚动，zDelta < 0 表示向下滚动
    if (zDelta > 0)
    {
        // 向上滚动
        OnVScroll(SB_LINEUP, 0, NULL);
        OnVScroll(SB_LINEUP, 0, NULL);
        OnVScroll(SB_LINEUP, 0, NULL);
    }
    else if (zDelta < 0)
    {
        // 向下滚动
        OnVScroll(SB_LINEDOWN, 0, NULL);
        OnVScroll(SB_LINEDOWN, 0, NULL);
        OnVScroll(SB_LINEDOWN, 0, NULL);
    }

    return TRUE;
}

