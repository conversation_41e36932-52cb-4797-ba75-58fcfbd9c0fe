/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef _META_VERSION_DEFINITION_H_
#define _META_VERSION_DEFINITION_H_

#include "Version.h"
#include <stdio.h>
#include <string.h>
#include <time.h>

#if defined(_WIN32)
#define WRAP_SPRINTF_S sprintf_s
#else
#define WRAP_SPRINTF_S snprintf
#endif

// Fill "vXX.YYYY.ZZ (flavor) (tag) (build date) (branch_info / scm_id)" to buffer
#define VERSION_DETAIL(version_info, buffer, buffer_size) \
    do { \
        char cBuildDate[30] = {0}; \
        int len = WRAP_SPRINTF_S(cBuildDate, sizeof(cBuildDate), "%04d/%02d/%02d %02d:%02d:%02d", \
                                 (version_info).build_date.tm_year + 1900, \
                                 (version_info).build_date.tm_mon + 1, \
                                 (version_info).build_date.tm_mday, \
                                 (version_info).build_date.tm_hour, \
                                 (version_info).build_date.tm_min, \
                                 (version_info).build_date.tm_sec); \
        if (len < 0) len = 0; \
        cBuildDate[len] = '\0'; \
        memset(buffer, 0, buffer_size);\
        len = WRAP_SPRINTF_S(buffer, buffer_size, \
                             "v%d.%d.%d (%s) (%s) (%s) (%s / %s)", \
                             (version_info).major, (version_info).minor, (version_info).revision, \
                             (version_info).version_flavor, (version_info).version_tag, cBuildDate, \
                             (version_info).branch_info, (version_info).version_scm_id); \
        if (len < 0) len = 0; \
        buffer[len] = '\0'; \
    } while (0)


/**
 * \ingroup General
 * \details The enumeration to specify the version structure type
 */
typedef enum META_LIBRARY_VERSION_TYPE
{
    META_LIBRARY_VERSION_V1,    /**< use with `METALibraryVersionInfo` structure */
    META_LIBRARY_VERSION_END
} META_LIBRARY_VERSION_TYPE;
/**
 * \ingroup General
 * \details Structure to hold META tool version information
 */
typedef struct METALibraryVersionInfo
{
    unsigned int major;         /**< major version number */
    unsigned int minor;         /**< release week number */
    unsigned int revision;      /**< revision number */
    char version_flavor[256];   /**< name of build configuration */
    char version_tag[256];      /**< customization name. Leave empty for official version */
    char version_scm_id[40];    /**< unique SHA-1 value for commit (max 40 characters) */
    char branch_info[256];      /**< branch name information */
    struct tm build_date;       /**< library built date (YYYY/MM/DD hh:mm:ss)*/
} METALibraryVersionInfo;

/**
 * Used by library API to get library-wise version information.
 * Remember to fill version build date as well.
 *
 * \code
 * #include "METAVersionDefinition.h"
 * #include "OsMisc.h"
 *
 * METALibraryVersionInfo *version_info = (METALibraryVersionInfo *) out_version_info;
 * FillVersionInfo(version_info);
 * MetaOsAbstractionLayer::OsGetLibraryLastModifyTime(version_info->build_date, <any_funtion_pointer_in_same_library>);
 * \endcode
*/
static inline void FillVersionInfo(METALibraryVersionInfo *version_info)
{
    version_info->major = ST_MAJOR_VERSION;
    version_info->minor = ST_MINOR_VERSION;
    version_info->revision = ST_REVISION;

    int ret = 0;

    memset(version_info->version_flavor, '\0', sizeof(version_info->version_flavor));
    ret = WRAP_SPRINTF_S(version_info->version_flavor, sizeof(version_info->version_flavor) - 1, "%s", VERSION_FLAVOR);
    if (ret > 0)
    {
        version_info->version_flavor[ret] = '\0';
    }

    memset(version_info->version_tag, '\0', sizeof(version_info->version_tag));
    ret = WRAP_SPRINTF_S(version_info->version_tag, sizeof(version_info->version_tag) - 1, "%s", VERSION_TAG);
    if (ret > 0)
    {
        version_info->version_tag[ret] = '\0';
    }

    memset(version_info->version_scm_id, '\0', sizeof(version_info->version_scm_id));
    ret = WRAP_SPRINTF_S(version_info->version_scm_id, sizeof(version_info->version_scm_id) - 1, "%s", VERSION_SCM_ID);
    if (ret > 0)
    {
        version_info->version_scm_id[ret] = '\0';
    }

    memset(version_info->branch_info, '\0', sizeof(version_info->branch_info));
    ret = WRAP_SPRINTF_S(version_info->branch_info, sizeof(version_info->branch_info) - 1, "%s", BRANCH_INFO);
    if (ret > 0)
    {
        version_info->branch_info[ret] = '\0';
    }
}

#endif
