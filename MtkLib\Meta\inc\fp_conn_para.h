/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __FP_CONN_PARA__
#define __FP_CONN_PARA__

#include "meta.h"
#include "conn_para.h"

typedef struct
{
    Conn_Para input_para;
    int *boot_stop;
} FP_Conn_Input;

typedef struct
{
    bool b_shutdown;
    bool b_backup;
    bool b_switchFlag;
} FP_Disconn_Input;

#endif
