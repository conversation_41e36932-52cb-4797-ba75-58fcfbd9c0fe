﻿先在工具文件夹中，准备一份 database.csv 文件
工具文件夹中 有一份示例database.csv文档，您可以用自己的文件覆盖它。文件格式要和它一致
文件第一行，必须要有IMEI1。其他项目如果不写，可以删减。
删减时，第一行标题与后面内容都必须删掉那一列

在工具设置界面，务必勾选所有要写的项目，且项目必须与database.csv一致
例如database.csv如果有barcode，那么设置界面也必须勾选barcode

工具只在启动时，一次性加载database.csv的数据
如果更新了database.csv，必须重启工具，重新加载数据

开始写码时，只需扫码填入IMEI1/Barcode
工具会在数据库中根据IMEI1/Barcode，查询其他要写的码，并自动写入

扫描IMEI1还是Barcode，请在工具中设置 Scan Code Type = 0(IMEI1),1(barcode)

database.csv 中的mac地址格式可以是下列三种：
00-11-22-33-44-55
00:11:22:33:44:55
001122334455
