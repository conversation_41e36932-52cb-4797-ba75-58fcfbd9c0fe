/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __CONN_PARA_H__
#define __CONN_PARA_H__

#include "meta.h"

typedef void (*StageNameCB)(const char *stage_names[], unsigned int stage_map);
typedef void (*StageProgressCB)(int current_stage_index, int current_stage_progress);

#define IP_ADDRESS_LEN 60

typedef enum
{
    META_PLATFORM_TYPE_SMARTPHONE = 0,
    META_PLATFORM_TYPE_XL1SIM = 1,
    META_PLATFORM_TYPE_SMARTPHONE_TW = 2,
    META_PLATFORM_TYPE_SMARTPHONE_GED = 3,
    META_PLATFORM_TYPE_DUT_IN_META_MODE = 4,
    META_PLATFORM_TYPE_ANDROID_TEST_MODE = 5,
    META_PLATFORM_TYPE_FLASHLESS = 6,
    META_PLATFORM_TYPE_DATACARD = 7,
    META_PLATFORM_TYPE_CPE = 8,
    META_PLATFORM_TYPE_END
} META_PLATFORM_TYPE;

typedef struct
{
    bool                isAuto;
    META_COMPORT_INFO   portInfo;
} METAAPP_COMPORT_INFO;

typedef struct
{
    char  *ipAddress;
    int    loggingPort;
} METAAPP_SOCKET_INFO;

typedef struct
{
    int metaTimeout;
    int mdLogBufferFlushWaitTime;
} META_WAIT_TIME;

typedef struct
{
    METAAPP_COMPORT_INFO    preloaderPort;
    METAAPP_COMPORT_INFO    metaPort;
    METAAPP_COMPORT_INFO    loggingPort;
    METAAPP_SOCKET_INFO     socketInfo;         /**< [META] Wi-Fi connection parameters */
    META_COMM_BAUDRATE      baudrate;           /**< [META] META stage baudrate polling array, it must end with META_BAUD_END. */
    META_IO_CHANNEL_TYPE    ioChannelType;
    META_PLATFORM_TYPE      platformType;       /**< [META] Specify the platform type to connect META */
    META_AP_TRACE_MODE      apTraceMode;        /**< [META} AP META mode trace parameters */
    META_MODEM_TRACE_PARA_T modemTrace;         /**< [META] Modem META mode trace parameters */
    META_COMM_TRACE_PARA_T  commTrace;          /**< [META] META dump com port raw data */
    META_SysTrace_CallBack  sysTraceCB;         /**< [META] MODEM System trace handler */
    void                   *sysTraceCBArg;      /**< [META] Argument for MODEM System trace handler */
    StageNameCB             stageNameCB;        /**< [META] Get connection stage names */
    StageProgressCB         stageProgressCB;    /**< [META] Get connection stage progress */
    unsigned int            connectTimeoutInMs; /**< [META] META stage sync timeout value (after BootROM negotiation pass) */
    META_WAIT_TIME          waitTime;            /**< [META] META Command waiting time */
} Conn_Para;

#endif
