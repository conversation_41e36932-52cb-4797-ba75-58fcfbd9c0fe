; NOTE: this file should be edited when SN_Writer tool was closed

[System Setup]
Stress Test = 0

; mobile/tablet/... had in meta mode
Already in Meta = False

; keep in meta mode when finish write
Keep in Meta = False

Clear Meta Boot Flag = False
Operator Mode = 0
Preloader Connect Timeout = 60000
Kernel Connect Timeout = 120000
Target type = 0

; com port scan filter, white list, use space/tab/comma/semicolon to split multi-filters
Brom Port Filter = VID_0E8D&PID_0003
Preloader Port Filter = VID_0E8D&PID_2000 VID_0525&PID_A4A7 VID_1004&PID_6000
Kernel Port Filter = VID_0E8D&PID_2007 VID_0E8D&PID_2006&MI_02 VID_0E8D&PID_2040&MI_02 VID_0BB4&PID_0005&MI_02 VID_1004&PID_6000 VID_0E8D&PID_202D&MI_01 VID_0E8D&PID_200E&MI_01 VID_0E8D&PID_2026&MI_02 VID_0E8D&PID_2040&MI_02 VID_0E8D&PID_7101&MI_00

; enable adb port
Composite Device Enable = False

Usb Enable = True
USBSwitchTool Enable = False
DualTalk Enable = False
Security USB Enable = False
Skip Write Prod_Info Enable = False
Check BackupNv to PC Enable = False
Check Fastboot OEM Lock Enable = False
Check Calibration flag Enable = False
Check FinalTest flag Enable = False
Write Meid = True
Write Esn = False
Write Barcode = True
Write IMEI = True
Write Serial No. = False
Write BT = True
Write Wifi = True
Write Hdcp = False
Write DRMKey = False
Install Hdcp Data = False
Write Ethernet Mac Address = False
Write DRMkey MCID = False
Write Attestation Key = False
IMEI Nums = 1
IMEI CheckSum = True
IMEI Lock = False
DualIMEI same = False
IMEI_1 Check header = False
IMEI_2 Check header = False
IMEI_3 Check header = False
IMEI_4 Check header = False
Meid Check header = False
Esn Check header = False
Serial No. Check header = False
Barcode Check header = False
BT Check header = False
Wifi Check header = False
Ethernet Check header = False
DRMKey MCID Check header = False
Enable Check BTWifi = False
IMEI_1 header string =
IMEI_2 header string =
IMEI_3 header string =
IMEI_4 header string =
Meid header string =
Esn header string =
Serial No. header string =
Barcode header string =
BT header string =
Wifi header string =
Ethernet header string =
DRMKey MCID header string =
AP DB from DUT = False
MD DB from DUT = False
Modem_1 database path =
Modem_2 database path =
AP database path =
SP Auth path =
Hdcp Bin path =
DrmKey path =
Hdcp data path =
Hdcp cek path =
Attestation Key Path =
Lock OTP = False
Log Dir = C:\SNWriter_LOG\
ModemLog = False
Enable Serial No = False
Enable Lock OTP = False
Ignore Database Inconsistent = False
Serial Num From IMEI = False
IMEI Lock Hidden = True
