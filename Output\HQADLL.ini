[Settings]
DebugMode = 6
DumpDebug = 1;
PrintCmdRaw = 0
;AssignIC=7925;


FullBandFFTMode = 0
ComPortNumber = 9
BaudRate = 921600;
WriteCommandDelay = 10
WaitReadCommTimeoutCount = 500;

;CmdDump = 1;

EthCmdRetry = 5;
CommandTimeout = 5000; 
Assignadapter = 0;    
DLLeFuseRW = 0;
BFEnable = 1;                       
BFSoundingRateNDPA = 12;
BFSoundingRateNDP = 12;
3883TXPower0 = 15;
3883TXPower1 = 15;
3883TXPower2 = 15;
SupportAPSOCOldCmd = 0;
ReCalWaitTime = 0;
LoadFWFromUI = 0;
QACmdForEfuse = 1;
APIP = *************
APUser = admin
APPwd = admin
ated = ated
dmesg = dmesg -c -s 50000
WiFiPower = 1
WiFiPowerCR = 0000000
WiFiPowerOnValue = 42424242
NVRAMDataDetails = NVRAMDataDetails.csv
RxVectorSetting = Rx_vector.csv
RxVectorResult = Rx_vector_Result.csv
RxVectorDump = C:\dump_RXV.txt
RxVectorDumpDone = C:\dump_RXV_done.txt
WinRxVectorDump = C:\Windows\RxVectorDump.txt
WinRxVectorDumpDone = C:\Windows\RxVectorDumpFinish.txt
RDDDump = C:\rdd_pulse.txt
RDDDumpDone = C:\rdd_pulse_done.txt
WinRDDDump = C:\Windows\rdd_pulse.txt
WinRDDDumpDone = C:\Windows\rdd_pulse_done.txt
RecalDumpDone = C:\recal_done.txt
WinRecalDump = C:\Windows\RecalDump.txt
WinRecalDumpDone = C:\Windows\RecalDumpFinish.txt
WinIQDump = C:\Windows\IQDump.txt
WinIQDumpDone = C:\Windows\IQDumpFinish.txt
DumpDebug = 1
RecalDumpStopCR = 0xffffffff
LoadScriptLog = 1
;If null default location same with HQADLL.
CalDumpOutFolder = 
CalID0 = RC_CAL.txt
CalID1 = RX_RSSI_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_DPD_RX_FI_FD_MPM.txt
CalID4 = TX_DPD_SCAN_HPM.txt
CalID5 = RX_FI.txt
CalID6 = RX_FD.txt
CalID7 = DPD.txt
CalID8 = TX_LPFG.txt
CalID9 = TX_IQ.txt
CalID10 = TX_LC_IQM.txt
CalID11 = TX_LC_PGA.txt
CalID12 = TX_LC_TANK.txt
CalID13 = SX_CAL.txt
CalID14 = RX_FD_FI.txt
CalID15 = iBF.txt
CalID16 = TOAE.txt
CalID30 = CAL_ALL.txt
CalID31 = CAL_ALL.txt

DefaultDeviceType = 1
ErrorCheck = 1

[Settings_MT0035]
AXRXOKCal = 1

[Settings_MT0037]
11BE = 1;
FFTShow = 1
11AX = 1;
nonLegacyPower = 1
CommandTimeout = 2000;
APIP = *************
APUser = boli
APPwd = 1
CmdDump = 1;
DLLCalRXInfo = 1
UNIFIEDCMD = 1
SupportBinFile = 0
BufferBinFile = eeprom_6632.bin
RxVectorSetting = Rx_vector_6632.csv

CalDumpOutFolder = 
CalID0 = RC_CAL.txt
CalID1 = RX_RSSI_DCOC_CAL.txt
CalID2 = RX_DCOC_CAL.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_PA_BIAS.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_DPD.txt
CalID30 = ALL.txt

;-- ICAP Setting----[START] 2021/07/09
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 2
FFTTOTWIFI = 2

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 11
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto

FFTNodeCount = 33
// RX, 1R, PHY_DOMAIN
FFTNode0 = 0x10400000:WF0 AFIFO
FFTNode1 = 0x10401000:WF1 AFIFO
FFTNode2 = 0x10500000:WF0 RXFI
FFTNode3 = 0x10501000:WF1 RXFI
FFTNode4 = 0x10600000:WF0 RXFD
FFTNode5 = 0x10601000:WF1 RXFD
FFTNode6 = 0x10700000:WF0 DLPF
FFTNode7 = 0x10701000:WF1 DLPF
FFTNode8 = 0x10800000:WF0 AGC DGC
FFTNode9 = 0x10801000:WF1 AGC DGC
// RX, 2R, PHY_DOMAIN
FFTNode10 = 0x10404000:WF1/0 AFIFO
FFTNode11 = 0x10504000:WF1/0 RXFI
FFTNode12 = 0x10604000:WF1/0 RXFD
FFTNode13 = 0x10704000:WF1/0 DLPF
FFTNode14 = 0x10804000:WF1/0 AGC DGC
// RX, 1R, ADC_DOMAIN
FFTNode15 = 0x00000000:WF0 ADC
FFTNode16 = 0x00001000:WF1 ADC
FFTNode17 = 0x00200000:WF0 NYF
FFTNode18 = 0x00201000:WF1 NYF
FFTNode19 = 0x00300000:WF0 DCRF
FFTNode20 = 0x00301000:WF1 DCRF
// RX, 2R, ADC_DOMAIN

FFTNode21 = 0x00004000:WF1/0 ADC
FFTNode22 = 0x00D04000:WF0 ADC 640M
FFTNode23 = 0x00D04000:WF1 ADC 640M
FFTNode24 = 0x00204000:WF1/0 NYF
FFTNode25 = 0x00304000:WF1/0 DCRF
//TX, 1T , TX_DOMAIN
FFTNode26 = 0x2090F000:WF0 PKLIM 1x
FFTNode27 = 0x20910000:WF1 PKLIM 1x
FFTNode28 = 0x20A0F000:WF0 CIC-1 3x
FFTNode29 = 0x20A10000:WF1 CIC-1 3x
;//TX, 2T , TX_DOMAIN
FFTNode30 = 0x20913000:WF1/0 PKLIM 1x
FFTNode31 = 0x20A13000:WF1/0 CIC-1 3x
;// Thermal
FFTNode32 = 0x40B20000:WF1/0 Thermal



//ADC
FFTADCBW05 = 320
FFTADCBW10 = 320
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640

GraphScaleFFTADCBW05 = 25
GraphScaleFFTADCBW10 = 25
GraphScaleFFTADCBW20 = 25
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5


//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50


//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50

//PKLIM
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50


//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW80 = 480

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25

;//-- ICAP Setting----[END] 2021/07/09
[Settings_MT3107]
DBDCChip = 1;
;TBTCChip = 1;
11BE = 1;

[Settings_MT66310]
CommandTimeout = 2000;
APIP = *************
APUser = boli
APPwd = 1
CmdDump = 1;
DLLCalRXInfo = 1
UNIFIEDCMD = 1
SupportBinFile = 0
BufferBinFile = eeprom_6632.bin
RxVectorSetting = Rx_vector_6632.csv

CalDumpOutFolder = 
CalID0 = RC_CAL.txt
CalID1 = RX_RSSI_DCOC_CAL.txt
CalID2 = RX_DCOC_CAL.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_PA_BIAS.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_DPD.txt
CalID30 = ALL.txt

//ICAP_Setting-Sart
//0:hide,1:show,2:disalbe
FFT_TrigEvent_Show = 2
FFTTOTWIFI        = 2
//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 160
FFTADCBW80 = 160

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

FFTEventCount = 11
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto

FFTNodeCount = 6
FFTNode0 = 0x10000006:ADC
FFTNode1 = 0x03:DCOC
FFTNode2 = 0x08:FDIQ
FFTNode3 = 0x09:FIIQ
FFTNode4 = 0x4D:AFIFO
FFTNode5 = 0x0B:WFSpectrum
//ICAP_Setting-End
[Settings_MT6631]
APIP = ************
APUser = mtk
APPwd = mtk
ated = ated
dmesg = dmesg -c

DLLCalRXInfo = 1
UNIFIEDCMD = 1
SupportBinFile = 0
BufferBinFile = eeprom_6632.bin
EthCmdRetry = 10;
CommandTimeout = 10;
FWCumCount = 1
FFTTOTWIFI	= 1;

//ADC
FFTADCBW20 = 80
FFTADCBW40 = 80
FFTADCBW80 = 160
FFTADCBW160NC = 160
FFTADCBW160C = 160

//IQC
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160NC = 160
FFTIQCBW160C = 160

[Settings_MT6632]
CommandTimeout = 2000;
APIP = *************
APUser = boli
APPwd = 1
CmdDump = 1;
DLLCalRXInfo = 1
UNIFIEDCMD = 1
SupportBinFile = 1
BufferBinFile = eeprom_6632.bin
RxVectorSetting = Rx_vector_6632.csv

CalDumpOutFolder = 
CalID0 = RC_CAL.txt
CalID1 = RX_RSSI_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_DPD_RX_FI_FD_MPM.txt
CalID4 = TX_DPD_SCAN_HPM.txt
CalID5 = RX_FI.txt
CalID6 = RX_FD.txt
CalID7 = DPD.txt
CalID8 = TX_LPFG.txt
CalID9 = TX_IQ.txt
CalID10 = TX_LC_IQM.txt
CalID11 = TX_LC_PGA.txt
CalID12 = TX_LC_TANK.txt
CalID13 = SX_CAL.txt
CalID14 = RX_FD_FI.txt
CalID15 = iBF.txt
CalID16 = TOAE.txt
CalID17 = Fdiq.txt
CalID30 = CAL_ALL.txt
CalID31 = CAL_ALL.txt

//ICAP_Setting-Sart
//0:hide,1:show,2:disalbe
FFT_TrigEvent_Show = 2

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 160
FFTADCBW80 = 160

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

FFTEventCount = 11
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto

FFTNodeCount = 6
FFTNode0 = 0x10000006:ADC
FFTNode1 = 0x03:DCOC
FFTNode2 = 0x08:FDIQ
FFTNode3 = 0x09:FIIQ
FFTNode4 = 0x4D:AFIFO
FFTNode5 = 0x0B:WFSpectrum
//ICAP_Setting-End

[Settings_MT6653]
11AX = 1;
11BE = 1;
nonLegacyPower = 1
CommandTimeout = 2000;
APIP = *************
APUser = boli
APPwd = 1
EFuseSize = 3584;
CmdDump = 1;
DLLCalRXInfo = 1
UNIFIEDCMD = 1
SupportBinFile = 0
BufferBinFile = eeprom_6635.bin
RxVectorSetting = Rx_vector_6635.csv

CalDumpOutFolder =
CalID0 = RC_CAL.txt
CalID1 = RX_RSSI_DCOC_CAL.txt
CalID2 = RX_DCOC_CAL.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_PA_BIAS.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_DPD.txt
CalID30 = ALL.txt
 
//FFT Full Band
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 1
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=E:\MT6639_NPTtool\1_MT6639_NPTrawdata
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=1
FFT_NODE=0x10504000:WF1/0 RXFI

NPT_2G_PHY_Index = 0
NPT_5G_PHY_Index = 1
NPT_6G_PHY_Index = 1

CaptureTime = 200;
CaptureStopLen = 50;

FFT_CAP_TIMEOUT_SEC=25
CallTestMode = 0;

//-- ICAP Setting----[START] 2021/03/11
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 2
FFTShow = 1
 
ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
ThermalBit = 8
 
 
FFTEventCount = 11
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
 
FFTNodeCount = 44
// RX, 1R, PHY_DOMAIN
FFTNode0 = 0x10400000:WF0 AFIFO
FFTNode1 = 0x10401000:WF1 AFIFO
FFTNode2 = 0x10500000:WF0 RXFI
FFTNode3 = 0x10501000:WF1 RXFI
FFTNode4 = 0x10600000:WF0 RXFD
FFTNode5 = 0x10601000:WF1 RXFD
FFTNode6 = 0x10700000:WF0 DLPF
FFTNode7 = 0x10701000:WF1 DLPF
FFTNode8 = 0x10800000:WF0 AGC_DGC
FFTNode9 = 0x10801000:WF1 AGC_DGC
// RX, 2R, PHY_DOMAIN
FFTNode10 = 0x10404000:WF1/0 AFIFO
FFTNode11 = 0x10504000:WF1/0 RXFI
FFTNode12 = 0x10604000:WF1/0 RXFD
FFTNode13 = 0x10704000:WF1/0 DLPF
FFTNode14 = 0x10804000:WF1/0 AGC_DGC
// RX, 1R, ADC_DOMAIN
FFTNode15 = 0x00000000:WF0 ADC
FFTNode16 = 0x00001000:WF1 ADC
FFTNode17 = 0x00200000:WF0 DCRF
FFTNode18 = 0x00201000:WF1 DCRF
FFTNode19 = 0x00300000:WF0 DCRF
FFTNode20 = 0x00301000:WF1 DCRF
// RX, 2R, ADC_DOMAIN
FFTNode21 = 0x00004000:WF1/0 ADC
FFTNode22 = 0x00204000:WF1/0 NYF
FFTNode23 = 0x00304000:WF1/0 DCRF
//TX, 1T , TX_DOMAIN
FFTNode24 = 0x2090F000:WF0 PKLIM 1x
FFTNode25 = 0x20910000:WF1 PKLIM 1x
FFTNode26 = 0x20A0F000:WF0 CIC-1 3x
FFTNode27 = 0x20A10000:WF1 CIC-1 3x
FFTNode28 = 0x20E0F000:WF0 DPD_COMP_IN_3x
FFTNode29 = 0x20E10000:WF1 DPD_COMP_IN_3x
FFTNode30 = 0x20F0F000:WF0 DPD_COMP_OUT_3x
FFTNode31 = 0x20F10000:WF1 DPD_COMP_OUT_3x
FFTNode32 = 0x2100F000:WF0 FLAT_COMP_OUT_3x
FFTNode33 = 0x21010000:WF1 FLAT_COMP_OUT_3x
// TX, 2T , TX_DOMAIN
FFTNode34 = 0x20913000:WF1/0 PKLIM 1x
FFTNode35 = 0x20A13000:WF1/0 CIC-1 3x
FFTNode36 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode37 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode38 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
// Thermal, 2 path
FFTNode39 = 0x40B20000:WF1/0 Thermal    
// RX, 1R, OTFK_DOMAIN
FFTNode40 = 0x60000000:WF0 R_Series ADC
FFTNode41 = 0x60200000:WF0 R_Series NYF
FFTNode42 = 0x70500000:WF0 R_Series RXFI
FFTNode43 = 0x70600000:WF0 R_Series RXFD

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 160
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280
  
GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 50
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25
 
//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640
 
GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5
 
//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320
 
GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25
 
//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320
 
GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25
 
//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320
 
GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25
 
//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960
 
GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333
 
//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160
 
GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

[Settings_MT6639]
11AX = 1;
11BE = 1;
nonLegacyPower = 1
CommandTimeout = 2000;
APIP = *************
APUser = boli
APPwd = 1
EFuseSize = 3584;
CmdDump = 1;
DLLCalRXInfo = 1
UNIFIEDCMD = 1
SupportBinFile = 0
BufferBinFile = eeprom_6639.bin
RxVectorSetting = Rx_vector_6639.csv

CalDumpOutFolder =
CalID0 = RC_CAL.txt
CalID1 = RX_RSSI_DCOC_CAL.txt
CalID2 = RX_DCOC_CAL.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_PA_BIAS.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_DPD.txt
CalID30 = ALL.txt
 
//FFT Full Band
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 1
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=E:\MT6639_NPTtool\1_MT6639_NPTrawdata
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=1
FFT_NODE=0x10504000:WF1/0 RXFI

NPT_2G_PHY_Index = 0
NPT_5G_PHY_Index = 1
NPT_6G_PHY_Index = 1

CaptureTime = 200;
CaptureStopLen = 50;

FFT_CAP_TIMEOUT_SEC=25
CallTestMode = 0;

//-- ICAP Setting----[START] 2021/03/11
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 2
FFTShow = 1
 
ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
ThermalBit = 8
 
 
FFTEventCount = 11
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
 
FFTNodeCount = 42
// RX, 1R, PHY_DOMAIN
FFTNode0 = 0x10400000:WF0 AFIFO
FFTNode1 = 0x10401000:WF1 AFIFO
FFTNode2 = 0x10500000:WF0 RXFI
FFTNode3 = 0x10501000:WF1 RXFI
FFTNode4 = 0x10600000:WF0 RXFD
FFTNode5 = 0x10601000:WF1 RXFD
FFTNode6 = 0x10700000:WF0 DLPF
FFTNode7 = 0x10701000:WF1 DLPF
FFTNode8 = 0x10800000:WF0 AGC_DGC
FFTNode9 = 0x10801000:WF1 AGC_DGC
// RX, 2R, PHY_DOMAIN
FFTNode10 = 0x10404000:WF1/0 AFIFO
FFTNode11 = 0x10504000:WF1/0 RXFI
FFTNode12 = 0x10604000:WF1/0 RXFD
FFTNode13 = 0x10704000:WF1/0 DLPF
FFTNode14 = 0x10804000:WF1/0 AGC_DGC
// RX, 1R, ADC_DOMAIN
FFTNode15 = 0x00000000:WF0 ADC
FFTNode16 = 0x00001000:WF1 ADC
FFTNode17 = 0x00D04000:WF0 ADC_1280M_BW320
FFTNode18 = 0x00D04001:WF1 ADC_1280M_BW320
FFTNode19 = 0x00200000:WF0 NYF
FFTNode20 = 0x00201000:WF1 NYF
FFTNode21 = 0x00300000:WF0 DCRF
FFTNode22 = 0x00301000:WF1 DCRF
// RX, 2R, ADC_DOMAIN
FFTNode23 = 0x00004000:WF1/0 ADC
FFTNode24 = 0x00204000:WF1/0 NYF
FFTNode25 = 0x00304000:WF1/0 DCRF
//TX, 1T , TX_DOMAIN
FFTNode26 = 0x2090F000:WF0 PKLIM 1x
FFTNode27 = 0x20910000:WF1 PKLIM 1x
FFTNode28 = 0x20A0F000:WF0 CIC-1 3x
FFTNode29 = 0x20A10000:WF1 CIC-1 3x
FFTNode30 = 0x20E0F000:WF0 DPD_COMP_IN_3x
FFTNode31 = 0x20E10000:WF1 DPD_COMP_IN_3x
FFTNode32 = 0x20F0F000:WF0 DPD_COMP_OUT_3x
FFTNode33 = 0x20F10000:WF1 DPD_COMP_OUT_3x
FFTNode34 = 0x2100F000:WF0 FLAT_COMP_OUT_3x
FFTNode35 = 0x21010000:WF1 FLAT_COMP_OUT_3x
// TX, 2T , TX_DOMAIN
FFTNode36 = 0x20913000:WF1/0 PKLIM 1x
FFTNode37 = 0x20A13000:WF1/0 CIC-1 3x
FFTNode38 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode39 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode40 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
// Thermal, 2 path
FFTNode41 = 0x40B20000:WF1/0 Thermal    
 
 
//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280
  
GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25
 
//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640
 
GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5
 
//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320
 
GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25
 
//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320
 
GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25
 
//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320
 
GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25
 
//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960
 
GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333
 
//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160
 
GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50
 
//-- ICAP Setting----[END] 2022/03/11
[Settings_MT6867]
CalID0 = RC_CAL
CalID1 = RSSIADC_DCOC
CalID2 = RX_DCOC
CalID3 = TX_TSSI_DCOC
CalID4 = TX_LPFG
CalID5 = TX_FDIQ
CalID6 = TX_DCIQ
CalID7 = TX_PA_BIAS
CalID8 = RX_FDIQ
CalID9 = RX_FIIQ
CalID10 = RX_FD_FI
CalID11 = TX_DPD
CalID30 = POR_CAL_DUMP
CalID31 = ALL

FFT_TrigEvent_Show = 2
FFT_Band_Show = 2
FFT_TriggerPhy_Show = 2
FFT_CaptureSource_Show = 1
FFTTOTWIFI        = 2

ADCBit = 12
IQCBit = 14

//ADC
FFTADCBW05 = 320
FFTADCBW10 = 320
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320

GraphScaleFFTADCBW05 = 25
GraphScaleFFTADCBW10 = 25
GraphScaleFFTADCBW20 = 25
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50

FFTEventCount = 18
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xFFA55555:TXFD_Cal_Enable
FFTEvent12 = 0xFE555555:TXFI_Cal_Enable
FFTEvent13 = 0x555B5555:TXLPFG_Cal_Enable
FFTEvent14 = 0x557E5555:RXFI_Cal_Enable
FFTEvent15 = 0xAA955555:RXFD_Cal_Enable
FFTEvent16 = 0xF9555555:RXHWAGC_Cal_Enable
FFTEvent17 = 0x555555FE:TXDPD_Cal_Enable

FFTNodeCount = 7
FFTNode0 = 0x82:PACKED ADC
FFTNode1 = 0x87:DCRF
FFTNode3 = 0x85:NYF
FFTNode4 = 0x8B:FDIQ
FFTNode5 = 0x89:FIIQ
FFTNode6 = 0x8D:AFIFO






[Settings_MT7603]
QACmdForEfuse = 0;

[Settings_MT7622]
TxRxPathByte = 2;
DBDCChip = 0
FFTTOTWIFI	= 4;
FFT_TrigEvent_Show = 2
FFTADCBW05 = 480
FFTADCBW10 = 480
FFTADCBW20 = 480
FFTADCBW40 = 480

FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80

FFTNodeCount = 5
FFTNode0 = 0x5D:ADC
FFTNode1 = 0x5E:DCOC
FFTNode2 = 0x48:FDIQ
FFTNode3 = 0x49:FIIQ
FFTNode4 = 0x60:AFIFO

FFTEventCount = 11
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto

[Settings_MT7626]
EFuseSize = 1024;
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI        = 3

ADCBit = 12
IQCBit = 14

//ADC
FFTADCBW05 = 320
FFTADCBW10 = 320
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320

GraphScaleFFTADCBW05 = 25
GraphScaleFFTADCBW10 = 25
GraphScaleFFTADCBW20 = 25
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50

FFTEventCount = 18
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xFFA55555:TXFD_Cal_Enable
FFTEvent12 = 0xFE555555:TXFI_Cal_Enable
FFTEvent13 = 0x555B5555:TXLPFG_Cal_Enable
FFTEvent14 = 0x557E5555:RXFI_Cal_Enable
FFTEvent15 = 0xAA955555:RXFD_Cal_Enable
FFTEvent16 = 0xF9555555:RXHWAGC_Cal_Enable
FFTEvent17 = 0x555555FE:TXDPD_Cal_Enable

FFTNodeCount = 7
FFTNode0 = 0x06:WF0/1ADC
FFTNode1 = 0x76:WF1/2ADC
FFTNode2 = 0x77:WF0/2ADC
FFTNode3 = 0x71:NYF
FFTNode4 = 0x75:FDIQ
FFTNode5 = 0x74:FIIQ
FFTNode6 = 0x73:AFIFO

[Settings_MT7628]
QACmdForEfuse = 0;

[Settings_MT7629]
EFuseSize = 1408;

[Settings_MT7636]
QACmdForEfuse = 0;

[Settings_MT7637]
BufferBin = 1


[Settings_MT7663]
ErrorCheck = 0
CmdDump = 1
UNIFIEDCMD = 1
SupportBinFile = 1
EFuseSize = 1536;
BufferBinFile = eeprom_7663.bin
DLLCalRXInfo = 1
RxVectorSetting = Rx_vector_7663.csv

//FFT
FFTShow = 1
FFT_TrigEvent_Show = 2
FFTTOTWIFI	= 2;

//FFT Full Band
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode= 0
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=D:\temp\result
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=0
FFT_NODE=0x08:FDIQ
FFT_CAP_TIMEOUT_SEC=25

//FFT Test Time Delay
FFTTimeDelayCount = 0
FFTTD0=0
FFTTD1=10
FFTTD2=70

//ADC
FFTADCBW05 = 320
FFTADCBW10 = 320
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320

GraphScaleFFTADCBW05 = 26.666667
GraphScaleFFTADCBW10 = 26.666667
GraphScaleFFTADCBW20 = 26.666667
GraphScaleFFTADCBW40 = 26.666667
GraphScaleFFTADCBW80 = 26.666667

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50

FFTEventCount = 18
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xFFA55555:TXFD_Cal_Enable
FFTEvent12 = 0xFE555555:TXFI_Cal_Enable
FFTEvent13 = 0x555B5555:TXLPFG_Cal_Enable
FFTEvent14 = 0x557E5555:RXFI_Cal_Enable
FFTEvent15 = 0xAA955555:RXFD_Cal_Enable
FFTEvent16 = 0xF9555555:RXHWAGC_Cal_Enable
FFTEvent17 = 0x555555FE:TXDPD_Cal_Enable

FFTNodeCount = 5
FFTNode0 = 0x06:ADC
FFTNode1 = 0x46:NYF
FFTNode2 = 0x08:FDIQ
FFTNode3 = 0x09:FIIQ
FFTNode4 = 0x4D:AFIFO

CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_PA_BIAS.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_DPD.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7668]

DLLCalRXInfo = 1
//UseDriverReturn = 1
UNIFIEDCMD = 1
SupportBinFile = 1
ErrorCheck = 0;
TxRxPathByte = 2
BufferBinFile = eeprom_7668.bin
;UIShowSubChip = 1
FFTTOTWIFI	= 2;
//ADC
FFTADCBW20 = 80
FFTADCBW40 = 80
FFTADCBW80 = 160
FFTADCBW160NC = 160
FFTADCBW160C = 160

//IQC
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160NC = 160
FFTIQCBW160C = 160

[Settings_MT7790]
APIP = *************
APUser = mediatek
APPwd = mediatek
ated = ated
UNIFIEDCMD = 1
FFT_Node_TOAE = 0
FFT_Node_SPECTRUM = 0
FFT_Node_RBIST = 0
RxVectorSetting = Rx_vector_7790.csv

[Settings_MT7902]
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 2048;
//1536;
11AX = 1;
;BufferBinFile = eeprom_7663.bin
;RxVectorSetting = Rx_vector_7915.csv
11BE = 1;
//FFT
FFTShow = 1
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 2
FFTTOTWIFI = 1

//FFT Full Band
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 0
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=D:\temp\result
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=0
FFT_NODE=0x15141002:WF1/0 RXFI
FFT_CAP_TIMEOUT_SEC=25

//FFT Disturb Detect
FFT_DISTURB_DETECT=0
FFT_DISTURB_DETECT_RETRY = 1
FFT_DISTURB_DETECT_HIDE_CHART = 0
FFT_DISTURB_DETECT_STOP = 0
FFT_DIST_DET_WF0_2G=-93
FFT_DIST_DET_WF0_2G_RANGE=2
FFT_DIST_DET_WF1_2G=-93
FFT_DIST_DET_WF1_2G_RANGE=2
FFT_DIST_DET_WF0_5G=-93
FFT_DIST_DET_WF0_5G_RANGE=2
FFT_DIST_DET_WF1_5G=-93
FFT_DIST_DET_WF1_5G_RANGE=2
FFT_DIST_DET_WF0_6G=-93
FFT_DIST_DET_WF0_6G_RANGE=2
FFT_DIST_DET_WF1_6G=-93
FFT_DIST_DET_WF1_6G_RANGE=2

//FFT Test Time Delay
FFTTimeDelayCount = 0

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 11
// RX, 1R, PHY_DOMAIN 
FFTNode0 = 0x14000000:WF0 AFIFO
FFTNode1 = 0x15000000:WF0 RXFI
FFTNode2 = 0x16000000:WF0 RXFD
FFTNode3 = 0x17000000:WF0 DLPF
FFTNode4 = 0x18000000:WF0 AGC DGC
// RX, 1R, ADC_DOMAIN 
FFTNode5 = 0x00000000:WF0 ADC
FFTNode6 = 0x02000000:WF0 NYF
FFTNode7 = 0x03000000:WF0 DCRF
FFTNode8 = 0x0D000000:WF0 ADC 640M
//TX, 1T , TX_DOMAIN 
FFTNode9 = 0x29030000:WF0 PKLIM 1x 
FFTNode10 = 0x2A030000:WF0 CIC-1 3x


//ADC
//FFTADCBW05 = 320
//FFTADCBW10 = 320
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640

//GraphScaleFFTADCBW05 = 25
//GraphScaleFFTADCBW10 = 25
GraphScaleFFTADCBW20 = 25
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5

//DLPF
//FFTDLPFBW05 = 10
//FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160

//GraphScaleFFTDLPFBW05 = 800
//GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50


//AGCDGC
//FFTAGCDGCBW05 = 10
//FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160

//GraphScaleFFTAGCDGCBW05 = 800
//GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50

//PKLIM-1x
//FFTPKLIMBW05 = 20
//FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160

//GraphScaleFFTPKLIMBW05 = 400
//GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50


//CIC-3x
//FFTCICBW05 = 60
//FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW80 = 480

//GraphScaleFFTCICBW05 = 133.33333
//GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666

//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25


CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt
[Settings_MT7903]
Band0TxAIDOffSet = 3A9
Band1TxAIDOffSet = 3A9
Band2TxAIDOffSet = 3A9

ATEMode = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 7680
//3776;
11AX = 1;
DBDCChip = 1;
11BE = 1;
;BufferBinFile = eeprom_7663.bin
;RxVectorSetting = Rx_vector_7915.csv

//FFT
FFTShow = 1
;RxVectorSetting = Rx_vector_7915.csv
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 4

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 45
// RX, 2R, PHY_DOMAIN
FFTNode0 = 0x10404000:WF1/0 AFIFO
FFTNode1 = 0x10409000:WF3/2 AFIFO
FFTNode2 = 0x10504000:WF1/0 RXFI
FFTNode3 = 0x10509000:WF3/2 RXFI
FFTNode4 = 0x10604000:WF1/0 RXFD
FFTNode5 = 0x10609000:WF3/2 RXFD
FFTNode6 = 0x10704000:WF1/0 DLPF 
FFTNode7 = 0x10709000:WF3/2 DLPF
FFTNode8 = 0x10804000:WF1/0 AGCDGC
FFTNode9 = 0x10809000:WF2/3 AGCDGC
// RX, 4R, PHY_DOMAIN
FFTNode10 = 0x1040E000:4-Way AFIFO
FFTNode11 = 0x1050E000:4-Way RXFI
FFTNode12 = 0x1060E000:4-Way RXFD
FFTNode13 = 0x1070E000:4-Way DLPF
FFTNode14 = 0x1080E000:4-Way AGCDGC
// RX, 2R, ADC_DOMAIN
FFTNode15 = 0x00004000:WF1/0 ADC
FFTNode16 = 0x00009000:WF3/2 ADC
FFTNode17 = 0x00D00000:WF0 ADC_1280M_BW320
FFTNode18 = 0x00D01000:WF1 ADC_1280M_BW320
FFTNode19 = 0x00D02000:WF2 ADC_1280M_BW320
FFTNode20 = 0x00D03000:WF3 ADC_1280M_BW320
FFTNode21 = 0x00204000:WF1/0 NYF
FFTNode22 = 0x00209000:WF3/2 NYF
FFTNode23 = 0x00304000:WF1/0 DCRF
FFTNode24 = 0x00309000:WF3/2 DCRF
// RX, 4R, ADC_DOMAIN
FFTNode25 = 0x0020E000:4-Way NYF
FFTNode26 = 0x0030E000:4-Way DCRF
// TX, 2T , TX_DOMAIN
FFTNode27 = 0x20913000:WF1/0 PKLIM 1x
FFTNode28 = 0x20918000:WF3/2 PKLIM 1x
FFTNode29 = 0x20A13000:WF1/0 CIC-1 3x
FFTNode30 = 0x20A18000:WF3/2 CIC-1 3x
FFTNode31 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode32 = 0x20E18000:WF3/2 DPD_COMP_IN_3x
FFTNode33 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode34 = 0x20F18000:WF3/2 DPD_COMP_OUT_3x
FFTNode35 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
FFTNode36 = 0x21018000:WF3/2 FLAT_COMP_OUT_3x
// TX, 4T , TX_DOMAIN
FFTNode37 = 0x2091D000:4-Way PKLIM 1x
FFTNode38 = 0x20A1D000:4-Way CIC-1 3x
FFTNode39 = 0x20E1D000:4-Way DPD_COMP_IN_3x
FFTNode40 = 0x20F1D000:4-Way DPD_COMP_OUT_3x
FFTNode41 = 0x2101D000:4-Way FLAT_COMP_OUT_3x
// Thermal, 2 path
FFTNode42 = 0x40B20000:WF1/0 Thermal
FFTNode43 = 0x40B21000:WF3/2 Thermal
// Thermal, 4 path
FFTNode44 = 0x40B22000:4-Way Thermal


//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25


//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25

//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25


//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//DPD_COMP_IN_3x
FFT_DPD_COMP_IN_3x_BW5 = 120
FFT_DPD_COMP_IN_3x_BW10 = 120
FFT_DPD_COMP_IN_3x_BW20 = 120
FFT_DPD_COMP_IN_3x_BW40 = 240
FFT_DPD_COMP_IN_3x_BW80 = 480
FFT_DPD_COMP_IN_3x_BW160C = 480
FFT_DPD_COMP_IN_3x_BW320 = 960

GraphScaleDPD_COMP_IN_3x_BW5	= 66.666667
GraphScaleDPD_COMP_IN_3x_BW10   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW20   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW40   = 33.333333
GraphScaleDPD_COMP_IN_3x_BW80   = 16.666666
GraphScaleDPD_COMP_IN_3x_BW160C = 16.666666
GraphScaleDPD_COMP_IN_3x_BW320  = 8.333333

//DPD_COMP_OUT_3x
FFT_DPD_COMP_OUT_3x_BW5	 = 120
FFT_DPD_COMP_OUT_3x_BW10 = 120
FFT_DPD_COMP_OUT_3x_BW20 = 120
FFT_DPD_COMP_OUT_3x_BW40 = 240
FFT_DPD_COMP_OUT_3x_BW80 = 480
FFT_DPD_COMP_OUT_3x_BW160C = 480
FFT_DPD_COMP_OUT_3x_BW320 = 960

GraphScaleDPD_COMP_OUT_3x_BW5 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW10 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW20 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW40 = 33.333333
GraphScaleDPD_COMP_OUT_3x_BW80 = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW160C = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW320  = 8.333333

//FLAT_COMP_OUT_3x
FFT_FLAT_COMP_OUT_3x_BW5 = 120
FFT_FLAT_COMP_OUT_3x_BW10 = 120
FFT_FLAT_COMP_OUT_3x_BW20 = 120
FFT_FLAT_COMP_OUT_3x_BW40 = 240
FFT_FLAT_COMP_OUT_3x_BW80 = 480
FFT_FLAT_COMP_OUT_3x_BW160C = 480
FFT_FLAT_COMP_OUT_3x_BW320 = 960

GraphScaleFLAT_COMP_OUT_3x_BW5 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW10 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW20 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW40 = 33.333333
GraphScaleFLAT_COMP_OUT_3x_BW80 = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW160C = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW320 = 8.333333
	
CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7906]
ATEMode = 1
FFTShow = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 4096;
11AX = 1;
;BufferBinFile = eeprom_.bin
;RxVectorSetting = Rx_vector_7916.csv
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 3

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 36
// RX, 2R, PHY_DOMAIN
FFTNode0 = 0x10404000:WF1/0 AFIFO
FFTNode1 = 0x10407000:WF2/1 AFIFO
FFTNode2 = 0x10405000:WF2/0 AFIFO
FFTNode3 = 0x10504000:WF1/0 RXFI
FFTNode4 = 0x10507000:WF2/1 RXFI
FFTNode5 = 0x10505000:WF2/0 RXFI
FFTNode6 = 0x10604000:WF1/0 RXFD
FFTNode7 = 0x10607000:WF2/1 RXFD
FFTNode8 = 0x10605000:WF2/0 RXFD
FFTNode9 = 0x10704000:WF1/0 DLPF
FFTNode10 = 0x10707000:WF2/1 DLPF
FFTNode11 = 0x10705000:WF2/0 DLPF
FFTNode12 = 0x10804000:WF1/0 AGCDGC
FFTNode13 = 0x10807000:WF2/1 AGCDGC
FFTNode14 = 0x10805000:WF2/0 AGCDGC
// RX, 2R, ADC_DOMAIN
FFTNode15 = 0x00004000:WF1/0 ADC
FFTNode16 = 0x00007000:WF2/1 ADC
FFTNode17 = 0x00005000:WF2/0 ADC
FFTNode18 = 0x00D00000:WF0 ADC_640M
FFTNode19 = 0x00D01000:WF1 ADC_640M
FFTNode20 = 0x00D02000:WF2 ADC_640M
FFTNode21 = 0x00204000:WF1/0 NYF
FFTNode22 = 0x00207000:WF2/1 NYF
FFTNode23 = 0x00205000:WF2/0 NYF
FFTNode24 = 0x00304000:WF1/0 DCRF
FFTNode25 = 0x00307000:WF2/1 DCRF
FFTNode26 = 0x00305000:WF2/0 DCRF
// TX, 2T , TX_DOMAIN
FFTNode27 = 0x20913000:WF1/0 PKLIM 1x
FFTNode28 = 0x20916000:WF2/1 PKLIM 1x
FFTNode29 = 0x20914000:WF2/0 PKLIM 1x
FFTNode30 = 0x20A13000:WF1/0 CIC-1 3x
FFTNode31 = 0x20A16000:WF2/1 CIC-1 3x
FFTNode32 = 0x20A14000:WF2/0 CIC-1 3x
// Thermal, 2 path
FFTNode33 = 0x40B20000:WF1/0 Thermal
FFTNode34 = 0x40B25000:WF2/1 Thermal
FFTNode35 = 0x40B23000:WF2/0 Thermal

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50

//PKLIM
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50

//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW80 = 480

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50

//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25

CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7915]
ATEMode = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 4096;
//3776;
11AX = 1;
;BufferBinFile = eeprom_.bin
;RxVectorSetting = Rx_vector_7915.csv
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 2
FFTTOTWIFI = 4

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8

FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 30
FFTNode0 = 0x04141000:WF1/0 AFIFO
FFTNode1 = 0x04141001:WF3/2 AFIFO
FFTNode2 = 0x05141002:WF1/0 RXFI
FFTNode3 = 0x05141003:WF3/2 RXFI
FFTNode4 = 0x06141004:WF1/0 RXFD
FFTNode5 = 0x06141005:WF3/2 RXFD
FFTNode6 = 0x07141006:WF1/0 DLPF 
FFTNode7 = 0x07141007:WF3/2 DLPF
FFTNode8 = 0x04151000:4-Way AFIFO
FFTNode9 = 0x05151001:4-Way RXFI
FFTNode10 = 0x06151002:4-Way RXFD
FFTNode11 = 0x07151003:4-Way DLPF
FFTNode12 = 0x00111004:WF1/0 ADC
FFTNode13 = 0x00111005:WF3/2 ADC
FFTNode14 = 0x0211100C:WF1/0 NYF
FFTNode15 = 0x0211100D:WF3/2 NYF
FFTNode16 = 0x0311100E:WF1/0 DCRF
FFTNode17 = 0x0311100F:WF3/2 DCRF
FFTNode18 = 0x02121001:4-Way NYF
FFTNode19 = 0x03121002:4-Way DCRF
FFTNode20 = 0x10211000:WF1/0 PKLIM 1x
FFTNode21 = 0x10211001:WF3/2 PKLIM 1x
FFTNode22 = 0x11211002:WF1/0 CIC-1 3x
FFTNode23 = 0x11211003:WF3/2 CIC-1 3x
FFTNode24 = 0x10221000:4-Way PKLIM 1x
FFTNode25 = 0x11221001:4-Way CIC-1 3x
FFTNode26 = 0x30291040:4-Way Thermal
FFTNode27 = 0x30281040:WF1/0 Thermal
FFTNode28 = 0x30281041:WF3/2 Thermal
FFTNode29 = 0x20001100:DFS

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 320
FFTADCBW80 = 320

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100

//PKLIM
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100

//CIC
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50

//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50

CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7916]
ATEMode = 1
FFTShow = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 4096;
11AX = 1;
;BufferBinFile = eeprom_.bin
;RxVectorSetting = Rx_vector_7916.csv
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 4

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 30
FFTNode0 = 0x04141000:WF1/0 AFIFO
FFTNode1 = 0x04141001:WF3/2 AFIFO
FFTNode2 = 0x05141002:WF1/0 RXFI
FFTNode3 = 0x05141003:WF3/2 RXFI
FFTNode4 = 0x06141004:WF1/0 RXFD
FFTNode5 = 0x06141005:WF3/2 RXFD
FFTNode6 = 0x07141006:WF1/0 DLPF 
FFTNode7 = 0x07141007:WF3/2 DLPF
FFTNode8 = 0x04151000:4-Way AFIFO
FFTNode9 = 0x05151001:4-Way RXFI
FFTNode10 = 0x06151002:4-Way RXFD
FFTNode11 = 0x07151003:4-Way DLPF
FFTNode12 = 0x00111004:WF1/0 ADC
FFTNode13 = 0x00111005:WF3/2 ADC
FFTNode14 = 0x0211100C:WF1/0 NYF
FFTNode15 = 0x0211100D:WF3/2 NYF
FFTNode16 = 0x0311100E:WF1/0 DCRF
FFTNode17 = 0x0311100F:WF3/2 DCRF
FFTNode18 = 0x02121001:4-Way NYF
FFTNode19 = 0x03121002:4-Way DCRF
FFTNode20 = 0x10211000:WF1/0 PKLIM 1x
FFTNode21 = 0x10211001:WF3/2 PKLIM 1x
FFTNode22 = 0x11211002:WF1/0 CIC-1 3x
FFTNode23 = 0x11211003:WF3/2 CIC-1 3x
FFTNode24 = 0x10221000:4-Way PKLIM 1x
FFTNode25 = 0x11221001:4-Way CIC-1 3x
FFTNode26 = 0x30291040:4-Way Thermal
FFTNode27 = 0x30281040:WF1/0 Thermal
FFTNode28 = 0x30281041:WF3/2 Thermal
FFTNode29 = 0x20001100:DFS



//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 320
FFTADCBW80 = 320

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25



//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100



//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100


//PKLIM
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100



//CIC
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50




CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt


[Settings_MT7922]
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 2560;
//1536;
11AX = 1;
;BufferBinFile = eeprom_7663.bin
;RxVectorSetting = Rx_vector_7915.csv

//FFT
FFTShow = 1
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 2
FFTTOTWIFI = 2

//FFT Full Band
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 0
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=D:\temp\result
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=0
FFT_NODE=0x15141002:WF1/0 RXFI
FFT_CAP_TIMEOUT_SEC=25

//FFT Disturb Detect
FFT_DISTURB_DETECT=0
FFT_DISTURB_DETECT_RETRY = 1
FFT_DISTURB_DETECT_HIDE_CHART = 0
FFT_DISTURB_DETECT_STOP = 0
FFT_DIST_DET_WF0_2G=-93
FFT_DIST_DET_WF0_2G_RANGE=2
FFT_DIST_DET_WF1_2G=-93
FFT_DIST_DET_WF1_2G_RANGE=2
FFT_DIST_DET_WF0_5G=-93
FFT_DIST_DET_WF0_5G_RANGE=2
FFT_DIST_DET_WF1_5G=-93
FFT_DIST_DET_WF1_5G_RANGE=2
FFT_DIST_DET_WF0_6G=-93
FFT_DIST_DET_WF0_6G_RANGE=2
FFT_DIST_DET_WF1_6G=-93
FFT_DIST_DET_WF1_6G_RANGE=2

//FFT Test Time Delay
FFTTimeDelayCount = 0

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 33
// RX, 1R, PHY_DOMAIN 
FFTNode0 = 0x14000000:WF0 AFIFO 
FFTNode1 = 0x14010000:WF1 AFIFO 
FFTNode2 = 0x15000000:WF0 RXFI 
FFTNode3 = 0x15010000:WF1 RXFI 
FFTNode4 = 0x16000000:WF0 RXFD 
FFTNode5 = 0x16010000:WF1 RXFD 
FFTNode6 = 0x17000000:WF0 DLPF 
FFTNode7 = 0x17010000:WF1 DLPF 
FFTNode8 = 0x18000000:WF0 AGC DGC 
FFTNode9 = 0x18010000:WF1 AGC DGC 
// RX, 2R, PHY_DOMAIN 
FFTNode10 = 0x14020000:WF1/0 AFIFO 
FFTNode11 = 0x15020000:WF1/0 RXFI 
FFTNode12 = 0x16020000:WF1/0 RXFD 
FFTNode13 = 0x17020000:WF1/0 DLPF 
FFTNode14 = 0x18020000:WF1/0 AGC DGC 
// RX, 1R, ADC_DOMAIN 
FFTNode15 = 0x00000000:WF0 ADC 
FFTNode16 = 0x00010000:WF1 ADC 
FFTNode17 = 0x02000000:WF0 NYF 
FFTNode18 = 0x02010000:WF1 NYF 
FFTNode19 = 0x03000000:WF0 DCRF 
FFTNode20 = 0x03010000:WF1 DCRF 
// RX, 2R, ADC_DOMAIN 
FFTNode21 = 0x0D000000:WF0 ADC 640M 
FFTNode22 = 0x0D010000:WF1 ADC 640M
FFTNode23 = 0x00020000:WF1/0 ADC
FFTNode24 = 0x02020000:WF1/0 NYF 
FFTNode25 = 0x03020000:WF1/0 DCRF 
//TX, 1T , TX_DOMAIN 
FFTNode26 = 0x29030000:WF0 PKLIM 1x 
FFTNode27 = 0x29040000:WF1 PKLIM 1x 
FFTNode28 = 0x2A030000:WF0 CIC-1 3x 
FFTNode29 = 0x2A040000:WF1 CIC-1 3x 
//TX, 2T , TX_DOMAIN 
FFTNode30 = 0x29050000:WF1/0 PKLIM 1x
FFTNode31 = 0x2A050000:WF1/0 CIC-1 3x
// Thermal 
FFTNode32 = 0x4B080000:WF1/0 Thermal


//ADC
//FFTADCBW05 = 320
//FFTADCBW10 = 320
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640

//GraphScaleFFTADCBW05 = 25
//GraphScaleFFTADCBW10 = 25
GraphScaleFFTADCBW20 = 25
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5

//DLPF
//FFTDLPFBW05 = 10
//FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160

//GraphScaleFFTDLPFBW05 = 800
//GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50


//AGCDGC
//FFTAGCDGCBW05 = 10
//FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160

//GraphScaleFFTAGCDGCBW05 = 800
//GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50

//PKLIM-1x
//FFTPKLIMBW05 = 20
//FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160

//GraphScaleFFTPKLIMBW05 = 400
//GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50


//CIC-3x
//FFTCICBW05 = 60
//FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW80 = 480

//GraphScaleFFTCICBW05 = 133.33333
//GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666

//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25


CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt


[Settings_MT7923]
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 2560;
//1536;
11AX = 1;
;BufferBinFile = eeprom_7663.bin
;RxVectorSetting = Rx_vector_7915.csv

//FFT
FFTShow = 1
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 2
FFTTOTWIFI = 2

//FFT Full Band
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 0
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=D:\temp\result
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=0
FFT_NODE=0x15141002:WF1/0 RXFI
FFT_CAP_TIMEOUT_SEC=25

//FFT Disturb Detect
FFT_DISTURB_DETECT=0
FFT_DISTURB_DETECT_RETRY = 1
FFT_DISTURB_DETECT_HIDE_CHART = 0
FFT_DISTURB_DETECT_STOP = 0
FFT_DIST_DET_WF0_2G=-93
FFT_DIST_DET_WF0_2G_RANGE=2
FFT_DIST_DET_WF1_2G=-93
FFT_DIST_DET_WF1_2G_RANGE=2
FFT_DIST_DET_WF0_5G=-93
FFT_DIST_DET_WF0_5G_RANGE=2
FFT_DIST_DET_WF1_5G=-93
FFT_DIST_DET_WF1_5G_RANGE=2
FFT_DIST_DET_WF0_6G=-93
FFT_DIST_DET_WF0_6G_RANGE=2
FFT_DIST_DET_WF1_6G=-93
FFT_DIST_DET_WF1_6G_RANGE=2

//FFT Test Time Delay
FFTTimeDelayCount = 0

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 33
// RX, 1R, PHY_DOMAIN 
FFTNode0 = 0x14000000:WF0 AFIFO 
FFTNode1 = 0x14010000:WF1 AFIFO 
FFTNode2 = 0x15000000:WF0 RXFI 
FFTNode3 = 0x15010000:WF1 RXFI 
FFTNode4 = 0x16000000:WF0 RXFD 
FFTNode5 = 0x16010000:WF1 RXFD 
FFTNode6 = 0x17000000:WF0 DLPF 
FFTNode7 = 0x17010000:WF1 DLPF 
FFTNode8 = 0x18000000:WF0 AGC DGC 
FFTNode9 = 0x18010000:WF1 AGC DGC 
// RX, 2R, PHY_DOMAIN 
FFTNode10 = 0x14020000:WF1/0 AFIFO 
FFTNode11 = 0x15020000:WF1/0 RXFI 
FFTNode12 = 0x16020000:WF1/0 RXFD 
FFTNode13 = 0x17020000:WF1/0 DLPF 
FFTNode14 = 0x18020000:WF1/0 AGC DGC 
// RX, 1R, ADC_DOMAIN 
FFTNode15 = 0x00000000:WF0 ADC 
FFTNode16 = 0x00010000:WF1 ADC 
FFTNode17 = 0x02000000:WF0 NYF 
FFTNode18 = 0x02010000:WF1 NYF 
FFTNode19 = 0x03000000:WF0 DCRF 
FFTNode20 = 0x03010000:WF1 DCRF 
// RX, 2R, ADC_DOMAIN 
FFTNode21 = 0x0D000000:WF0 ADC 640M 
FFTNode22 = 0x0D010000:WF1 ADC 640M
FFTNode23 = 0x00020000:WF1/0 ADC
FFTNode24 = 0x02020000:WF1/0 NYF 
FFTNode25 = 0x03020000:WF1/0 DCRF 
//TX, 1T , TX_DOMAIN 
FFTNode26 = 0x29030000:WF0 PKLIM 1x 
FFTNode27 = 0x29040000:WF1 PKLIM 1x 
FFTNode28 = 0x2A030000:WF0 CIC-1 3x 
FFTNode29 = 0x2A040000:WF1 CIC-1 3x 
//TX, 2T , TX_DOMAIN 
FFTNode30 = 0x29050000:WF1/0 PKLIM 1x
FFTNode31 = 0x2A050000:WF1/0 CIC-1 3x
// Thermal 
FFTNode32 = 0x4B080000:WF1/0 Thermal


//ADC
//FFTADCBW05 = 320
//FFTADCBW10 = 320
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640

//GraphScaleFFTADCBW05 = 25
//GraphScaleFFTADCBW10 = 25
GraphScaleFFTADCBW20 = 25
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5

//DLPF
//FFTDLPFBW05 = 10
//FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160

//GraphScaleFFTDLPFBW05 = 800
//GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50


//AGCDGC
//FFTAGCDGCBW05 = 10
//FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160

//GraphScaleFFTAGCDGCBW05 = 800
//GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50

//PKLIM-1x
//FFTPKLIMBW05 = 20
//FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160

//GraphScaleFFTPKLIMBW05 = 400
//GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50


//CIC-3x
//FFTCICBW05 = 60
//FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW80 = 480

//GraphScaleFFTCICBW05 = 133.33333
//GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666

//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25


CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt


[Settings_MT7925]
11AX = 1;
11BE = 1;
nonLegacyPower = 1
CommandTimeout = 2000;
APIP = *************
APUser = boli
APPwd = 1
EFuseSize = 3584;
CmdDump = 1;
DLLCalRXInfo = 1
UNIFIEDCMD = 1
SupportBinFile = 0
BufferBinFile = eeprom_6632.bin
RxVectorSetting = Rx_vector_6632.csv
SecEFuseSize = 4120

CalDumpOutFolder =
CalID0 = RC_CAL.txt
CalID1 = RX_RSSI_DCOC_CAL.txt
CalID2 = RX_DCOC_CAL.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_PA_BIAS.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_DPD.txt
CalID30 = ALL.txt
 
//FFT Full Band
BandMode="SingleBand 0"
NPTiCapModeInit = 0
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_RX_DELAY = 200
FFT_6G_Supported = 1
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=d:\Temp\Result
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=1
FFT_NODE=0x10504000:WF1/0 RXFI
FixedGainSupport = 0
FixedGainScriptFolder = FixedGainScripts
FixedGainHighScriptFile = Fix_Gain_High.txt
FixedGainMidleScriptFile = Fix_Gain_Mid.txt
FixedGainLowScriptFile = Fix_Gain_Low.txt
ResetGainScriptFile = Reset_gain_to_auto.txt

NPT_2G_PHY_Index = 0
NPT_5G_PHY_Index = 0
NPT_6G_PHY_Index = 0

CaptureTime = 200;
CaptureStopLen = 50;

FFT_CAP_TIMEOUT_SEC=25
CallTestMode = 0;

//-- ICAP Setting----[START] 2021/03/11
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 2
FFTShow = 1
 
ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
ThermalBit = 8
 
FFTEventCount = 11
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
 
FFTNodeCount = 42
// RX, 1R, PHY_DOMAIN
FFTNode0 = 0x10400000:WF0 AFIFO
FFTNode1 = 0x10401000:WF1 AFIFO
FFTNode2 = 0x10500000:WF0 RXFI
FFTNode3 = 0x10501000:WF1 RXFI
FFTNode4 = 0x10600000:WF0 RXFD
FFTNode5 = 0x10601000:WF1 RXFD
FFTNode6 = 0x10700000:WF0 DLPF
FFTNode7 = 0x10701000:WF1 DLPF
FFTNode8 = 0x10800000:WF0 AGC_DGC
FFTNode9 = 0x10801000:WF1 AGC_DGC
// RX, 2R, PHY_DOMAIN
FFTNode10 = 0x10404000:WF1/0 AFIFO
FFTNode11 = 0x10504000:WF1/0 RXFI
FFTNode12 = 0x10604000:WF1/0 RXFD
FFTNode13 = 0x10704000:WF1/0 DLPF
FFTNode14 = 0x10804000:WF1/0 AGC_DGC
// RX, 1R, ADC_DOMAIN
FFTNode15 = 0x00000000:WF0 ADC
FFTNode16 = 0x00001000:WF1 ADC
FFTNode17 = 0x00D04000:WF0 ADC_1280M_BW320
FFTNode18 = 0x00D04001:WF1 ADC_1280M_BW320
FFTNode19 = 0x00200000:WF0 NYF
FFTNode20 = 0x00201000:WF1 NYF
FFTNode21 = 0x00300000:WF0 DCRF
FFTNode22 = 0x00301000:WF1 DCRF
// RX, 2R, ADC_DOMAIN
FFTNode23 = 0x00004000:WF1/0 ADC
FFTNode24 = 0x00204000:WF1/0 NYF
FFTNode25 = 0x00304000:WF1/0 DCRF
//TX, 1T , TX_DOMAIN
FFTNode26 = 0x2090F000:WF0 PKLIM 1x
FFTNode27 = 0x20910000:WF1 PKLIM 1x
FFTNode28 = 0x20A0F000:WF0 CIC-1 3x
FFTNode29 = 0x20A10000:WF1 CIC-1 3x
FFTNode30 = 0x20E0F000:WF0 DPD_COMP_IN_3x
FFTNode31 = 0x20E10000:WF1 DPD_COMP_IN_3x
FFTNode32 = 0x20F0F000:WF0 DPD_COMP_OUT_3x
FFTNode33 = 0x20F10000:WF1 DPD_COMP_OUT_3x
FFTNode34 = 0x2100F000:WF0 FLAT_COMP_OUT_3x
FFTNode35 = 0x21010000:WF1 FLAT_COMP_OUT_3x
// TX, 2T , TX_DOMAIN
FFTNode36 = 0x20913000:WF1/0 PKLIM 1x
FFTNode37 = 0x20A13000:WF1/0 CIC-1 3x
FFTNode38 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode39 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode40 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
// Thermal, 2 path
FFTNode41 = 0x40B20000:WF1/0 Thermal    
 
 
//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280
  
GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25
 
//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640
 
GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5
 
//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320
 
GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25
 
//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320
 
GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25
 
//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320
 
GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25
 
//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960
 
GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333
 
//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160
 
GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50
 
//-- ICAP Setting----[END] 2022/03/11
[Settings_MT7933]

nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
BufferBin = 0
SupportBinFile = 1
BufferBinFile = eeprom.bin
EFuseSize = 1536;
11AX = 1;
;RxVectorSetting = Rx_vector_7915.csv

ComPortNumber = 9
BaudRate = 921600

//FFT
FFTShow = 1
FFT_TrigMode_Show  = 2
//0:hide,1:show,2:disalbe
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 2
//1:1x1, 2:2x2,4:4x4
FFTTOTWIFI = 1

//FFT Full Band
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 0
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=D:\temp\result
FFT_SHOW_RUNTIME_CHART=1
AUTO_FULL_BAND_SUPPORT=0
FFT_NODE=0x15141002:WF1/0 RXFI

//FFT Disturb Detect
FFT_DISTURB_DETECT=0
FFT_DISTURB_DETECT_RETRY = 1
FFT_DISTURB_DETECT_HIDE_CHART = 0
FFT_DISTURB_DETECT_STOP = 0
FFT_DIST_DET_WF0_2G=-93
FFT_DIST_DET_WF0_2G_RANGE=2
FFT_DIST_DET_WF1_2G=-93
FFT_DIST_DET_WF1_2G_RANGE=2
FFT_DIST_DET_WF0_5G=-93
FFT_DIST_DET_WF0_5G_RANGE=2
FFT_DIST_DET_WF1_5G=-93
FFT_DIST_DET_WF1_5G_RANGE=2
FFT_DIST_DET_WF0_6G=-93
FFT_DIST_DET_WF0_6G_RANGE=2
FFT_DIST_DET_WF1_6G=-93
FFT_DIST_DET_WF1_6G_RANGE=2

//FFT Test Time Delay
FFTTimeDelayCount = 0

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 7
// RX, 1R, PHY_DOMAIN
FFTNode0 = 0x14000000:WF0 AFIFO
FFTNode1 = 0x15000000:WF0 RXFI
FFTNode2 = 0x16000000:WF0 RXFD
FFTNode3 = 0x17000000:WF0 DLPF
// RX, 1R, ADC_DOMAIN
FFTNode4 = 0x00000000:WF0 ADC
FFTNode5 = 0x02000000:WF0 NYF
FFTNode6 = 0x03000000:WF0 DCRF


//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 320
FFTADCBW80 = 320

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25



//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100



//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100


//PKLIM
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100



//CIC
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50




CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7935]
11AX = 1;
11BE = 1;
nonLegacyPower = 1
CommandTimeout = 2000;
APIP = *************
APUser = boli
APPwd = 1
EFuseSize = 3584;
CmdDump = 1;
DLLCalRXInfo = 1
UNIFIEDCMD = 1
SupportBinFile = 0
BufferBinFile = eeprom_6639.bin
RxVectorSetting = Rx_vector_6639.csv

CalDumpOutFolder =
CalID0 = RC_CAL.txt
CalID1 = RX_RSSI_DCOC_CAL.txt
CalID2 = RX_DCOC_CAL.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_PA_BIAS.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_DPD.txt
CalID30 = ALL.txt
 
//FFT Full Band
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 1
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=E:\MT6639_NPTtool\1_MT6639_NPTrawdata
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=1
FFT_NODE=0x10504000:WF1/0 RXFI

NPT_2G_PHY_Index = 0
NPT_5G_PHY_Index = 1
NPT_6G_PHY_Index = 1

CaptureTime = 200;
CaptureStopLen = 50;

FFT_CAP_TIMEOUT_SEC=25
CallTestMode = 0;

//-- ICAP Setting----[START] 2021/03/11
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 2
FFTShow = 1
 
ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
ThermalBit = 8
 
 
FFTEventCount = 11
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
 
FFTNodeCount = 42
// RX, 1R, PHY_DOMAIN
FFTNode0 = 0x10400000:WF0 AFIFO
FFTNode1 = 0x10401000:WF1 AFIFO
FFTNode2 = 0x10500000:WF0 RXFI
FFTNode3 = 0x10501000:WF1 RXFI
FFTNode4 = 0x10600000:WF0 RXFD
FFTNode5 = 0x10601000:WF1 RXFD
FFTNode6 = 0x10700000:WF0 DLPF
FFTNode7 = 0x10701000:WF1 DLPF
FFTNode8 = 0x10800000:WF0 AGC_DGC
FFTNode9 = 0x10801000:WF1 AGC_DGC
// RX, 2R, PHY_DOMAIN
FFTNode10 = 0x10404000:WF1/0 AFIFO
FFTNode11 = 0x10504000:WF1/0 RXFI
FFTNode12 = 0x10604000:WF1/0 RXFD
FFTNode13 = 0x10704000:WF1/0 DLPF
FFTNode14 = 0x10804000:WF1/0 AGC_DGC
// RX, 1R, ADC_DOMAIN
FFTNode15 = 0x00000000:WF0 ADC
FFTNode16 = 0x00001000:WF1 ADC
FFTNode17 = 0x00D04000:WF0 ADC_1280M_BW320
FFTNode18 = 0x00D04001:WF1 ADC_1280M_BW320
FFTNode19 = 0x00200000:WF0 NYF
FFTNode20 = 0x00201000:WF1 NYF
FFTNode21 = 0x00300000:WF0 DCRF
FFTNode22 = 0x00301000:WF1 DCRF
// RX, 2R, ADC_DOMAIN
FFTNode23 = 0x00004000:WF1/0 ADC
FFTNode24 = 0x00204000:WF1/0 NYF
FFTNode25 = 0x00304000:WF1/0 DCRF
//TX, 1T , TX_DOMAIN
FFTNode26 = 0x2090F000:WF0 PKLIM 1x
FFTNode27 = 0x20910000:WF1 PKLIM 1x
FFTNode28 = 0x20A0F000:WF0 CIC-1 3x
FFTNode29 = 0x20A10000:WF1 CIC-1 3x
FFTNode30 = 0x20E0F000:WF0 DPD_COMP_IN_3x
FFTNode31 = 0x20E10000:WF1 DPD_COMP_IN_3x
FFTNode32 = 0x20F0F000:WF0 DPD_COMP_OUT_3x
FFTNode33 = 0x20F10000:WF1 DPD_COMP_OUT_3x
FFTNode34 = 0x2100F000:WF0 FLAT_COMP_OUT_3x
FFTNode35 = 0x21010000:WF1 FLAT_COMP_OUT_3x
// TX, 2T , TX_DOMAIN
FFTNode36 = 0x20913000:WF1/0 PKLIM 1x
FFTNode37 = 0x20A13000:WF1/0 CIC-1 3x
FFTNode38 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode39 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode40 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
// Thermal, 2 path
FFTNode41 = 0x40B20000:WF1/0 Thermal    
 
 
//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280
  
GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25
 
//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640
 
GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5
 
//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320
 
GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25
 
//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320
 
GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25
 
//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320
 
GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25
 
//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960
 
GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333
 
//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160
 
GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50
 
//-- ICAP Setting----[END] 2022/03/11

[Settings_MT7961]
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 2560;
//1536;
11AX = 1;
;BufferBinFile = eeprom_7663.bin
;RxVectorSetting = Rx_vector_7915.csv

//FFT
FFTShow = 1
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 2
FFTTOTWIFI = 2

//FFT Full Band
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 0
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=158.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=2
FFT_PCB_LOSS_WF1_2G=0.5
FFT_PCB_LOSS_WF0_5G=1.5
FFT_PCB_LOSS_WF1_5G=0.5
FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_Y_MAX=-45
FFT_Y_MIN=-145
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=D:\temp\result
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=0
FFT_NODE=0x15141002:WF1/0 RXFI
FFT_CAP_TIMEOUT_SEC=25

//FFT Disturb Detect
FFT_DISTURB_DETECT=0
FFT_DISTURB_DETECT_RETRY = 1
FFT_DISTURB_DETECT_HIDE_CHART = 0
FFT_DISTURB_DETECT_STOP = 0
FFT_DIST_DET_WF0_2G=-93
FFT_DIST_DET_WF0_2G_RANGE=2
FFT_DIST_DET_WF1_2G=-93
FFT_DIST_DET_WF1_2G_RANGE=2
FFT_DIST_DET_WF0_5G=-93
FFT_DIST_DET_WF0_5G_RANGE=2
FFT_DIST_DET_WF1_5G=-93
FFT_DIST_DET_WF1_5G_RANGE=2
FFT_DIST_DET_WF0_6G=-93
FFT_DIST_DET_WF0_6G_RANGE=2
FFT_DIST_DET_WF1_6G=-93
FFT_DIST_DET_WF1_6G_RANGE=2

//FFT Test Time Delay
FFTTimeDelayCount = 0

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 11
FFTNode0 = 0x00111004:WF1/0 ADC
FFTNode1 = 0x0211100C:WF1/0 NYF
FFTNode2 = 0x0311100E:WF1/0 DCRF
FFTNode3 = 0x14141000:WF1/0 AFIFO
FFTNode4 = 0x15141002:WF1/0 RXFI
FFTNode5 = 0x16141004:WF1/0 RXFD
FFTNode6 = 0x17141006:WF1/0 DLPF  
FFTNode7 = 0x20211000:WF1/0 PKLIM 1x
FFTNode8 = 0x21211002:WF1/0 CIC-1 3x
FFTNode9 = 0x30001100:DFS
FFTNode10 = 0x40281040:WF1/0 Thermal

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 320
FFTADCBW80 = 320

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100

//PKLIM
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100

//CIC
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50

//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50


CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt


[Settings_MT7981]
ATEMode = 1
FFTShow = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
EFuseSize = 4096;
11AX = 1;
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 3

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 36
// RX, 2R, PHY_DOMAIN
FFTNode0 = 0x10404000:WF1/0 AFIFO
FFTNode1 = 0x10407000:WF2/1 AFIFO
FFTNode2 = 0x10405000:WF2/0 AFIFO
FFTNode3 = 0x10504000:WF1/0 RXFI
FFTNode4 = 0x10507000:WF2/1 RXFI
FFTNode5 = 0x10505000:WF2/0 RXFI
FFTNode6 = 0x10604000:WF1/0 RXFD
FFTNode7 = 0x10607000:WF2/1 RXFD
FFTNode8 = 0x10605000:WF2/0 RXFD
FFTNode9 = 0x10704000:WF1/0 DLPF
FFTNode10 = 0x10707000:WF2/1 DLPF
FFTNode11 = 0x10705000:WF2/0 DLPF
FFTNode12 = 0x10804000:WF1/0 AGCDGC
FFTNode13 = 0x10807000:WF2/1 AGCDGC
FFTNode14 = 0x10805000:WF2/0 AGCDGC
// RX, 2R, ADC_DOMAIN
FFTNode15 = 0x00004000:WF1/0 ADC
FFTNode16 = 0x00007000:WF2/1 ADC
FFTNode17 = 0x00005000:WF2/0 ADC
FFTNode18 = 0x00D00000:WF0 ADC_640M
FFTNode19 = 0x00D01000:WF1 ADC_640M
FFTNode20 = 0x00D02000:WF2 ADC_640M
FFTNode21 = 0x00204000:WF1/0 NYF
FFTNode22 = 0x00207000:WF2/1 NYF
FFTNode23 = 0x00205000:WF2/0 NYF
FFTNode24 = 0x00304000:WF1/0 DCRF
FFTNode25 = 0x00307000:WF2/1 DCRF
FFTNode26 = 0x00305000:WF2/0 DCRF
// TX, 2T , TX_DOMAIN
FFTNode27 = 0x20913000:WF1/0 PKLIM 1x
FFTNode28 = 0x20916000:WF2/1 PKLIM 1x
FFTNode29 = 0x20914000:WF2/0 PKLIM 1x
FFTNode30 = 0x20A13000:WF1/0 CIC-1 3x
FFTNode31 = 0x20A16000:WF2/1 CIC-1 3x
FFTNode32 = 0x20A14000:WF2/0 CIC-1 3x
// Thermal, 2 path
FFTNode33 = 0x40B20000:WF1/0 Thermal
FFTNode34 = 0x40B25000:WF2/1 Thermal
FFTNode35 = 0x40B23000:WF2/0 Thermal

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50

//PKLIM
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50

//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW80 = 480

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50

//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25

CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7986]

//FFT Full Band
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 0
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=90.969
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=40
FFT_PCB_LOSS_WF1_2G=40
FFT_PCB_LOSS_WF2_2G=40
FFT_PCB_LOSS_WF3_2G=40

FFT_PCB_LOSS_WF0_5G=38
FFT_PCB_LOSS_WF1_5G=38
FFT_PCB_LOSS_WF2_5G=38
FFT_PCB_LOSS_WF3_5G=38

FFT_PCB_LOSS_WF0_6G=0
FFT_PCB_LOSS_WF1_6G=0
FFT_PCB_LOSS_WF2_6G=0
FFT_PCB_LOSS_WF3_6G=0

NPT_2G_PHY_Index = 0
NPT_5G_PHY_Index = 1
NPT_6G_PHY_Index = 0

CaptureTime = 50;
CaptureStopLen = 50;

FFT_Y_MAX=-10
FFT_Y_MIN=-180
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=D:\temp\result
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=0
SigleChannelModeOnly = 1
FixedGainSupport = 1
FFT_NODE=0x1050E000:4-Way RXFI
FFT_NODE_WF2_WF3=0x1050E000:4-Way RXFI
FFT_CAP_TIMEOUT_SEC=25

//FFT Disturb Detect
FFT_DISTURB_DETECT=0
FFT_DISTURB_DETECT_RETRY = 1
FFT_DISTURB_DETECT_HIDE_CHART = 0
FFT_DISTURB_DETECT_STOP = 0
FFT_DIST_DET_WF0_2G=-93
FFT_DIST_DET_WF0_2G_RANGE=2
FFT_DIST_DET_WF1_2G=-93
FFT_DIST_DET_WF1_2G_RANGE=2
FFT_DIST_DET_WF0_5G=-93
FFT_DIST_DET_WF0_5G_RANGE=2
FFT_DIST_DET_WF1_5G=-93
FFT_DIST_DET_WF1_5G_RANGE=2
FFT_DIST_DET_WF0_6G=-93
FFT_DIST_DET_WF0_6G_RANGE=2
FFT_DIST_DET_WF1_6G=-93
FFT_DIST_DET_WF1_6G_RANGE=2

//FFT Test Time Delay
FFTTimeDelayCount = 0

ATEMode = 1
FFTShow = 1
UIShowSubChip = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 4096;
11AX = 1;
;BufferBinFile = eeprom_.bin
;RxVectorSetting = Rx_vector_7986.csv
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 4

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 34
// RX, 2R, PHY_DOMAIN
FFTNode0 = 0x10404000:WF1/0 AFIFO
FFTNode1 = 0x10409000:WF3/2 AFIFO
FFTNode2 = 0x10504000:WF1/0 RXFI
FFTNode3 = 0x10509000:WF3/2 RXFI
FFTNode4 = 0x10604000:WF1/0 RXFD
FFTNode5 = 0x10609000:WF3/2 RXFD
FFTNode6 = 0x10704000:WF1/0 DLPF 
FFTNode7 = 0x10709000:WF3/2 DLPF
FFTNode8 = 0x10804000:WF1/0 AGCDGC
FFTNode9 = 0x10809000:WF2/3 AGCDGC
// RX, 4R, PHY_DOMAIN
FFTNode10 = 0x1040E000:4-Way AFIFO
FFTNode11 = 0x1050E000:4-Way RXFI
FFTNode12 = 0x1060E000:4-Way RXFD
FFTNode13 = 0x1070E000:4-Way DLPF
FFTNode14 = 0x1080E000:4-Way AGCDGC
// RX, 2R, ADC_DOMAIN
FFTNode15 = 0x00004000:WF1/0 ADC
FFTNode16 = 0x00009000:WF3/2 ADC
FFTNode17 = 0x00D05000:WF2/0 ADC_640M
FFTNode18 = 0x00D08000:WF1/3 ADC_640M
FFTNode19 = 0x00204000:WF1/0 NYF
FFTNode20 = 0x00209000:WF3/2 NYF
FFTNode21 = 0x00304000:WF1/0 DCRF
FFTNode22 = 0x00309000:WF3/2 DCRF
// RX, 4R, ADC_DOMAIN
FFTNode23 = 0x0020E000:4-Way NYF
FFTNode24 = 0x0030E000:4-Way DCRF
// TX, 2T , TX_DOMAIN
FFTNode25 = 0x20913000:WF1/0 PKLIM 1x
FFTNode26 = 0x20918000:WF3/2 PKLIM 1x
FFTNode27 = 0x20A13000:WF1/0 CIC-1 3x
FFTNode28 = 0x20A18000:WF3/2 CIC-1 3x
// TX, 4T , TX_DOMAIN
FFTNode29 = 0x2091D000:4-Way PKLIM 1x
FFTNode30 = 0x20A1D000:4-Way CIC-1 3x
// Thermal, 2 path
FFTNode31 = 0x40B20000:WF1/0 Thermal
FFTNode32 = 0x40B21000:WF3/2 Thermal
// Thermal, 4 path
FFTNode33 = 0x40B22000:4-Way Thermal




//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 160
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5


//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50


//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50

//PKLIM
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50


//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW80 = 480

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25



CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt


[Settings_MT7988]
DBDCChip = 1;
;TBTCChip = 1;
11BE = 1;
[Settings_MT7990]

;FFT Full Band
FFTTOTWIFI = 5
FullBandFFTMode = 0
FFT_FullBand_FullUI_DebugMode = 0
FFT_6G_Supported = 1
FFT_Size_Show = 3
FFT_2G_Band_Plan=0
FFT_5G_Band_Plan=0
FFT_COMPENSATION=0
FFT_PCB_LOSS_GROUP_SETTING = 0
FFT_PCB_LOSS_WF0_2G=1
FFT_PCB_LOSS_WF1_2G=1
FFT_PCB_LOSS_WF2_2G=1
FFT_PCB_LOSS_WF3_2G=1

FFT_PCB_LOSS_WF0_5G=1
FFT_PCB_LOSS_WF1_5G=1
FFT_PCB_LOSS_WF2_5G=1
FFT_PCB_LOSS_WF3_5G=1

FFT_PCB_LOSS_WF0_6G=1
FFT_PCB_LOSS_WF1_6G=1
FFT_PCB_LOSS_WF2_6G=1
FFT_PCB_LOSS_WF3_6G=1
FFT_PCB_LOSS_WF4_6G=1

FFT_COMPENSATION_WF0_2G=5
FFT_COMPENSATION_WF1_2G=5
FFT_COMPENSATION_WF2_2G=5
FFT_COMPENSATION_WF3_2G=5

FFT_COMPENSATION_WF0_5G=5
FFT_COMPENSATION_WF1_5G=5
FFT_COMPENSATION_WF2_5G=5
FFT_COMPENSATION_WF3_5G=5

FFT_COMPENSATION_WF0_6G=7
FFT_COMPENSATION_WF1_6G=7
FFT_COMPENSATION_WF2_6G=7
FFT_COMPENSATION_WF3_6G=7
FFT_COMPENSATION_WF4_6G=7

NPT_2G_PHY_Index = 0
NPT_5G_PHY_Index = 1
NPT_6G_PHY_Index = 2
NPT_RBW_Enable = 1
FixedGainSupport = 1
FixedGainScriptFolder = FixedGainScripts
FixedGainHighScriptFile = Fix_Gain_High.txt
FixedGainMidleScriptFile = Fix_Gain_Mid.txt
FixedGainLowScriptFile = Fix_Gain_Low.txt
ResetGainScriptFile = Reset_gain_to_auto.txt

;312.5kHz, 156.25kHz, 78.125kHz, 39.062kHz
;NPT_RBW = 78.125kHz 

CaptureTime = 50;
CaptureStopLen = 50;

FFT_Y_MAX=0
FFT_Y_MIN=-180
FFT_FILE_NAME=result.csv
FFT_DATA_FILE_PATH=D:\temp\result
FFT_SHOW_RUNTIME_CHART=0
AUTO_FULL_BAND_SUPPORT=0
SigleChannelModeOnly = 0
FFT_NODE=0x10504000:WF1/0 RXFI
FFT_NODE_WF2_WF3=0x10504000:WF1/0 RXFI
;FFT_NODE_WF2_WF3=0x10509000:WF3/2 RXFI
FFT_CAP_TIMEOUT_SEC=25

;FFT Disturb Detect
FFT_DISTURB_DETECT=0
FFT_DISTURB_DETECT_RETRY = 1
FFT_DISTURB_DETECT_HIDE_CHART = 0
FFT_DISTURB_DETECT_STOP = 0
FFT_DIST_DET_WF0_2G=-93
FFT_DIST_DET_WF0_2G_RANGE=2
FFT_DIST_DET_WF1_2G=-93
FFT_DIST_DET_WF1_2G_RANGE=2
FFT_DIST_DET_WF0_5G=-93
FFT_DIST_DET_WF0_5G_RANGE=2
FFT_DIST_DET_WF1_5G=-93
FFT_DIST_DET_WF1_5G_RANGE=2
FFT_DIST_DET_WF0_6G=-93
FFT_DIST_DET_WF0_6G_RANGE=2
FFT_DIST_DET_WF1_6G=-93
FFT_DIST_DET_WF1_6G_RANGE=2

;FFT Test Time Delay
FFTTimeDelayCount = 0

Band0TxAIDOffSet = 3A9
Band1TxAIDOffSet = 3A9
Band2TxAIDOffSet = 3A9

ATEMode = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 7680
//3776;
11AX = 1;
DBDCChip = 1;
11BE = 1;

//FFT
FFTShow = 1
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 50
// RX, 2R, PHY_DOMAIN
FFTNode0 = 0x10404000:WF1/0 AFIFO
FFTNode1 = 0x10409000:WF3/2 AFIFO
FFTNode2 = 0x10504000:WF1/0 RXFI
FFTNode3 = 0x10509000:WF3/2 RXFI
FFTNode4 = 0x10604000:WF1/0 RXFD
FFTNode5 = 0x10609000:WF3/2 RXFD
FFTNode6 = 0x10704000:DLPF
FFTNode7 = 0x10804000:AGCDGC

// RX, 2R, ADC_DOMAIN
FFTNode8 = 0x00004000:WF1/0 ADC
FFTNode9 = 0x00009000:WF3/2 ADC
FFTNode10 = 0x00D00000:WF0 ADC_1280M_BW320
FFTNode11 = 0x00D01000:WF1 ADC_1280M_BW320
FFTNode12 = 0x00D02000:WF2 ADC_1280M_BW320
FFTNode13 = 0x00D03000:WF3 ADC_1280M_BW320
FFTNode14 = 0x00204000:WF1/0 NYF
FFTNode15 = 0x00209000:WF3/2 NYF
FFTNode16 = 0x00304000:WF1/0 DCRF
FFTNode17 = 0x00309000:WF3/2 DCRF

// TX, 2T , TX_DOMAIN
FFTNode18 = 0x20913000:PKLIM 1x
FFTNode19 = 0x20A13000:WF1/0 CIC-1_3x
FFTNode20 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode21 = 0x20E18000:WF3/2 DPD_COMP_IN_3x
FFTNode22 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode23 = 0x20F18000:WF3/2 DPD_COMP_OUT_3x
FFTNode24 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
FFTNode25 = 0x21018000:WF3/2 FLAT_COMP_OUT_3x

// TX, 1T , BW320 TX_DOMAIN
FFTNode26 = 0x20E0F000:WF0 DPD_COMP_IN_3x BW320
FFTNode27 = 0x20E10000:WF1 DPD_COMP_IN_3x BW320
FFTNode28 = 0x20E11000:WF2 DPD_COMP_IN_3x BW320
FFTNode29 = 0x20E12000:WF3 DPD_COMP_IN_3x BW320
FFTNode30 = 0x20F0F000:WF0 DPD_COMP_OUT_3x BW320
FFTNode31 = 0x20F10000:WF1 DPD_COMP_OUT_3x BW320
FFTNode32 = 0x20F11000:WF2 DPD_COMP_OUT_3x BW320
FFTNode33 = 0x20F12000:WF3 DPD_COMP_OUT_3x BW320
FFTNode34 = 0x2100F000:WF0 FLAT_COMP_OUT_3x BW320
FFTNode35 = 0x21010000:WF1 FLAT_COMP_OUT_3x BW320
FFTNode36 = 0x21011000:WF2 FLAT_COMP_OUT_3x BW320
FFTNode37 = 0x21012000:WF3 FLAT_COMP_OUT_3x BW320
FFTNode38 = 0x60024000:WF4 OTFK_ADC
FFTNode39 = 0x60224000:WF4 OTFK_NYF
FFTNode40 = 0x70524000:WF4 OTFK_RXFI
FFTNode41 = 0x70624000:WF4 OTFK_RXFD
FFTNode42 = 0x60D24000:WF4 OTFK_ADC_1280M
FFTNode43 = 0x10424000:WF4 AFIFO_640M
FFTNode44 = 0x10524000:WF4 RXFI_640M
FFTNode45 = 0x10624000:WF4 RXFD_640M
FFTNode46 = 0x00024000:WF4 ADC_640M
FFTNode47 = 0x00D24000:WF4 ADC_1280M
FFTNode48 = 0x00224000:WF4 NYF_640M
FFTNode49 = 0x00324000:WF4 DCRF_640M

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25


//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25

//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25


//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//DPD_COMP_IN_3x
FFT_DPD_COMP_IN_3x_BW5 = 120
FFT_DPD_COMP_IN_3x_BW10 = 120
FFT_DPD_COMP_IN_3x_BW20 = 120
FFT_DPD_COMP_IN_3x_BW40 = 240
FFT_DPD_COMP_IN_3x_BW80 = 480
FFT_DPD_COMP_IN_3x_BW160C = 480
FFT_DPD_COMP_IN_3x_BW320 = 960

GraphScaleDPD_COMP_IN_3x_BW5	= 66.666667
GraphScaleDPD_COMP_IN_3x_BW10   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW20   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW40   = 33.333333
GraphScaleDPD_COMP_IN_3x_BW80   = 16.666666
GraphScaleDPD_COMP_IN_3x_BW160C = 16.666666
GraphScaleDPD_COMP_IN_3x_BW320  = 8.333333

//DPD_COMP_OUT_3x
FFT_DPD_COMP_OUT_3x_BW5	 = 120
FFT_DPD_COMP_OUT_3x_BW10 = 120
FFT_DPD_COMP_OUT_3x_BW20 = 120
FFT_DPD_COMP_OUT_3x_BW40 = 240
FFT_DPD_COMP_OUT_3x_BW80 = 480
FFT_DPD_COMP_OUT_3x_BW160C = 480
FFT_DPD_COMP_OUT_3x_BW320 = 960

GraphScaleDPD_COMP_OUT_3x_BW5 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW10 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW20 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW40 = 33.333333
GraphScaleDPD_COMP_OUT_3x_BW80 = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW160C = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW320  = 8.333333

//FLAT_COMP_OUT_3x
FFT_FLAT_COMP_OUT_3x_BW5 = 120
FFT_FLAT_COMP_OUT_3x_BW10 = 120
FFT_FLAT_COMP_OUT_3x_BW20 = 120
FFT_FLAT_COMP_OUT_3x_BW40 = 240
FFT_FLAT_COMP_OUT_3x_BW80 = 480
FFT_FLAT_COMP_OUT_3x_BW160C = 480
FFT_FLAT_COMP_OUT_3x_BW320 = 960

GraphScaleFLAT_COMP_OUT_3x_BW5 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW10 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW20 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW40 = 33.333333
GraphScaleFLAT_COMP_OUT_3x_BW80 = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW160C = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW320 = 8.333333
	
CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt
CalID12 = TX_DPD_ONLY.txt
CalID13 = TX_TSSI_DNL.txt
CalID14 = TX_FLATNESS.txt
CalID15 = TX_DNL.txt
CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7992]
Band0TxAIDOffSet = 3A9
Band1TxAIDOffSet = 3A9
Band2TxAIDOffSet = 3A9

ATEMode = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 7680
//3776;
11AX = 1;
DBDCChip = 1;
11BE = 1;
;BufferBinFile = eeprom_7663.bin
;RxVectorSetting = Rx_vector_7915.csv

//FFT
FFTShow = 1
;RxVectorSetting = Rx_vector_7915.csv
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 4

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 52
// RX, 2R, PHY_DOMAIN
FFTNode0 = 0x10404000:WF1/0 AFIFO
FFTNode1 = 0x10409000:WF3/2 AFIFO
FFTNode2 = 0x10504000:WF1/0 RXFI
FFTNode3 = 0x10509000:WF3/2 RXFI
FFTNode4 = 0x10604000:WF1/0 RXFD
FFTNode5 = 0x10609000:WF3/2 RXFD
FFTNode6 = 0x10704000:DLPF
FFTNode7 = 0x10804000:AGCDGC

// RX, 2R, ADC_DOMAIN
FFTNode8 = 0x00004000:WF1/0 ADC
FFTNode9 = 0x00009000:WF3/2 ADC
FFTNode10 = 0x00D00000:WF0 ADC_1280M_BW320
FFTNode11 = 0x00D01000:WF1 ADC_1280M_BW320
FFTNode12 = 0x00D02000:WF2 ADC_1280M_BW320
FFTNode13 = 0x00D03000:WF3 ADC_1280M_BW320
FFTNode14 = 0x00204000:WF1/0 NYF
FFTNode15 = 0x00209000:WF3/2 NYF
FFTNode16 = 0x00304000:WF1/0 DCRF
FFTNode17 = 0x00309000:WF3/2 DCRF

// TX, 2T , TX_DOMAIN
FFTNode18 = 0x20913000:PKLIM 1x
FFTNode19 = 0x20A13000:WF1/0 CIC-1_3x
FFTNode20 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode21 = 0x20E18000:WF3/2 DPD_COMP_IN_3x
FFTNode22 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode23 = 0x20F18000:WF3/2 DPD_COMP_OUT_3x
FFTNode24 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
FFTNode25 = 0x21018000:WF3/2 FLAT_COMP_OUT_3x

// TX, 1T , BW320 TX_DOMAIN
FFTNode26 = 0x20E0F000:WF0 DPD_COMP_IN_3x BW320
FFTNode27 = 0x20E10000:WF1 DPD_COMP_IN_3x BW320
FFTNode28 = 0x20E11000:WF2 DPD_COMP_IN_3x BW320
FFTNode29 = 0x20E12000:WF3 DPD_COMP_IN_3x BW320
FFTNode30 = 0x20F0F000:WF0 DPD_COMP_OUT_3x BW320
FFTNode31 = 0x20F10000:WF1 DPD_COMP_OUT_3x BW320
FFTNode32 = 0x20F11000:WF2 DPD_COMP_OUT_3x BW320
FFTNode33 = 0x20F12000:WF3 DPD_COMP_OUT_3x BW320
FFTNode34 = 0x2100F000:WF0 FLAT_COMP_OUT_3x BW320
FFTNode35 = 0x21010000:WF1 FLAT_COMP_OUT_3x BW320
FFTNode36 = 0x21011000:WF2 FLAT_COMP_OUT_3x BW320
FFTNode37 = 0x21012000:WF3 FLAT_COMP_OUT_3x BW320
FFTNode38 = 0x60024000:WF4 OTFK_ADC
FFTNode39 = 0x60224000:WF4 OTFK_NYF
FFTNode40 = 0x70524000:WF4 OTFK_RXFI
FFTNode41 = 0x70624000:WF4 OTFK_RXFD
FFTNode42 = 0x60D24000:WF4 OTFK_ADC_1280M
FFTNode43 = 0x10424000:WF4 AFIFO_640M
FFTNode44 = 0x10524000:WF4 RXFI_640M
FFTNode45 = 0x10624000:WF4 RXFD_640M
FFTNode46 = 0x00024000:WF4 ADC_640M
FFTNode47 = 0x00D24000:WF4 ADC_1280M
FFTNode48 = 0x00224000:WF4 NYF_640M
FFTNode49 = 0x00324000:WF4 DCRF_640M
FFTNode50 = 0x30024000:WF4 DFS_ADC
FFTNode51 = 0x30224000:WF4 DFS_NYF

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25


//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25

//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25


//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//DPD_COMP_IN_3x
FFT_DPD_COMP_IN_3x_BW5 = 120
FFT_DPD_COMP_IN_3x_BW10 = 120
FFT_DPD_COMP_IN_3x_BW20 = 120
FFT_DPD_COMP_IN_3x_BW40 = 240
FFT_DPD_COMP_IN_3x_BW80 = 480
FFT_DPD_COMP_IN_3x_BW160C = 480
FFT_DPD_COMP_IN_3x_BW320 = 960

GraphScaleDPD_COMP_IN_3x_BW5	= 66.666667
GraphScaleDPD_COMP_IN_3x_BW10   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW20   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW40   = 33.333333
GraphScaleDPD_COMP_IN_3x_BW80   = 16.666666
GraphScaleDPD_COMP_IN_3x_BW160C = 16.666666
GraphScaleDPD_COMP_IN_3x_BW320  = 8.333333

//DPD_COMP_OUT_3x
FFT_DPD_COMP_OUT_3x_BW5	 = 120
FFT_DPD_COMP_OUT_3x_BW10 = 120
FFT_DPD_COMP_OUT_3x_BW20 = 120
FFT_DPD_COMP_OUT_3x_BW40 = 240
FFT_DPD_COMP_OUT_3x_BW80 = 480
FFT_DPD_COMP_OUT_3x_BW160C = 480
FFT_DPD_COMP_OUT_3x_BW320 = 960

GraphScaleDPD_COMP_OUT_3x_BW5 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW10 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW20 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW40 = 33.333333
GraphScaleDPD_COMP_OUT_3x_BW80 = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW160C = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW320  = 8.333333

//FLAT_COMP_OUT_3x
FFT_FLAT_COMP_OUT_3x_BW5 = 120
FFT_FLAT_COMP_OUT_3x_BW10 = 120
FFT_FLAT_COMP_OUT_3x_BW20 = 120
FFT_FLAT_COMP_OUT_3x_BW40 = 240
FFT_FLAT_COMP_OUT_3x_BW80 = 480
FFT_FLAT_COMP_OUT_3x_BW160C = 480
FFT_FLAT_COMP_OUT_3x_BW320 = 960

GraphScaleFLAT_COMP_OUT_3x_BW5 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW10 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW20 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW40 = 33.333333
GraphScaleFLAT_COMP_OUT_3x_BW80 = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW160C = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW320 = 8.333333
	
CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt
CalID12 = TX_DPD_ONLY.txt
CalID13 = TX_TSSI_DNL.txt
CalID14 = TX_FLATNESS.txt
CalID15 = TX_DNL.txt
CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7991]
Band0TxAIDOffSet = 3A9
Band1TxAIDOffSet = 3A9
Band2TxAIDOffSet = 3A9

ATEMode = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 7680
//3776;
11AX = 1;
DBDCChip = 1;
11BE = 1;
;BufferBinFile = eeprom_7663.bin
;RxVectorSetting = Rx_vector_7915.csv

//FFT
FFTShow = 1
;RxVectorSetting = Rx_vector_7915.csv
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 3

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 52
// RX, 2R, PHY_DOMAIN
FFTNode0 = 0x10404000:WF1/0 AFIFO
FFTNode1 = 0x10409000:WF3/2 AFIFO
FFTNode2 = 0x10504000:WF1/0 RXFI
FFTNode3 = 0x10509000:WF3/2 RXFI
FFTNode4 = 0x10604000:WF1/0 RXFD
FFTNode5 = 0x10609000:WF3/2 RXFD
FFTNode6 = 0x10704000:DLPF
FFTNode7 = 0x10804000:AGCDGC

// RX, 2R, ADC_DOMAIN
FFTNode8 = 0x00004000:WF1/0 ADC
FFTNode9 = 0x00009000:WF3/2 ADC
FFTNode10 = 0x00D00000:WF0 ADC_1280M_BW320
FFTNode11 = 0x00D01000:WF1 ADC_1280M_BW320
FFTNode12 = 0x00D02000:WF2 ADC_1280M_BW320
FFTNode13 = 0x00D03000:WF3 ADC_1280M_BW320
FFTNode14 = 0x00204000:WF1/0 NYF
FFTNode15 = 0x00209000:WF3/2 NYF
FFTNode16 = 0x00304000:WF1/0 DCRF
FFTNode17 = 0x00309000:WF3/2 DCRF

// TX, 2T , TX_DOMAIN
FFTNode18 = 0x20913000:PKLIM 1x
FFTNode19 = 0x20A13000:WF1/0 CIC-1_3x
FFTNode20 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode21 = 0x20E18000:WF3/2 DPD_COMP_IN_3x
FFTNode22 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode23 = 0x20F18000:WF3/2 DPD_COMP_OUT_3x
FFTNode24 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
FFTNode25 = 0x21018000:WF3/2 FLAT_COMP_OUT_3x

// TX, 1T , BW320 TX_DOMAIN
FFTNode26 = 0x20E0F000:WF0 DPD_COMP_IN_3x BW320
FFTNode27 = 0x20E10000:WF1 DPD_COMP_IN_3x BW320
FFTNode28 = 0x20E11000:WF2 DPD_COMP_IN_3x BW320
FFTNode29 = 0x20E12000:WF3 DPD_COMP_IN_3x BW320
FFTNode30 = 0x20F0F000:WF0 DPD_COMP_OUT_3x BW320
FFTNode31 = 0x20F10000:WF1 DPD_COMP_OUT_3x BW320
FFTNode32 = 0x20F11000:WF2 DPD_COMP_OUT_3x BW320
FFTNode33 = 0x20F12000:WF3 DPD_COMP_OUT_3x BW320
FFTNode34 = 0x2100F000:WF0 FLAT_COMP_OUT_3x BW320
FFTNode35 = 0x21010000:WF1 FLAT_COMP_OUT_3x BW320
FFTNode36 = 0x21011000:WF2 FLAT_COMP_OUT_3x BW320
FFTNode37 = 0x21012000:WF3 FLAT_COMP_OUT_3x BW320
FFTNode38 = 0x60024000:WF4 OTFK_ADC
FFTNode39 = 0x60224000:WF4 OTFK_NYF
FFTNode40 = 0x70524000:WF4 OTFK_RXFI
FFTNode41 = 0x70624000:WF4 OTFK_RXFD
FFTNode42 = 0x60D24000:WF4 OTFK_ADC_1280M
FFTNode43 = 0x10424000:WF4 AFIFO_640M
FFTNode44 = 0x10524000:WF4 RXFI_640M
FFTNode45 = 0x10624000:WF4 RXFD_640M
FFTNode46 = 0x00024000:WF4 ADC_640M
FFTNode47 = 0x00D24000:WF4 ADC_1280M
FFTNode48 = 0x00224000:WF4 NYF_640M
FFTNode49 = 0x00324000:WF4 DCRF_640M
FFTNode50 = 0x30024000:WF4 DFS_ADC
FFTNode51 = 0x30224000:WF4 DFS_NYF

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25


//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25

//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25


//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//DPD_COMP_IN_3x
FFT_DPD_COMP_IN_3x_BW5 = 120
FFT_DPD_COMP_IN_3x_BW10 = 120
FFT_DPD_COMP_IN_3x_BW20 = 120
FFT_DPD_COMP_IN_3x_BW40 = 240
FFT_DPD_COMP_IN_3x_BW80 = 480
FFT_DPD_COMP_IN_3x_BW160C = 480
FFT_DPD_COMP_IN_3x_BW320 = 960

GraphScaleDPD_COMP_IN_3x_BW5	= 66.666667
GraphScaleDPD_COMP_IN_3x_BW10   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW20   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW40   = 33.333333
GraphScaleDPD_COMP_IN_3x_BW80   = 16.666666
GraphScaleDPD_COMP_IN_3x_BW160C = 16.666666
GraphScaleDPD_COMP_IN_3x_BW320  = 8.333333

//DPD_COMP_OUT_3x
FFT_DPD_COMP_OUT_3x_BW5	 = 120
FFT_DPD_COMP_OUT_3x_BW10 = 120
FFT_DPD_COMP_OUT_3x_BW20 = 120
FFT_DPD_COMP_OUT_3x_BW40 = 240
FFT_DPD_COMP_OUT_3x_BW80 = 480
FFT_DPD_COMP_OUT_3x_BW160C = 480
FFT_DPD_COMP_OUT_3x_BW320 = 960

GraphScaleDPD_COMP_OUT_3x_BW5 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW10 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW20 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW40 = 33.333333
GraphScaleDPD_COMP_OUT_3x_BW80 = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW160C = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW320  = 8.333333

//FLAT_COMP_OUT_3x
FFT_FLAT_COMP_OUT_3x_BW5 = 120
FFT_FLAT_COMP_OUT_3x_BW10 = 120
FFT_FLAT_COMP_OUT_3x_BW20 = 120
FFT_FLAT_COMP_OUT_3x_BW40 = 240
FFT_FLAT_COMP_OUT_3x_BW80 = 480
FFT_FLAT_COMP_OUT_3x_BW160C = 480
FFT_FLAT_COMP_OUT_3x_BW320 = 960

GraphScaleFLAT_COMP_OUT_3x_BW5 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW10 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW20 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW40 = 33.333333
GraphScaleFLAT_COMP_OUT_3x_BW80 = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW160C = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW320 = 8.333333
	
CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt
CalID12 = TX_DPD_ONLY.txt
CalID13 = TX_TSSI_DNL.txt
CalID14 = TX_FLATNESS.txt
CalID15 = TX_DNL.txt
CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt

[Settings_MT7996]
Band0TxAIDOffSet = 3A9
Band1TxAIDOffSet = 3A9
Band2TxAIDOffSet = 3A9

ATEMode = 1
nonLegacyPower = 1
CmdDump = 1
UNIFIEDCMD = 1
;SupportBinFile = 1
EFuseSize = 7680
//3776;
11AX = 1;
DBDCChip = 1;
11BE = 1;
;BufferBinFile = eeprom_7663.bin
;RxVectorSetting = Rx_vector_7915.csv

//FFT
FFTShow = 1
;RxVectorSetting = Rx_vector_7915.csv
FFT_TrigMode_Show  = 2
FFT_TrigEvent_Show = 2
FFT_TriggerPhy_Show = 1
FFTTOTWIFI = 4

ADCBit = 12
IQCBit = 14
DLPFBit = 14
AGCDGCBit = 14
PKLIMBit = 12
CICBit = 13
DFSBit = 14
ThermalBit = 8


FFTEventCount = 12
FFTEvent0 = 0x00000000:FreeRun
FFTEvent1 = 0x20000000:EBF_Enable
FFTEvent2 = 0x08000000:MU_RX_Payload
FFTEvent3 = 0x00000008:FCS_Error
FFTEvent4 = 0x00000020:SIG_Error
FFTEvent5 = 0x00000080:LTFSYNC_Abort
FFTEvent6 = 0x00000200:CCK_Abort
FFTEvent7 = 0x00000800:AGC_POP_Trig
FFTEvent8 = 0x80000000:TOAE_Trig
FFTEvent9 = 0x00000002:PD
FFTEvent10 = 0x00000001:Auto
FFTEvent11 = 0xffffffff:Calibration_Trig

FFTNodeCount = 38
// RX, 2R, PHY_DOMAIN
FFTNode0 = 0x10404000:WF1/0 AFIFO
FFTNode1 = 0x10409000:WF3/2 AFIFO
FFTNode2 = 0x10504000:WF1/0 RXFI
FFTNode3 = 0x10509000:WF3/2 RXFI
FFTNode4 = 0x10604000:WF1/0 RXFD
FFTNode5 = 0x10609000:WF3/2 RXFD
FFTNode6 = 0x10704000:DLPF
FFTNode7 = 0x10804000:AGCDGC

// RX, 2R, ADC_DOMAIN
FFTNode8 = 0x00004000:WF1/0 ADC
FFTNode9 = 0x00009000:WF3/2 ADC
FFTNode10 = 0x00D00000:WF0 ADC_1280M_BW320
FFTNode11 = 0x00D01000:WF1 ADC_1280M_BW320
FFTNode12 = 0x00D02000:WF2 ADC_1280M_BW320
FFTNode13 = 0x00D03000:WF3 ADC_1280M_BW320
FFTNode14 = 0x00204000:WF1/0 NYF
FFTNode15 = 0x00209000:WF3/2 NYF
FFTNode16 = 0x00304000:WF1/0 DCRF
FFTNode17 = 0x00309000:WF3/2 DCRF

// TX, 2T , TX_DOMAIN
FFTNode18 = 0x20913000:PKLIM 1x
FFTNode19 = 0x20A13000:WF1/0 CIC-1_3x
FFTNode20 = 0x20E13000:WF1/0 DPD_COMP_IN_3x
FFTNode21 = 0x20E18000:WF3/2 DPD_COMP_IN_3x
FFTNode22 = 0x20F13000:WF1/0 DPD_COMP_OUT_3x
FFTNode23 = 0x20F18000:WF3/2 DPD_COMP_OUT_3x
FFTNode24 = 0x21013000:WF1/0 FLAT_COMP_OUT_3x
FFTNode25 = 0x21018000:WF3/2 FLAT_COMP_OUT_3x

// TX, 1T , BW320 TX_DOMAIN
FFTNode26 = 0x20E0F000:WF0 DPD_COMP_IN_3x BW320
FFTNode27 = 0x20E10000:WF1 DPD_COMP_IN_3x BW320
FFTNode28 = 0x20E11000:WF2 DPD_COMP_IN_3x BW320
FFTNode29 = 0x20E12000:WF3 DPD_COMP_IN_3x BW320
FFTNode30 = 0x20F0F000:WF0 DPD_COMP_OUT_3x BW320
FFTNode31 = 0x20F10000:WF1 DPD_COMP_OUT_3x BW320
FFTNode32 = 0x20F11000:WF2 DPD_COMP_OUT_3x BW320
FFTNode33 = 0x20F12000:WF3 DPD_COMP_OUT_3x BW320
FFTNode34 = 0x2100F000:WF0 FLAT_COMP_OUT_3x BW320
FFTNode35 = 0x21010000:WF1 FLAT_COMP_OUT_3x BW320
FFTNode36 = 0x21011000:WF2 FLAT_COMP_OUT_3x BW320
FFTNode37 = 0x21012000:WF3 FLAT_COMP_OUT_3x BW320

//ADC
FFTADCBW05 = 160
FFTADCBW10 = 160
FFTADCBW20 = 320
FFTADCBW40 = 320
FFTADCBW80 = 320
FFTADCBW160C = 640
FFTADCBW320 = 1280

GraphScaleFFTADCBW05 = 50
GraphScaleFFTADCBW10 = 50
GraphScaleFFTADCBW20 = 50
GraphScaleFFTADCBW40 = 25
GraphScaleFFTADCBW80 = 25
GraphScaleFFTADCBW160C = 12.5
GraphScaleFFTADCBW320 = 6.25


//IQC
FFTIQCBW05 = 10
FFTIQCBW10 = 20
FFTIQCBW20 = 40
FFTIQCBW40 = 80
FFTIQCBW80 = 160
FFTIQCBW160C = 320
FFTIQCBW320 = 640

GraphScaleFFTIQCBW05 = 200
GraphScaleFFTIQCBW10 = 200
GraphScaleFFTIQCBW20 = 200
GraphScaleFFTIQCBW40 = 100
GraphScaleFFTIQCBW80 = 50
GraphScaleFFTIQCBW160C = 25
GraphScaleFFTIQCBW320 = 12.5

//DLPF
FFTDLPFBW05 = 10
FFTDLPFBW10 = 20
FFTDLPFBW20 = 40
FFTDLPFBW40 = 40
FFTDLPFBW80 = 80
FFTDLPFBW160C = 160
FFTDLPFBW320 = 320

GraphScaleFFTDLPFBW05 = 800
GraphScaleFFTDLPFBW10 = 400
GraphScaleFFTDLPFBW20 = 200
GraphScaleFFTDLPFBW40 = 200
GraphScaleFFTDLPFBW80 = 100
GraphScaleFFTDLPFBW160C = 50
GraphScaleFFTDLPFBW320 = 25

//AGCDGC
FFTAGCDGCBW05 = 10
FFTAGCDGCBW10 = 20
FFTAGCDGCBW20 = 40
FFTAGCDGCBW40 = 40
FFTAGCDGCBW80 = 80
FFTAGCDGCBW160C = 160
FFTAGCDGCBW320 = 320

GraphScaleFFTAGCDGCBW05 = 800
GraphScaleFFTAGCDGCBW10 = 400
GraphScaleFFTAGCDGCBW20 = 200
GraphScaleFFTAGCDGCBW40 = 200
GraphScaleFFTAGCDGCBW80 = 100
GraphScaleFFTAGCDGCBW160C = 50
GraphScaleFFTAGCDGCBW320 = 25

//PKLIM-1x
FFTPKLIMBW05 = 20
FFTPKLIMBW10 = 20
FFTPKLIMBW20 = 20
FFTPKLIMBW40 = 40
FFTPKLIMBW80 = 80
FFTPKLIMBW160C = 160
FFTPKLIMBW320 = 320

GraphScaleFFTPKLIMBW05 = 400
GraphScaleFFTPKLIMBW10 = 400
GraphScaleFFTPKLIMBW20 = 400
GraphScaleFFTPKLIMBW40 = 200
GraphScaleFFTPKLIMBW80 = 100
GraphScaleFFTPKLIMBW160C = 50
GraphScaleFFTPKLIMBW320 = 25


//CIC-3x
FFTCICBW05 = 60
FFTCICBW10 = 60
FFTCICBW20 = 60
FFTCICBW40 = 120
FFTCICBW80 = 240
FFTCICBW160C = 480
FFTCICBW320 = 960

GraphScaleFFTCICBW05 = 133.33333
GraphScaleFFTCICBW10 = 133.33333
GraphScaleFFTCICBW20 = 133.33333
GraphScaleFFTCICBW40 = 66.666667
GraphScaleFFTCICBW80 = 33.333333
GraphScaleFFTCICBW160C = 16.666666
GraphScaleFFTCICBW320 = 8.333333

//DFS
FFTDFSBW05 = 10
FFTDFSBW10 = 20
FFTDFSBW20 = 40
FFTDFSBW40 = 80
FFTDFSBW80 = 160

GraphScaleFFTDFSBW05 = 200
GraphScaleFFTDFSBW10 = 200
GraphScaleFFTDFSBW20 = 200
GraphScaleFFTDFSBW40 = 100
GraphScaleFFTDFSBW80 = 50


//THERMAL
FFTTHERMALBW05 = 10
FFTTHERMALBW10 = 20
FFTTHERMALBW20 = 40
FFTTHERMALSBW40 = 80
FFTTHERMALBW80 = 160

GraphScaleFFTTHERMALBW05 = 200
GraphScaleFFTTHERMALBW10 = 200
GraphScaleFFTTHERMALBW20 = 200
GraphScaleFFTTHERMALBW40 = 100
GraphScaleFFTTHERMALBW80 = 50

//DPD_COMP_IN_3x
FFT_DPD_COMP_IN_3x_BW5 = 120
FFT_DPD_COMP_IN_3x_BW10 = 120
FFT_DPD_COMP_IN_3x_BW20 = 120
FFT_DPD_COMP_IN_3x_BW40 = 240
FFT_DPD_COMP_IN_3x_BW80 = 480
FFT_DPD_COMP_IN_3x_BW160C = 480
FFT_DPD_COMP_IN_3x_BW320 = 960

GraphScaleDPD_COMP_IN_3x_BW5	= 66.666667
GraphScaleDPD_COMP_IN_3x_BW10   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW20   = 66.666667
GraphScaleDPD_COMP_IN_3x_BW40   = 33.333333
GraphScaleDPD_COMP_IN_3x_BW80   = 16.666666
GraphScaleDPD_COMP_IN_3x_BW160C = 16.666666
GraphScaleDPD_COMP_IN_3x_BW320  = 8.333333

//DPD_COMP_OUT_3x
FFT_DPD_COMP_OUT_3x_BW5	 = 120
FFT_DPD_COMP_OUT_3x_BW10 = 120
FFT_DPD_COMP_OUT_3x_BW20 = 120
FFT_DPD_COMP_OUT_3x_BW40 = 240
FFT_DPD_COMP_OUT_3x_BW80 = 480
FFT_DPD_COMP_OUT_3x_BW160C = 480
FFT_DPD_COMP_OUT_3x_BW320 = 960

GraphScaleDPD_COMP_OUT_3x_BW5 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW10 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW20 = 66.666667
GraphScaleDPD_COMP_OUT_3x_BW40 = 33.333333
GraphScaleDPD_COMP_OUT_3x_BW80 = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW160C = 16.666666
GraphScaleDPD_COMP_OUT_3x_BW320  = 8.333333

//FLAT_COMP_OUT_3x
FFT_FLAT_COMP_OUT_3x_BW5 = 120
FFT_FLAT_COMP_OUT_3x_BW10 = 120
FFT_FLAT_COMP_OUT_3x_BW20 = 120
FFT_FLAT_COMP_OUT_3x_BW40 = 240
FFT_FLAT_COMP_OUT_3x_BW80 = 480
FFT_FLAT_COMP_OUT_3x_BW160C = 480
FFT_FLAT_COMP_OUT_3x_BW320 = 960

GraphScaleFLAT_COMP_OUT_3x_BW5 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW10 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW20 = 66.666667
GraphScaleFLAT_COMP_OUT_3x_BW40 = 33.333333
GraphScaleFLAT_COMP_OUT_3x_BW80 = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW160C = 16.666666
GraphScaleFLAT_COMP_OUT_3x_BW320 = 8.333333
	
CalID0 = RC_CAL.txt
CalID1 = RSSIADC_DCOC.txt
CalID2 = RX_DCOC.txt
CalID3 = TX_TSSI_DCOC.txt
CalID4 = TX_LPFG.txt
CalID5 = TX_FDIQ.txt
CalID6 = TX_DCIQ.txt
CalID7 = TX_DPD.txt
CalID8 = RX_FDIQ.txt
CalID9 = RX_FIIQ.txt
CalID10 = RX_FD_FI.txt
CalID11 = TX_PA_BIAS.txt

CalID31 = POR_CAL_DUMP.txt
CalID31 = ALL.txt





