/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __EXPORT_H__
#define __EXPORT_H__
#ifdef _WIN32
#if defined(METACONN_EXPORTS) // inside DLL
#define METACONNAPI __declspec(dllexport) __stdcall
#else // outside DLL
#define METACONNAPI __declspec(dllimport) __stdcall
#endif  // METACONN_EXPORTS
#else // #ifdef _WIN32
#define METACONNAPI
#endif

#define METAAPPAPI METACONNAPI

#endif
