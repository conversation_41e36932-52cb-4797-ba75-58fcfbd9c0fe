#include "StdAfx.h"
#include <shlwapi.h>
#include "SPexc.h"
#include "SN Writer.h"
#include "SN WriterDlg.h"
#include "ScanData.h"
#include "HdcpEncryption.h"
#include "C2kAgent_api.h"
//#include "C2kAgent_api_datatype.h"
#include <windows.h>
#include <tchar.h>
#include <string.h>
#include "Encryption.h"
#include "SLA_Challenge.h"
#include <iostream>  
#include <fstream>
#include <sstream>

#include "HttpApi.h"

/*TrustKernel add */
#include "LibKPHA.h"
#include "libuploader.h"
#include "LibTurkey.h"
#include "LibKpaUtil.h"
#include <vector>

#include "CodeDatabase/CodeDatabase.h" // jiali add for ONLY INPUT IMEI
#include <ctime>
#include "MesServerProxy.h"
#include "HTMesProxy.h"
#include <string>
using std::string;

using trustkernel::Config;
using trustkernel::Logger;
using trustkernel::Licenser;
using trustkernel::DefaultLicenser;
using trustkernel::VTurkeyLicenser;

using trustkernel::Device;
using trustkernel::Uploader;

using trustkernel::Turkey;
using trustkernel::TurkeyLicenser;

#define s_MAX_PATH 512

#define META_MODEM_SRV_ETS 3
#define META_MODEM_CH_TUNNELING 2


extern CSNWriterDlg *g_pMainDlg;

static const GUID  GUID_PORT_CLASS_USB2SER = {0x4D36E978L, 0xE325, 0x11CE, {0xBF, 0xC1, 0x08, 0x00, 0x2B, 0xE1, 0x03, 0x18}};

void __stdcall CNF_SPReadFromNVRAM(const AP_FT_NVRAM_READ_CNF *cnf, const short token, void *usrData)
{
    if (cnf->status == META_SUCCESS)
    {
        SetEvent(*((HANDLE*)usrData));
    }
}

void __stdcall CNF_SPWriteToNVRAM ( const AP_FT_NVRAM_WRITE_CNF *cnf, const short token, void *usrData)
{
    if (cnf->status == META_SUCCESS)
    {
        SetEvent(*((HANDLE*)usrData));
    }
}

VerInfo_Cnf VerInfo;
bool bVerCallBackRet;
void __stdcall GetTargetVersionCallBack(const VerInfo_Cnf *cnf, const short token, void *userData)
{
	VerInfo = *cnf;
	bVerCallBackRet = true;
}

SmartPhoneSN::SmartPhoneSN()
{
    memset(&m_stModeArg, 0, sizeof(SP_BOOT_ARG_S));
    memset(&m_sMdInfo, 0, sizeof(SP_MODEMInfo_s));
    m_nKernelComport = 0;
    m_eMetaMode = SP_NOTIN_META;
    m_hMauiMetaHandle = INVALID_META_HANDLE;
    m_hSPMetaHandle = INVALID_META_HANDLE;
    m_bWorldPhone = false;
    m_bWithoutMD = false;
    m_bDualModem = false;
    hdle_write_cmd = NULL;
    hdle_read_out = NULL;
    r_bufsize = sizeof(read_buf);
    memset(&read_buf, 0, r_bufsize);
}

SmartPhoneSN::~SmartPhoneSN()
{
    MetaHandle_DeInit();
    //dongck add for fastboot oem lock
    Adb_WR_Cmd("exit", strlen("exit"));
    //MTRACE(g_hEBOOT_DEBUG, "ATB_MR::Scan_Devices_FunProc(): close cmd.exe");
    memset(&read_buf, 0, r_bufsize);
    CloseHandle(hdle_write_cmd);
    //MTRACE(g_hEBOOT_DEBUG, "Adb_Manager::~Adb_Manager(): close hdle_write_cmd");
    CloseHandle(hdle_read_out);
    //MTRACE(g_hEBOOT_DEBUG, "Adb_Manager::~Adb_Manager(): close hdle_read_out");
}

void SmartPhoneSN::SPInit()
{
    memset(&m_stModeArg, 0, sizeof(SP_BOOT_ARG_S));
    memset(&m_sMdInfo, 0, sizeof(SP_MODEMInfo_s));
    m_nKernelComport = 0;
    m_eMetaMode = SP_NOTIN_META;
    m_bWorldPhone = false;
    m_bWithoutMD = false;
    m_bDualModem = false;
    m_bWriteProdInfo = false;

    m_iCurMDChanelIndex = 0;
    m_bDSDAProject = false;
    m_iC2kProject = 0;
    m_bInitExtMDdb = false;
    memset(m_iMDChannelIndex, 0, (sizeof(UINT)*MAX_MD_CHANNEL_NUM));

    if (g_sMetaComm.sWriteOption.bWriteBarcode ||
        g_sMetaComm.sWriteOption.bWriteIMEI ||
        g_sMetaComm.sWriteOption.bWriteMeid ||
        g_sMetaComm.sWriteOption.bWriteBT ||
        g_sMetaComm.sWriteOption.bWriteWifi ||
        g_sMetaComm.sWriteOption.bWriteSerialNo)
    {
        m_bWriteProdInfo = true;
    }
    else
    {
        m_bWriteProdInfo = false;
    }
}

bool SmartPhoneSN::ReadSignIMEI(char *outSigIMEI)
{
	char FolderPath[MAX_PATH];
	char *FolderPath1;
	char SignatureIMEIFold[]="critical_info.txt";
	char SignIMEI[4096];
	memset(SignIMEI,0,sizeof(SignIMEI));
	GetModuleFileName(NULL, FolderPath, MAX_PATH);
	(_tcsrchr(FolderPath, _T('\\')))[1] = 0;
	FolderPath1=strcat(FolderPath, SignatureIMEIFold);

	std::ifstream fin(FolderPath1, std::ios::in);
	if(!fin)
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::ReadSignIMEI() Signature IMEI File don't exist in exe directory");
		return false;
	}
	fin.getline(SignIMEI, sizeof(SignIMEI));
	fin.clear();
	fin.close();

	for(int i=strlen(SignIMEI);i<4095;i++)
	{
		SignIMEI[i]='0';
	}
	strcpy_s(outSigIMEI,sizeof(SignIMEI),SignIMEI);
	return true;
}

META_RESULT SmartPhoneSN::WriteNvramLoop()
{
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::WriteNvramLoop() start...");

    SMART_PHONE_STAGE_e spStage = SP_START_STAGE;
    SMART_PHONE_STAGE_e spNextStage = SP_END_STAGE;
    META_RESULT MetaResult = META_SUCCESS;
    MULTIIMEI_OPTION_e multiIMEIOption = SINGLE_MD_SINGLE_IMEI;

    int iRet = 0;
    int option = 0;
    double fBeginProcess = 0.25;
    double fStep = 0.04;
    m_bBackupNvramSuccess = false;

    bool bCheckCalFlag = g_sMetaComm.bCheckCalFlag;
    bool bCheckFtFlag = g_sMetaComm.bCheckFtFlag;
	char AP_Flag[1] = {0};

    int mdNums = 1;
    int MDIndex = 0;
    int IMEI_index = 0;
    bool bWriteModemFail = false;
    if (g_sMetaComm.eTargetType == SMART_PHONE_DUALMODEM || m_bDSDAProject)
    {
        mdNums = 2;
        g_sMetaComm.sIMEIOption.iImeiNums = 2;
    }
    else if (g_sMetaComm.eTargetType == TABLET_WIFI_ONLY)
    {
        m_bWriteModemNvram = false;
        bCheckCalFlag = false;
        bCheckFtFlag = false;
        mdNums = 0;
    }
	/*else if (g_sMetaComm.eTargetType == SMART_PHONE)
	{
		mdNums = 1;
	}*/
	else if (g_sMetaComm.eTargetType == THIN_MODEM)
	{
		mdNums = 1;
	}
    else
    {
        mdNums = m_sMdInfo.number_of_md;
		if(mdNums == 0)
		{
			m_bWriteModemNvram = false;
			bCheckCalFlag = false;
			bCheckFtFlag = false;
		}
    }

    while ((spStage != SP_END_STAGE) && (*m_pMetaStopFlag != BOOT_STOP))
    {
        switch (spStage)
        {
        case SP_START_STAGE:
			if ((m_bWriteModemNvram 
				|| (bCheckCalFlag || bCheckFtFlag)) && mdNums >= 1 
				|| g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
            {
                //Target in AP meta mode, need to switch Modem meta
                if (m_eMetaMode == SP_AP_META)
                {
                    spNextStage = SP_AP2MD_STAGE;
                }
                else //Target already in modem meta mode
                {
                    spNextStage = SP_CHECK_CAL_FT_FLAG_STAGE;
                }
            }
            else
            {
                spNextStage = SP_BT_ADDRESS_STAGE;
            }
            break;

        case SP_AP2MD_STAGE:
            fBeginProcess += fStep;
			iRet = MDSLA_Connect();
            if (iRet == META_SUCCESS)
            {
                spNextStage = SP_CHECK_CAL_FT_FLAG_STAGE;
            }
            else
            {
                UpdateUIMsg("ERROR!! APSwithToModemMeta_Ex() : MetaResult = %s", ResultToString(iRet));
                spNextStage = SP_END_STAGE;
            }
            break;

        case SP_CHECK_CAL_FT_FLAG_STAGE:
            if (g_sMetaComm.bCheckCalFlag || g_sMetaComm.bCheckFtFlag)
            {
                char tmpBuf[2048] = {0};
                iRet = REQ_ReadModem_NVRAM_Start(WRITE_BARCODE, tmpBuf, 1);
                if (iRet == META_SUCCESS)
                {
                    bool bCheckPass = true;
                    bCheckPass = CheckCalFinalTestStatus(tmpBuf);
                    if (bCheckPass == true)
                    {
                        spNextStage = SP_BARCODE_MD_STAGE;
                    }
                    else
                    {
                        iRet = META_FAILED;
                        spNextStage = SP_END_STAGE;
                    }
                }
                else
                {
                    UpdateUIMsg("ERROR!! Read Barcode: MetaResult = %s", ResultToString(iRet));
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_BARCODE_MD_STAGE;
            }
            break;

        case SP_BARCODE_MD_STAGE:
            if (g_sMetaComm.sWriteOption.bWriteIMEI)
            {
                if (mdNums == 1 && g_sMetaComm.sIMEIOption.iImeiNums == 1)
                {
                    multiIMEIOption = SINGLE_MD_SINGLE_IMEI;
                }
                else if (mdNums == 1 && g_sMetaComm.sIMEIOption.iImeiNums >= 2)
                {
                    multiIMEIOption = SINGLE_MD_MULTI_IMEI;
                }
                else if (mdNums == 2)
                {
                    if (m_iC2kProject != 0 && g_sMetaComm.sIMEIOption.iImeiNums == 1)
                    {
                        multiIMEIOption = SINGLE_MD_SINGLE_IMEI;
                    }
                    if (m_iC2kProject != 0 && g_sMetaComm.sIMEIOption.iImeiNums >= 2 )
                    {
                        multiIMEIOption = SINGLE_MD_MULTI_IMEI;
                    }
                    if (g_sMetaComm.eTargetType == SMART_PHONE_DUALMODEM && g_sMetaComm.sIMEIOption.iImeiNums == 2)
                    {
                        multiIMEIOption = DUAL_MD_DUAL_IMEI;
                    }
                }
                else
                {
                    multiIMEIOption = MULTIIMEI_RESERVE;
                }
            }
            spNextStage = SP_CHECK_NUU_FLAG_STAGE;

            break;
			
		case SP_CHECK_NUU_FLAG_STAGE:
		{	
			char tmpBuf[2048] = {0};
			char AP_Flag[4];
			if(REQ_ReadModem_NVRAM_Start(WRITE_BARCODE, tmpBuf, 1) == META_SUCCESS)
			{   
				char errMsg[256] = {0};
				char shortBarcode[65] = {0};
				{
					strncpy(shortBarcode, tmpBuf, 64);
					char * p = strchr(shortBarcode, ' ');
					if (p)
					{
						*p = 0;
					}
					strcpy(g_sMetaComm.smesconnect.Sn, shortBarcode);
				}
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Barcode = %s", g_sMetaComm.smesconnect.Sn);

				//read serial.No
				if (g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
				{
					Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
					std::string sn = codes.sn;
					if (sn.empty())
					{
						UpdateUIMsg("ERROR!! sn wrong: [%s]", sn.c_str());
						spNextStage = SP_END_STAGE;
						iRet = META_FAILED;
						break;
					}

					memset(m_sScanData.strSerialNo, 0, sizeof(m_sScanData.strSerialNo));
					strncpy_s(m_sScanData.strSerialNo, sn.c_str(), sizeof(m_sScanData.strSerialNo)-1);
					memset(g_sMetaComm.sScanData.strSerialNo, 0, sizeof(g_sMetaComm.sScanData.strSerialNo));
					strncpy_s(g_sMetaComm.sScanData.strSerialNo, codes.sn.c_str(), sizeof(g_sMetaComm.sScanData.strSerialNo)-1);
				}
				else
				{
					char Imei1[100] = {0};
					char Imei2[100] = {0};
					char Meid1[100] = {0};
					char Meid2[100] = {0};
					char Msn[100] = {0};
					char Psn[100] = {0};
					char Bt[100] = {0};
					char Wifi[100] = {0};
					char attestationkey[100] = {0};
					char keybox[100] = {0};
					char row[100] = {0};
					char readext[100] = {0};
					char wallpaper_id[100] = {0};
					char skuid[100] = {0};
					char emmc[100] = {0};
					char memory[100] = {0};
					char sw_version[100] = {0};
					char message[1024] = {0};

					bool ret2;

					if(g_sMetaComm.smesconnect.strMes[0] == 'L')
					{
						MesServerProxy proxy;

						ret2 = proxy.GetAllCode(g_sMetaComm.smesconnect.Resource, g_sMetaComm.smesconnect.Sn, m_sScanData.strIMEI[0], message);
						if(!ret2)
						{
							UpdateUIMsg("%s", message);
							return META_FAILED;
						}

						string oMessage = message;
						if(oMessage.find("BT") != string::npos)
						{
							string oMessagestr =  oMessage.substr(oMessage.find("BT"));
							string BT = oMessagestr.substr(oMessagestr.find("BT"), oMessagestr.find("/"));
							string BTcode = BT.substr(BT.find(":")+1);
							strcpy(m_sScanData.strBTAddr, BTcode.c_str());
						}
						if(oMessage.find("WIFI") != string::npos)
						{
							string oMessagestr =  oMessage.substr(oMessage.find("WIFI"));
							string WIFI = oMessagestr.substr(oMessagestr.find("WIFI"), oMessagestr.find("/"));
							string WIFIcode = WIFI.substr(WIFI.find(":")+1);
							strcpy(m_sScanData.strWifiAddr, WIFIcode.c_str());
						}
						if(oMessage.find("PSN") != string::npos)
						{
							string oMessagestr =  oMessage.substr(oMessage.find("PSN"));
							string PSN = oMessagestr.substr(oMessagestr.find("PSN"), oMessagestr.find("/"));
							string PSNcode = PSN.substr(PSN.find(":")+1);
							strcpy(m_sScanData.strSerialNo, PSNcode.c_str());
						}
					}
					/*else
					{
						strcpy(g_sMetaComm.smesconnect.Imei,m_sScanData.strIMEI[0]);
						MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::IMEI[0] = %s", m_sScanData.strIMEI[0]);
						ret2 = HTMesProxy::inst()->DB_GetImei3(g_sMetaComm.smesconnect.Gdh,
									g_sMetaComm.smesconnect.Imei,
									Imei1,Imei2,Meid1,Meid2,Msn,Psn,Bt,Wifi,
									attestationkey,keybox,row,readext,wallpaper_id,skuid,emmc,memory,sw_version,
									message);

						if(!ret2)
						{
							UpdateUIMsg("%s", message);
							MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::HTMesProxy::inst()->DB_GetImei3 message = %s", message);
							return META_FAILED;
						}
						else
						{
							strcpy(g_sMetaComm.smesconnect.Imei, Imei1);
							strcpy(m_sScanData.strIMEI[1], Imei2);
							strcpy(g_sMetaComm.smesconnect.imei2, Imei2);
							strcpy(m_sScanData.strBTAddr, Bt);
							strcpy(g_sMetaComm.smesconnect.Bt, Bt);
							strcpy(m_sScanData.strWifiAddr, Wifi);
							strcpy(g_sMetaComm.smesconnect.Wifi, Wifi);
							strcpy(m_sScanData.strSerialNo, Msn);
							strcpy(g_sMetaComm.smesconnect.Msn, Msn);
							strcpy(m_sScanData.strMeid, Meid1);
							strcpy(g_sMetaComm.smesconnect.Meid, Meid1);
							MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::IMEI[1] = %s", m_sScanData.strIMEI[1]);
							MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::BT = %s", m_sScanData.strBTAddr);
							MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Wifi = %s", m_sScanData.strWifiAddr);
							MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SerialNo = %s", m_sScanData.strSerialNo);
							MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Meid = %s", m_sScanData.strMeid);
						}

					}*/
				}
				//read serial.No end
				//check AP flag with serial.No
				if(g_sMetaComm.bCheckFlag)
				{
					if(g_sMetaComm.sCheckFlag.bHardwareinfoFlag){
						char PSN_Flag;
						if(m_sScanData.strSerialNo[4] == '3' && m_sScanData.strSerialNo[5] == '9' && m_sScanData.strSerialNo[6] == 'S')
							PSN_Flag = '1';
						else
						{
							if(m_sScanData.strSerialNo[4] == '3' && m_sScanData.strSerialNo[5] == '9' && m_sScanData.strSerialNo[6] == 'N')
								PSN_Flag = '2';
							else
							{
								if(m_sScanData.strSerialNo[4] == '6' && m_sScanData.strSerialNo[5] == '5' && m_sScanData.strSerialNo[6] == 'S')
									PSN_Flag = '3';
								else
								{
									if(m_sScanData.strSerialNo[4] == '6' && m_sScanData.strSerialNo[5] == '5' && m_sScanData.strSerialNo[6] == 'N')
										PSN_Flag = '4';
								}
							}
						}
						bool ret3 = Read_AP_Flag(AP_Flag);
						if(!ret3)
						{
							UpdateUIMsg("读取AP标志位失败！"); 
							return META_FAILED;
						}
						else
						{
							if(PSN_Flag != AP_Flag[0])
							{
								switch(AP_Flag[0]) 
								{
									case '1' :
										UpdateUIMsg("硬件标志位不匹配，整机SN为 %s ,AP显示为6739+ST！",m_sScanData.strSerialNo); 
										break;
									case '2' :
										UpdateUIMsg("硬件标志位不匹配，整机SN为 %s ,AP显示为6739+NXP！",m_sScanData.strSerialNo); 
										break;
									case '3' :
										UpdateUIMsg("硬件标志位不匹配，整机SN为 %s ,AP显示为8765+ST！",m_sScanData.strSerialNo); 
										break;
									case '4' :
										UpdateUIMsg("硬件标志位不匹配，整机SN为 %s ,AP显示为8765+NXP！",m_sScanData.strSerialNo); 
										break;
									default : 
										UpdateUIMsg("AP端硬件标志位错误！"); 
										break;
								}
								MTRACE(g_hEBOOT_DEBUG, "The Hardware Info Flag is: \"%c\"   mismatch! ",AP_Flag[0]);
								return META_FAILED;
							}
						} 
					}
				}
				//check AP flag with serial.No end
			}
			else
			{
				UpdateUIMsg("获取主板sn失败！");
				return META_FAILED;
			}
			
			spNextStage =SP_CHECK_MD_FLAG;
            break;
		}	
		
		case SP_CHECK_MD_FLAG:
		{
			if(g_sMetaComm.bCheckFlag)
			{
				char The_Flag[65] = {0};
				bool ret = Check_Flag(The_Flag);
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Check MD Flag start...");
				if(ret)
				{
					if(g_sMetaComm.sCheckFlag.bCurrentFlag)
					{
						if(The_Flag[54] != 'P')
						{
							UpdateUIMsg("检查电流标志位失败，barcode为 %s ！",The_Flag);
							MTRACE(g_hEBOOT_DEBUG, "The Current Flag is: \"%c\"   mismatch \"P\"! ",The_Flag[54]);
							return META_FAILED;
						}
				
					}
					if(g_sMetaComm.sCheckFlag.bCoupingFlag)
					{
						if(The_Flag[59] != 'P')
						{
							UpdateUIMsg("检查耦合标志位失败，barcode为 %s ！",The_Flag);
							MTRACE(g_hEBOOT_DEBUG, "The Couping Flag is: \"%c\"   mismatch \"P\"! ",The_Flag[59]);
							return META_FAILED;

						}
					}
				}
				else
				{
					UpdateUIMsg("获取Barcode信息失败！");
					return META_FAILED;
				}
			}
			spNextStage =SP_CHECK_AP_FLAG;
		
            break;	
		}
		case SP_CHECK_AP_FLAG:
		{
			if(g_sMetaComm.bCheckFlag)
			{
				char The_Flag[65] = {0};
				bool ret = Check_Flag(The_Flag);
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Check AP Flag start...");
				if(ret)
				{
					if(g_sMetaComm.sCheckFlag.bMMI1Flag)
					{
						if(The_Flag[55] != 'P')
						{
							UpdateUIMsg("检查MMI1标志位失败，barcode为 %s ！",The_Flag);
							MTRACE(g_hEBOOT_DEBUG, "The MMI1 Flag is: \"%c\"   mismatch \"P\"! ",The_Flag[55]);
							return META_FAILED;
						}
				
					}
					if(g_sMetaComm.sCheckFlag.bAgingFlag)
					{
						if(The_Flag[53] != 'P')
						{
							UpdateUIMsg("检查老化标志位失败，barcode为 %s ！",The_Flag);
							MTRACE(g_hEBOOT_DEBUG, "The Aging Flag is: \"%c\"   mismatch \"P\"! ",The_Flag[53]);
							return META_FAILED;

						}
					}
					if(g_sMetaComm.sCheckFlag.bMMI2Flag)
					{
						if(The_Flag[56] != 'P')
						{
							UpdateUIMsg("检查MMI2标志位失败，barcode为 %s ！",The_Flag);
							MTRACE(g_hEBOOT_DEBUG, "The MMI2 Flag is: \"%c\"   mismatch \"P\"! ",The_Flag[56]);
							return META_FAILED;

						}
					}
					if(g_sMetaComm.sCheckFlag.bMDMFlag)//检查MDM标志位
					{
					   char MDM_flag;
					   MDM_flag = CheckMDMFlag();
					   if (    (MDM_flag == '1' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"DE") != 0)
							|| (MDM_flag == '2' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"GB") != 0)
							|| (MDM_flag == '3' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"FR") != 0)
							|| (MDM_flag == '4' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"DK") != 0)
							|| (MDM_flag == '5' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"FI") != 0)
							|| (MDM_flag == '6' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"NO") != 0)
							|| (MDM_flag == '7' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"SE") != 0)
							|| (MDM_flag == '8' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"NL") != 0)
							|| (MDM_flag == '0' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"CA,FSL2") != 0)
							|| (MDM_flag == 'A' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"AT") != 0)
							|| (MDM_flag == 'B' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"BE") != 0)
							|| (MDM_flag == 'C' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"LU") != 0)
							|| (MDM_flag == 'D' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"IT") != 0)
							|| (MDM_flag == 'E' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"IE") != 0)
							|| (MDM_flag == 'F' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"ES") != 0)
							|| (MDM_flag == 'G' && strcmp(g_sMetaComm.sCheckFlag.strMDM,"CH") != 0)
							|| (MDM_flag == 'H') || (MDM_flag == 'I')
						  )
					   {
						   switch(MDM_flag)
						   {
							   case '1' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 DE\r\n");break;
							   case '2' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 GB\r\n");break;
							   case '3' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 FR\r\n");break;
							   case '4' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 DK\r\n");break;
							   case '5' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 FI\r\n");break;
							   case '6' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 NO\r\n");break;
							   case '7' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 SE\r\n");break;
							   case '8' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 NL\r\n");break;
							   case '0' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 CA,FSL2\r\n");break;
							   case 'A' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 AT\r\n");break;
							   case 'B' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 BE\r\n");break;
							   case 'C' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 LU\r\n");break;
							   case 'D' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 IT\r\n");break;
							   case 'E' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 IE\r\n");break;
							   case 'F' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 ES\r\n");break;
							   case 'G' :UpdateUIMsg("FAIL!配置选择的MDM代码与系统中的MDM代码不符,系统的代码为 CH\r\n");break;
							   case 'H' :UpdateUIMsg("FAIL!读码失败\r\n");break;
							   default : UpdateUIMsg("FAIL!请检查是否有写MDM代码或者代码格式是否正确\r\n");break; 
						   }
						  MTRACE(g_hEBOOT_DEBUG, "The MDM Flag is: \"%c\"  mismatch MDM Code  %s ! ",MDM_flag,g_sMetaComm.sCheckFlag.strMDM);
						  MetaResult = META_FAILED;
						return META_FAILED;
					   }
					}
					if(g_sMetaComm.sCheckFlag.bEfuseFlag)
					{
						if(The_Flag[57] != 'P')
						{
							UpdateUIMsg("检查Efuse标志位失败，barcode为 %s ！",The_Flag);
							MTRACE(g_hEBOOT_DEBUG, "The Efuse Flag is: \"%c\"   mismatch \"P\"! ",The_Flag[57]);
							return META_FAILED;
						}
					}
				}
				else
				{
					UpdateUIMsg("获取Barcode信息失败！");
					return META_FAILED;
				}
			}
			spNextStage = SP_CHECK_SW_VERSION;
		
            break;	
		}
		case SP_CHECK_SW_VERSION:
			if (g_sMetaComm.bcheckversion)
			{
				if (!checkversion_EX())
				{
					UpdateUIMsg("检查软件版本号失败！");
					MetaResult = META_FAILED;
					return META_FAILED;
				}
			}
			spNextStage = SP_CHECK_SW_INTNUMBER;
			break;
        case SP_CHECK_SW_INTNUMBER:
			if (g_sMetaComm.bcheckint)
			{
				if (!checkIntNumber())
				{
					UpdateUIMsg("检查Int Number失败！");
					MetaResult = META_FAILED;
					return META_FAILED;
				}
			}
			spNextStage = SP_IMEI_MD_STAGE;
			break;
        case SP_IMEI_MD_STAGE:
		{	
			if(g_sMetaComm.smesconnect.mesconnectcheck == TRUE)
			{
				bool ret;
				char *error = (char *)malloc(sizeof(char)*1024);
				//char *message = (char *)malloc(sizeof(char)*1024);
				char Xhbz[2];
				if(g_sMetaComm.smesconnect.strMes[0] == 'L')
				{
					MesServerProxy proxy;
					
					ret = proxy.CheckRoutePassed(g_sMetaComm.smesconnect.Sn, g_sMetaComm.smesconnect.Resource, error);
					if(!ret)
					{
						//MessageBox(NULL, error, "fail", MB_OK);
						UpdateUIMsg("%s", error);
						return META_FAILED;
					}
				}
				else
				{
					strcpy(m_sScanData.strIMEI[0], g_sMetaComm.smesconnect.Imei);
					strcpy(m_sScanData.strIMEI[1], g_sMetaComm.smesconnect.imei2);
					strcpy(m_sScanData.strBTAddr, g_sMetaComm.smesconnect.Bt);
					strcpy(m_sScanData.strWifiAddr, g_sMetaComm.smesconnect.Wifi);
					strcpy(m_sScanData.strSerialNo, g_sMetaComm.smesconnect.Msn);
					strcpy(m_sScanData.strMeid, g_sMetaComm.smesconnect.Meid);
					/*char Imei1[100] = {0};
					char Imei2[100] = {0};
					char Meid1[100] = {0};
					char Meid2[100] = {0};
					char Msn[100] = {0};
					char Psn[100] = {0};
					char Bt[100] = {0};
					char Wifi[100] = {0};
					char attestationkey[100] = {0};
					char keybox[100] = {0};
					char row[100] = {0};
					char readext[100] = {0};
					char wallpaper_id[100] = {0};
					char skuid[100] = {0};
					char emmc[100] = {0};
					char memory[100] = {0};
					char sw_version[100] = {0};
					char message[1024] = {0};

					//strcpy(g_sMetaComm.smesconnect.Imei,m_sScanData.strIMEI[0]);
					ret = HTMesProxy::inst()->DB_GetImei3(g_sMetaComm.smesconnect.Gdh,
								g_sMetaComm.smesconnect.Sn,
								Imei1,Imei2,Meid1,Meid2,Msn,Psn,Bt,Wifi,
								attestationkey,keybox,row,readext,wallpaper_id,skuid,emmc,memory,sw_version,
								message);

					if(!ret)
					{
						UpdateUIMsg("%s", message);
						return META_FAILED;
					}
					else
					{
						strcpy(g_sMetaComm.smesconnect.Imei, Imei1);
						strcpy(m_sScanData.strIMEI[1], Imei2);
						strcpy(g_sMetaComm.smesconnect.imei2, Imei2);
						strcpy(m_sScanData.strBTAddr, Bt);
						strcpy(g_sMetaComm.smesconnect.Bt, Bt);
						strcpy(m_sScanData.strWifiAddr, Wifi);
						strcpy(g_sMetaComm.smesconnect.Wifi, Wifi);
						strcpy(m_sScanData.strSerialNo, Msn);
						strcpy(g_sMetaComm.smesconnect.Msn, Msn);
						strcpy(m_sScanData.strMeid, Meid1);
						strcpy(g_sMetaComm.smesconnect.Meid, Meid1);
					}
					/*ret = HTMesProxy::inst()->DB_GetXhbz(g_sMetaComm.smesconnect.Gdh,g_sMetaComm.smesconnect.Imei, Xhbz, error);
					if(!ret)
					{ 
						UpdateUIMsg("%s", error);
						return META_FAILED;
					}
					else
					{
						if(Xhbz[0] == 'Y')
						{
							UpdateUIMsg("该imei已写过码！");
							return META_FAILED;
						}
					}*/
				}
			}
			//
            bWriteModemFail = false;
            for (MDIndex = 0; MDIndex < mdNums && bWriteModemFail == false; MDIndex++)
            {
                //When MDIndex = 0, database already init by EnterModemMetaToInitModemDB() function
                //So just need to load MDIndex >= 1 MD database
                if (mdNums >= 2 && MDIndex >= 1 && m_iC2kProject == 0)
                {
                    fBeginProcess += fStep;
                    iRet = SwitchMDByIndex(MDIndex);
                    if (iRet != META_SUCCESS)
                    {
                        UpdateUIMsg("ERROR!!SwitchMDByIndex[%d] : MetaResult = %s", MDIndex, ResultToString(iRet));
                        bWriteModemFail = true;
                        break;
                    }
                    else
                    {
                        fBeginProcess += fStep;
                        iRet = LoadModemDatabase(MDIndex);
                        if (iRet != META_SUCCESS)
                        {
                            UpdateUIMsg("ERROR!!LoadModemDatabase[%d] : MetaResult = %s", MDIndex, ResultToString(iRet));
                            bWriteModemFail = true;
                            break;
                        }
                    }
                }

                if (g_sMetaComm.sWriteOption.bWriteBarcode && (MDIndex == 0 || (mdNums >= 2 && MDIndex >= 1 && m_iC2kProject == 0)))
                {
                    fBeginProcess += fStep;
                    // jiali add begin
					if(g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
                    {
                        Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
                        std::string barcode = codes.barcode;
                        if (barcode.empty())
                        {
                            UpdateUIMsg("ERROR!! barcode wrong: [%s]", barcode.c_str());
                            spNextStage = SP_END_STAGE;
                            iRet = META_FAILED;
                            break;
                        }

                        memset(m_sScanData.strBarcode, 0, sizeof(m_sScanData.strBarcode));
                        strncpy_s(m_sScanData.strBarcode, barcode.c_str(), sizeof(m_sScanData.strBarcode)-1);
                        memset(g_sMetaComm.sScanData.strBarcode, 0, sizeof(g_sMetaComm.sScanData.strBarcode));
                        strncpy_s(g_sMetaComm.sScanData.strBarcode, barcode.c_str(), sizeof(g_sMetaComm.sScanData.strBarcode)-1);
                    }
                    // jiali add end
                    MTRACE(g_hEBOOT_DEBUG, "Barcode[%d] = \"%s\"", MDIndex, m_sScanData.strBarcode);
                    iRet = REQ_WriteModem_NVRAM_Start(WRITE_BARCODE, m_sScanData.strBarcode, 1);
                    if (iRet != META_SUCCESS)
                    {
                        UpdateUIMsg("ERROR!! Barcode[%d] : MetaResult = %s", MDIndex, ResultToString(iRet));
                        bWriteModemFail = true;
                        break;
                    }
                }

                if (g_sMetaComm.sWriteOption.bWriteIMEI  && (MDIndex == 0 || (mdNums >= 2 && MDIndex >= 1 && m_iC2kProject == 0)))
                {
                    if (multiIMEIOption == SINGLE_MD_SINGLE_IMEI || multiIMEIOption == SINGLE_MD_MULTI_IMEI)
                    {
						if(g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
						{
	                        for (int i = 0; i < g_sMetaComm.sIMEIOption.iImeiNums && iRet==META_SUCCESS; i++) // jiali modify
	                        {
	                            fBeginProcess += fStep;
	                            IMEI_index = i;

	                            // jiali add begin
	                            if (i == 0 || i == 1)
	                            {
	                                // set imei1/imei2 valuable
	                                Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
	                                std::string imei = i == 0 ? codes.imei1 : codes.imei2;
	                                if (imei.empty())
	                                {
	                                    UpdateUIMsg("ERROR!! imei[%d] wrong: [%s]", i+1, imei.c_str());
	                                    spNextStage = SP_END_STAGE;
	                                    iRet = META_FAILED;
	                                    break;
	                                }

	                                memset(m_sScanData.strIMEI[i], 0, sizeof(m_sScanData.strIMEI[i]));
	                                strncpy_s(m_sScanData.strIMEI[i], imei.c_str(), sizeof(m_sScanData.strIMEI[i])-1);
	                                memset(g_sMetaComm.sScanData.strIMEI[i], 0, sizeof(g_sMetaComm.sScanData.strIMEI[i]));
	                                strncpy_s(g_sMetaComm.sScanData.strIMEI[i], imei.c_str(), sizeof(g_sMetaComm.sScanData.strIMEI[i])-1);
	                            }
	                            // jiali add end
								
								MTRACE(g_hEBOOT_DEBUG, "IMEI[%d] = \"%s\"", i, m_sScanData.strIMEI[i]);

	                            iRet = REQ_WriteModem_NVRAM_Start(WRITE_IMEI, m_sScanData.strIMEI[i], i + 1);
	                            if (iRet != META_SUCCESS)
	                            {
	                                if (m_bNeedBackupIMEI)
	                                {
	                                    strcpy_s(g_AutoGenData.sIMEI.Next, m_strBackupIMEI);
	                                }

	                                UpdateUIMsg("ERROR!! IMEI[%d] : MetaResult = %s", i, ResultToString(iRet));
	                                bWriteModemFail = true;
	                                break;
	                            }
							}
						}
						else
						{
							for (int i = 0; i < g_sMetaComm.sIMEIOption.iImeiNums; i++)
	                        {
	                            fBeginProcess += fStep;
	                            IMEI_index = i;
	                            MTRACE(g_hEBOOT_DEBUG, "IMEI[%d] = \"%s\"", i, m_sScanData.strIMEI[i]);

	                            iRet = REQ_WriteModem_NVRAM_Start(WRITE_IMEI, m_sScanData.strIMEI[i], i + 1);
	                            if (iRet != META_SUCCESS)
	                            {
	                                if (m_bNeedBackupIMEI)
	                                {
	                                    strcpy_s(g_AutoGenData.sIMEI.Next, m_strBackupIMEI);
	                                }

	                                UpdateUIMsg("ERROR!! IMEI[%d] : MetaResult = %s", i, ResultToString(iRet));
	                                bWriteModemFail = true;
	                                break;
	                            }
	                        }
						}
                    }
                    else if (multiIMEIOption == DUAL_MD_DUAL_IMEI)
                    {
                        MTRACE(g_hEBOOT_DEBUG, "jiali, exit here #uadhfig"); // jiali add, 暂时没有这里的需求，如果遇到先报错再完善
                        exit(-1); // jiali add, 暂时没有这里的需求，如果遇到先报错再完善
                        fBeginProcess += fStep;
                        int i = 0;
                        i = MDIndex;
                        IMEI_index = MDIndex;
                        if (m_bDSDAProject)
                        {
                            IMEI_index = 0;
                        }

                        MTRACE(g_hEBOOT_DEBUG, "IMEI[%d] = \"%s\"", i, m_sScanData.strIMEI[i]);
                        iRet = REQ_WriteModem_NVRAM_Start(WRITE_IMEI, m_sScanData.strIMEI[i], IMEI_index + 1);
                        if (iRet != META_SUCCESS)
                        {
                            if (m_bNeedBackupIMEI)
                            {
                                strcpy_s(g_AutoGenData.sIMEI.Next, m_strBackupIMEI);
                            }

                            UpdateUIMsg("ERROR!! IMEI[%d] : MetaResult = %s", i, ResultToString(iRet));
                            bWriteModemFail = true;
                            break;
                        }
                    }

                    if (iRet == META_SUCCESS)
                    {
                        if ((IMEI_index + 1) == g_sMetaComm.sIMEIOption.iImeiNums)
                        {
                            if (g_sMetaComm.sIMEIOption.bLockIMEI)
                            {
                                iRet = META_NVRAM_LockDown_r(m_hMauiMetaHandle, 5000);
                                if (iRet != META_SUCCESS)
                                {
                                    bWriteModemFail = true;
                                    break;
                                }
                            }
                        }
                    }
                    else
                    {
                        bWriteModemFail = true;
                        break;
                    }
                }//end if(g_sMetaComm.sWriteOption.bWriteIMEI)
            }//end for(MDIndex = 0; MDIndex < mdNums; MDIndex++)

            // Backup imei to SDS
            if (!bWriteModemFail && m_bDSDAProject)
            {
                bool backupResult = DSDA_ExternalModemBackup();
                if (!backupResult)
                {
                    UpdateUIMsg("ERROR!! Backup external modem fail!");
                    spNextStage = SP_C2K_STAGE;
                    break;
                }
            }

            if (bWriteModemFail == true)
            {
                spNextStage = SP_END_STAGE;
            }
            else
            {
                spNextStage = SP_C2K_STAGE;
            }

            break;
}
        case SP_C2K_STAGE:
            fBeginProcess += fStep;
            if (g_sMetaComm.sWriteOption.bWriteMeid || g_sMetaComm.sWriteOption.bWriteEsn)
            {
                // jiali add begin
				if(g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
                {
                    // try set meid valuable
                    Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
                    std::string meid = codes.meid;
                    if (meid.empty())
                    {
                        UpdateUIMsg("ERROR!! meid wrong: [%s]", meid.c_str());
                        spNextStage = SP_END_STAGE;
                        iRet = META_FAILED;
                        break;
                    }

                    memset(m_sScanData.strMeid, 0, sizeof(m_sScanData.strMeid));
                    strncpy_s(m_sScanData.strMeid, meid.c_str(), sizeof(m_sScanData.strMeid)-1);
                    memset(g_sMetaComm.sScanData.strMeid, 0, sizeof(g_sMetaComm.sScanData.strMeid));
                    strncpy_s(g_sMetaComm.sScanData.strMeid, meid.c_str(), sizeof(g_sMetaComm.sScanData.strMeid)-1);
                }
                // jiali add end

                if (m_iC2kProject != 0)
                    iRet = EnterC2KGen90();
                else
                    iRet = EnterC2KGen93();

                if (iRet != META_SUCCESS)
                {
                    spNextStage = SP_END_STAGE;
					break;
				}
			}

			if (g_sMetaComm.sWriteOption.bWriteSIMEI)
			{
				char signIMEI[4096]={0};
				bool bReadSuccess = ReadSignIMEI(signIMEI);
				if(!bReadSuccess)
				{
					UpdateUIMsg("ERROR!! Signature IMEI file don't exist in exe Dir");
					spNextStage = SP_END_STAGE;
					iRet = META_FAILED;
					break;
				}
				MTRACE(g_hEBOOT_DEBUG, "SignaturedIMEI[%d] = \"%s\" length=%d", MDIndex, signIMEI, strlen(signIMEI));
				iRet = REQ_WriteSignatureIMEI_NVRAM_Start(signIMEI, 1);
				if (iRet != META_SUCCESS)
				{
					UpdateUIMsg("ERROR!! SignatureIMEI[%d] : MetaResult = %s", MDIndex, ResultToString(iRet));
                    spNextStage = SP_END_STAGE;
                    break;
                }
            }
            spNextStage = SP_MD2AP_STAGE;
            break;

        case SP_MD2AP_STAGE:

            if (g_sMetaComm.sIMEIOption.bLockOtp)
            {
                iRet = LockOTP();
                if (iRet != 0)
                {
                    spNextStage = SP_END_STAGE;
                    break;
                }
            }

            fBeginProcess += fStep;
            spNextStage = SP_BT_ADDRESS_STAGE;
            break;

        case SP_BT_ADDRESS_STAGE:
            if (g_sMetaComm.sWriteOption.bWriteBT)
            {
                // jiali add begin
				if(g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
				{
	                Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
	                std::string writableMac = codes.getWritableMacAddr(codes.bt_mac);
	                if (writableMac.empty())
	                {
	                    UpdateUIMsg("ERROR!! bt address wrong: [%s]", codes.bt_mac.c_str());
	                    spNextStage = SP_END_STAGE;
	                }
	                memset(m_sScanData.strBTAddr, 0, sizeof(m_sScanData.strBTAddr));
	                strncpy_s(m_sScanData.strBTAddr, codes.bt_mac.c_str(), sizeof(m_sScanData.strBTAddr)-1);
	                memset(g_sMetaComm.sScanData.strBTAddr, 0, sizeof(g_sMetaComm.sScanData.strBTAddr));
	                strncpy_s(g_sMetaComm.sScanData.strBTAddr, codes.bt_mac.c_str(), sizeof(g_sMetaComm.sScanData.strBTAddr)-1);
	                // jiali add end

	                fBeginProcess += fStep;
	                MTRACE(g_hEBOOT_DEBUG, "BT = \"%s\"", m_sScanData.strBTAddr);
	                iRet = REQ_WriteAP_NVRAM_Start(WRITE_BT, m_sScanData.strBTAddr, 1);
				}
				else
				{
					MTRACE(g_hEBOOT_DEBUG, "BT = \"%s\"", m_sScanData.strBTAddr);
	                iRet = REQ_WriteAP_NVRAM_Start(WRITE_BT, m_sScanData.strBTAddr, 1);
				}
                if (iRet == META_SUCCESS)
                {
                    spNextStage = SP_WIFI_ADDRESS_STAGE;
                }
                else
                {
                    UpdateUIMsg("ERROR!! BT : MetaResult = %s", ResultToString_SP(iRet));
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_WIFI_ADDRESS_STAGE;
            }
            break;

        case SP_WIFI_ADDRESS_STAGE:
            if (g_sMetaComm.sWriteOption.bWriteWifi)
            {
                // jiali add begin
				if(g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
				{
	                Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
	                std::string writableMac = codes.getWritableMacAddr(codes.wifi_mac);
	                if (writableMac.empty())
	                {
	                    UpdateUIMsg("ERROR!! bt address wrong: [%s]", codes.wifi_mac.c_str());
	                    spNextStage = SP_END_STAGE;
	                }
	                memset(m_sScanData.strWifiAddr, 0, sizeof(m_sScanData.strWifiAddr));
	                strncpy_s(m_sScanData.strWifiAddr, codes.wifi_mac.c_str(), sizeof(m_sScanData.strWifiAddr)-1);
	                memset(g_sMetaComm.sScanData.strWifiAddr, 0, sizeof(g_sMetaComm.sScanData.strWifiAddr));
	                strncpy_s(g_sMetaComm.sScanData.strWifiAddr, codes.wifi_mac.c_str(), sizeof(g_sMetaComm.sScanData.strWifiAddr)-1);
	                // jiali add end

	                fBeginProcess += fStep;
	                MTRACE(g_hEBOOT_DEBUG, "Wifi = \"%s\"", m_sScanData.strWifiAddr);
	                iRet = REQ_WriteAP_NVRAM_Start(WRITE_WIFI, m_sScanData.strWifiAddr, 1);
				}
				else
				{
					fBeginProcess += fStep;
	                MTRACE(g_hEBOOT_DEBUG, "Wifi = \"%s\"", m_sScanData.strWifiAddr);
	                iRet = REQ_WriteAP_NVRAM_Start(WRITE_WIFI, m_sScanData.strWifiAddr, 1);
				}
                if (iRet == META_SUCCESS)
                {
                    spNextStage = SP_ETHERNET_MAC_STAGE;
                }
                else
                {
                    UpdateUIMsg("ERROR!! Wifi : MetaResult = %s", ResultToString_SP(iRet));
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_ETHERNET_MAC_STAGE;
            }
            break;

        case SP_ETHERNET_MAC_STAGE:
            if (g_sMetaComm.sWriteOption.bWriteEthernetMac)
            {
                fBeginProcess += fStep;
                MTRACE(g_hEBOOT_DEBUG, "Ethernet Mac = \"%s\"", m_sScanData.strEthernetMac);
                iRet = REQ_WriteAP_NVRAM_Start(WRITE_ETHERNET_MAC, m_sScanData.strEthernetMac, 1);
                if (iRet == META_SUCCESS)
                {
                    spNextStage = SP_HDCP_STAGE;
                }
                else
                {
                    UpdateUIMsg("ERROR!! Ethernet Mac : MetaResult = %s", ResultToString_SP(iRet));
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_HDCP_STAGE;
            }
            break;

        case SP_HDCP_STAGE:
            fBeginProcess += fStep;
            if (g_sMetaComm.sWriteOption.bWriteHdcp)
            {
                iRet = REQ_WriteHdcpBinToAPNvram_Start(g_sMetaComm.sLoadFile.strHdcpBinPath);
                if (iRet == META_SUCCESS)
                {
                    spNextStage = SP_DRMKEY_STAGE;
                }
                else
                {
                    UpdateUIMsg("ERROR!! Hdcp : MetaResult = %s", ResultToString_SP(iRet));
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_DRMKEY_STAGE;
            }
            break;

        case SP_DRMKEY_STAGE:
            fBeginProcess += fStep;
            if (g_sMetaComm.sWriteOption.bWriteDrm)
            {
                iRet = REQ_InstallDRMKey_Start();
                if (iRet == META_SUCCESS)
                {
                    spNextStage = SP_HDCPDATA_INSTALL_STAGE;
                }
                else
                {
                    UpdateUIMsg("ERROR!! DRMKey : MetaResult = %s", ResultToString_SP(iRet));
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_HDCPDATA_INSTALL_STAGE;
            }
            break;

        case SP_HDCPDATA_INSTALL_STAGE:
            fBeginProcess += fStep;
            if (g_sMetaComm.sWriteOption.bInstallHdcpData)
            {
                iRet = REQ_InstallHdcpData_Start(g_sMetaComm.sLoadFile.strHdcpDataPath, g_sMetaComm.sLoadFile.strHdcpCekPath);
                if (iRet == META_SUCCESS)
                {
                    spNextStage = SP_DRMKEY_MCID_STAGE;
                }
                else
                {
                    UpdateUIMsg("ERROR!! HdcpData : MetaResult = %s", ResultToString_SP(iRet));
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_DRMKEY_MCID_STAGE;
            }
            break;

        case SP_DRMKEY_MCID_STAGE:
            fBeginProcess += fStep;
            if (g_sMetaComm.sWriteOption.bWriteDrmkeyMCID)
            {
                iRet = REQ_WriteDRMKeyMCID_Start(m_sScanData.strDrmkeyMCID);
                if (iRet == META_SUCCESS)
                {
                    spNextStage = SP_ATTESTATIONKEY_STAGE;
                }
                else
                {
                    UpdateUIMsg("ERROR!! DRMKeyMCID : MetaResult = %s", ResultToString_SP(iRet));
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_ATTESTATIONKEY_STAGE;
            }
            break;

        case SP_ATTESTATIONKEY_STAGE:
            fBeginProcess += fStep;
            if (!g_sMetaComm.sWriteOption.bWriteAttestationKey)
            {
                spNextStage = SP_BACKUPNV_TO_PC_STAGE;
                break;
            }

            iRet = REQ_InstallAttestationKey_Start(g_sMetaComm.sLoadFile.strAttestationKeyPath);
            if (iRet != 0)
            {
                spNextStage = SP_END_STAGE;
                break;
            }

            spNextStage = SP_BACKUPNV_TO_PC_STAGE;
            break;

        case SP_BACKUPNV_TO_PC_STAGE:
            if (g_sMetaComm.bCheckBackNVtoPC)
            {
                fBeginProcess += fStep;
                char NumFile[128] = {0};
                iRet = GetNvramFileName(NumFile, 128);

                if (iRet == META_SUCCESS)
                {
                    iRet = REQ_BackupNvram2PC_Start(NumFile);
                    if (iRet != META_SUCCESS)
                    {
                        UpdateUIMsg("ERROR!! REQ_BackupNvram2PC_Start : MetaResult = %s", ResultToString_SP(iRet));
                        spNextStage = SP_END_STAGE;
                    }
                    else
                    {
                        spNextStage = SP_BACKUP_NVRAM_STAGE;
                    }
                }
                else
                {
                    spNextStage = SP_END_STAGE;
                }
            }
            else
            {
                spNextStage = SP_BARCODE_MD_FLAG_STAGE;
            }
            break;
		
		//zhangqi modify for set SN writer flag to barcode start
		case SP_BARCODE_MD_FLAG_STAGE:
		{
				//Target in AP meta mode, need to switch Modem meta
				if (m_eMetaMode == SP_AP_META)
				{
					iRet = EnableModemMeta();
					if (iRet == META_SUCCESS)
					{
						spNextStage = SP_PRODINFO_STAGE;
					}
					else
					{
						UpdateUIMsg("ERROR!! APSwithToModemMeta_Ex() : MetaResult = %s", ResultToString(iRet));
						spNextStage = SP_END_STAGE;
						break;
					}
					
				}
				else //Target already in modem meta mode
				{
					spNextStage = SP_PRODINFO_STAGE;
				}
				
				char tmpBuf[2048] = {0};
				iRet = REQ_ReadModem_NVRAM_Start(WRITE_BARCODE, tmpBuf, 1);
				if (iRet == META_SUCCESS)
				{
					memcpy(m_sScanData.strBarcode, tmpBuf, sizeof(m_sScanData.strBarcode));
				}
				else
				{
					UpdateUIMsg("ERROR!! Read Barcode: MetaResult = %s", ResultToString(iRet));
					spNextStage = SP_END_STAGE;
					break;
				}
				fBeginProcess += fStep;
				MTRACE(g_hEBOOT_DEBUG, "BarcodeFlag[%d] = \"%s\"", MDIndex, m_sScanData.strBarcode);
				iRet = REQ_WriteModem_NVRAM_Start(WRITE_BARCODE_FLAG, m_sScanData.strBarcode, 1);
				if (iRet != META_SUCCESS)
				{
					//UpdateUIMsg("ERROR!! SNWriterFlag[%d] : MetaResult = %s", MDIndex, ResultToString(iRet));
					UpdateUIMsg("ERROR!! 写码标志位写入失败，请检查主板是否已写入barcode！");
					bWriteModemFail = true;
					spNextStage = SP_END_STAGE;
					break;
				}
				spNextStage = SP_BACKUP_NVRAM_STAGE;
                break;

			}
			//zhangqi modify for set SN writer flag to barcode end

        case SP_BACKUP_NVRAM_STAGE:
        {
            fBeginProcess += fStep;

            iRet = REQ_BackupNvram2BinRegion_Start();
            if (iRet != META_SUCCESS)
            {
                m_bBackupNvramSuccess = false;
                UpdateUIMsg("ERROR!! BackUP : MetaResult = %s", ResultToString_SP(iRet));
                spNextStage = SP_END_STAGE;
            }
            else
            {
                m_bBackupNvramSuccess = true;
                spNextStage = SP_PRODINFO_STAGE;  //zhangqi modify for set SN writer flag to barcode
            }

            break;
        }
        case SP_PRODINFO_STAGE:
        {
            if (m_bWriteProdInfo && g_sMetaComm.bSkipWriteProdInfo == false)
            {
                MTRACE(g_hEBOOT_DEBUG, "Prod_Info = \"...\"");

                // jiali add begin
                if (g_sMetaComm.sWriteOption.bWriteSerialNo && g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
                {
                    Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
                    std::string sn = codes.sn;
                    if (sn.empty())
                    {
                        UpdateUIMsg("ERROR!! sn wrong: [%s]", sn.c_str());
                        spNextStage = SP_END_STAGE;
                        iRet = META_FAILED;
                        break;
                    }

                    memset(m_sScanData.strSerialNo, 0, sizeof(m_sScanData.strSerialNo));
                    strncpy_s(m_sScanData.strSerialNo, sn.c_str(), sizeof(m_sScanData.strSerialNo)-1);
                    memset(g_sMetaComm.sScanData.strSerialNo, 0, sizeof(g_sMetaComm.sScanData.strSerialNo));
                    strncpy_s(g_sMetaComm.sScanData.strSerialNo, codes.sn.c_str(), sizeof(g_sMetaComm.sScanData.strSerialNo)-1);
                }
                // jiali add end

				if (g_sMetaComm.sWriteOption.bWriteSerialNo && g_sMetaComm.smesconnect.mesconnectcheck == TRUE)
                {
                    memset(g_sMetaComm.sScanData.strSerialNo, 0, sizeof(g_sMetaComm.sScanData.strSerialNo));
                    strncpy_s(g_sMetaComm.sScanData.strSerialNo, m_sScanData.strSerialNo, sizeof(g_sMetaComm.sScanData.strSerialNo)-1);
                }
				
                fBeginProcess += fStep;
                iRet = REQ_WriteAP_PRODINFO_Start();
                if (iRet != META_SUCCESS)
                {
                    UpdateUIMsg("ERROR!! Prod_Info : MetaResult = %s", ResultToString_SP(iRet));
                }
            }
            spNextStage = SP_GetCsrFromDut_STAGE;

            break;
        }

		case SP_GetCsrFromDut_STAGE:
            fBeginProcess += fStep;
            if (!g_sMetaComm.sWriteOption.bGetCsrFromDut)
            {
                spNextStage = SP_FACTORY_RESET_STAGE;
                break;
            }

            iRet = REQ_GetCsrFromDut_Start();
            if (iRet != 0)
            {
				UpdateUIMsg("ERROR!! GetCsrFromDut failed!");
                spNextStage = SP_END_STAGE;
				iRet = META_FAILED;
                break;
            }

			spNextStage = SP_FACTORY_RESET_STAGE;
            break;

		case SP_FACTORY_RESET_STAGE:
			if (g_sMetaComm.bfactoryreset)
			{
				if (!FactoryReset())
				{
					UpdateUIMsg("恢复出厂设置失败！");
					iRet = META_FAILED;
					spNextStage = SP_END_STAGE;
					break;
				}
			}
			spNextStage = SP_Reboot;
			break;

		case SP_Reboot:
			if (g_sMetaComm.breboot)
			{
				if (!Reboot())
				{
					UpdateUIMsg("重启失败！");
					iRet = META_FAILED;
					spNextStage = SP_END_STAGE;
					break;
				}
				Sleep(3000);
			}
			spNextStage = SP_END_STAGE;
			break; 

        default:
            // for maintain use
            MTRACE_ERR(g_hEBOOT_DEBUG, "Unknown stage %d, end the flow.", spStage);
            UpdateUIMsg("ERROR!! Unsupported write item.");
            iRet = META_NOT_SUPPORT;
            spNextStage = SP_END_STAGE;
        }

        UpdateProgress(fBeginProcess);
        spStage = spNextStage;
    }

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::WriteNvramLoop() end...");
    return (META_RESULT)iRet;
}

int SmartPhoneSN::GetSPModemInfo_Ex()
{
    int iRet = 0;
    char *pFuncName = NULL;
	unsigned int uiAPIRetry = 0;

    //Init parameters for without world phone feature
    m_sMdInfo.number_of_md = 0;
    m_sMdInfo.active_md_idx = 1;
    m_sMdInfo.number_of_mdSwImg = 1;
    m_sMdInfo.activeMdTypeIdx = 0;
    m_bDSDAProject = false;

	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		iRet = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Query_Info_r");
		if (iRet == META_SUCCESS)
		{	
			break;
		}
	}

    MODEM_QUERY_INFO_REQ pReq;
    MODEM_QUERY_INFO_CNF pCnf;
	if (iRet == META_SUCCESS)
	{
		for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
		{
    		memset(&pReq, 0, sizeof(MODEM_QUERY_INFO_REQ));
    		memset(&pCnf, 0, sizeof(MODEM_QUERY_INFO_CNF));

			iRet = SP_META_MODEM_Query_Info_r(m_hSPMetaHandle, 10000, &pReq, &pCnf);
			if (iRet == META_SUCCESS)
			{	
				break;
			}
		}
        if (iRet != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Query_Info_r(): fail, %s.", ResultToString_SP(iRet));
            return iRet;
        }
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Query_Info_r(): ok. md_num = %d, active_md_idx = %d.",
               pCnf.modem_number, pCnf.modem_id);
        m_sMdInfo.number_of_md = pCnf.modem_number;
        m_sMdInfo.active_md_idx = pCnf.modem_id;
        m_bWithoutMD = (m_sMdInfo.number_of_md == 0) ? true : false;
        m_bDualModem = (m_sMdInfo.number_of_md == 2) ? true : false;
    }

    if (m_sMdInfo.number_of_md <= 0)
        return META_SUCCESS;

	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		iRet = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Capability_r");
		if (iRet == META_SUCCESS)
		{	
			break;
		}
	}
    MODEM_CAPABILITY_LIST_REQ pCapabilitiesReq;
    MODEM_CAPABILITY_LIST_CNF pCapabilitiesCnf;
	if (iRet == META_SUCCESS)
	{
		for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
		{
    		memset(&pCapabilitiesReq, 0, sizeof(pCapabilitiesReq));
   		 	memset(&pCapabilitiesCnf, 0, sizeof(pCapabilitiesCnf));
			iRet = SP_META_MODEM_Capability_r(m_hSPMetaHandle, 10000, &pCapabilitiesReq, &pCapabilitiesCnf);
			if (iRet == META_SUCCESS)
			{	
				break;
			}
		}
        if (iRet != META_SUCCESS)
        {
            m_sMdInfo.multi_md_capability_support = 0;
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Capability_r(): Get modem capability from target Fail!!");

			m_tMetaReq_Ex.protocol = 2;
			m_tMetaReq_Ex.channel_type = 2;
            iRet = META_SUCCESS;
        }
        else
        {
            m_sMdInfo.multi_md_capability_support = 1;
            memcpy(&m_SpMdCapList, &pCapabilitiesCnf, sizeof(pCapabilitiesCnf));
            m_tMetaReq_Ex.protocol = m_SpMdCapList.modem_cap[m_sMdInfo.active_md_idx - 1].md_service;
            m_tMetaReq_Ex.channel_type =  m_SpMdCapList.modem_cap[m_sMdInfo.active_md_idx - 1].ch_type;

            if (m_sMdInfo.number_of_md >= 2)
            {
                m_bDSDAProject = true;
                m_iC2kProject = 0;
                int mdIndex = 0;

                for (int i = 0; i < MAX_MD_CHANNEL_NUM; i++)
                {
                    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Modem capability, protocol = %d, channel_type = %d!",
                           m_SpMdCapList.modem_cap[i].md_service, m_SpMdCapList.modem_cap[i].ch_type);

                    if (m_SpMdCapList.modem_cap[i].md_service > 0 )
                    {
                        m_iMDChannelIndex[mdIndex] = i;
                        mdIndex += 1;
                    }

                    if (m_sMdInfo.number_of_md == 2 && m_SpMdCapList.modem_cap[i].md_service == 3)
                    {
                        m_iC2kProject = 1;
                        m_bDSDAProject = false;
                    }
                }
            }
        }
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Capability_r(): Get modem capability, protocol = %d, channel_type = %d!",
               m_tMetaReq_Ex.protocol, m_tMetaReq_Ex.channel_type);
    }

	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		iRet = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Get_CurrentModemType_r");
		if (iRet == META_SUCCESS)
		{	
			break;
		}
	}
    MODEM_GET_CURRENTMODEMTYPE_REQ pCurMDTypeReq;
    MODEM_GET_CURENTMODEMTYPE_CNF pCurMDTypeCnf;

	if (iRet == META_SUCCESS)
	{
		for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
		{
    		memset(&pCurMDTypeReq, 0, sizeof(pCurMDTypeReq));
    		memset(&pCurMDTypeCnf, 0, sizeof(pCurMDTypeCnf));
			iRet = SP_META_MODEM_Get_CurrentModemType_r(m_hSPMetaHandle, 10000, &pCurMDTypeReq, &pCurMDTypeCnf);
			if (iRet == META_SUCCESS)
			{	
				break;
			}
		}
        if (iRet != META_SUCCESS)
        {
            iRet = META_SUCCESS;
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Get_CurrentModemType_r(): Get modem type from target Fail, mean that handset not support worldphone feature!!");
        }
        else
        {
            m_sMdInfo.current_mdtype = pCurMDTypeCnf.current_modem_type;
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Get_CurrentModemType_r(): Get MD Image info from target success, MdType = %d.", m_sMdInfo.current_mdtype);
        }
    }

	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		iRet = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Query_MDIMGType_r");
		if (iRet == META_SUCCESS)
		{	
			break;
		}
	}
    MODEM_QUERY_MDIMGTYPE_REQ pMDImgTypeReq;
    MODEM_QUERY_MDIMGTYPE_CNF pMDImgTypeCnf;

	if (iRet == META_SUCCESS)
	{
		for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
		{
    		memset(&pMDImgTypeReq, 0, sizeof(pMDImgTypeReq));
    		memset(&pMDImgTypeCnf, 0, sizeof(pMDImgTypeCnf));
			iRet = SP_META_MODEM_Query_MDIMGType_r(m_hSPMetaHandle, 10000, &pMDImgTypeReq, &pMDImgTypeCnf);
			if (iRet == META_SUCCESS)
			{	
				break;
			}
		}
        if (iRet != META_SUCCESS)
        {
            iRet = META_SUCCESS;
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Query_MDIMGType_r(): Get MD Image info from target fail, mean that handset not support worldphone feature!!");
        }
        else
        {
            memcpy(m_sMdInfo.mdimg_type, pMDImgTypeCnf.mdimg_type, 16);
            bool bAllZero = true;
            m_bWorldPhone = true;
            m_sMdInfo.number_of_mdSwImg = 0;
            //Get current active mdtype index
            for (int i = 0; i < 16; i++)
            {
                if (m_sMdInfo.mdimg_type[i] != 0 )
                {
                    m_sMdInfo.number_of_mdSwImg += 1;
                    bAllZero = false;
                }

                if (m_sMdInfo.current_mdtype != 0 && m_sMdInfo.mdimg_type[i] == m_sMdInfo.current_mdtype)
                {
                    m_sMdInfo.activeMdTypeIdx = i;
                }
            }

            //For before MT6577(include MT6577)old chip project
            //Old chip project call this api will return success, but mdimg_type array data all zero
            if (bAllZero)
            {
                m_sMdInfo.number_of_mdSwImg = 1;
                m_sMdInfo.activeMdTypeIdx = 0;
                m_bWorldPhone = false;
                MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Query_MDIMGType_r(): Get MD Image info from target success but all zero, mean that handset not support worldphone feature!!");
            }
            else
            {
                MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Query_MDIMGType_r(): Get MD Image info from target success, activeMdTypeIdx = %d, mean that handset support worldphone feature!!", m_sMdInfo.activeMdTypeIdx);
            }

            if (m_sMdInfo.number_of_md >= 2)
            {
                //extern modem have one SwImg
                m_sMdInfo.number_of_mdSwImg += (m_sMdInfo.number_of_md - 1);
            }
        }
    }

    //if(SPMETA_DLL:P_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Query_Download_Status_r ") == META_SUCCESS)
    if (m_bDSDAProject)
    {
        MODEM_QUERY_DOWNLOAD_STATUS_REQ pDLReq;
        MODEM_QUERY_DOWNLOAD_STATUS_CNF pDLCnf;
		for (uiAPIRetry = 0; uiAPIRetry < 10; uiAPIRetry++)
		{
        	memset(&pDLReq, 0, sizeof(MODEM_QUERY_DOWNLOAD_STATUS_REQ));
        	memset(&pDLCnf, 0, sizeof(MODEM_QUERY_DOWNLOAD_STATUS_CNF));

			iRet = SP_META_MODEM_Query_Download_Status_r (m_hSPMetaHandle, 80000, &pDLReq, &pDLCnf);
			if (iRet == META_SUCCESS)
			{	
				break;
			}
		}
        if (iRet != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_MODEM_Query_Download_Status_r(): Query extern modem download process fail, MetaResult = %s", ResultToString_SP(iRet));
            iRet = 0;
        }
    }

    return iRet;
}

//Fix connect with kernel comport probabilistic fail bug
int SmartPhoneSN::TryToOpenSPKernelComport(int KernelCom_Num)
{
    int ret_i = 1;
    DWORD dwError_win = 0u;
    char * sz_error = NULL;
    HANDLE Hcom;
    char tmp_com[30] = {0};
    int retryTime = 0;

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::TryToOpenSPKernelComport(): Try to open kernel comport until it ready, KernelCom_Num = %d...", KernelCom_Num);

    sz_error = new char[1024];
    memset(sz_error, 0, 1024);
    sprintf_s(tmp_com, "\\\\.\\COM%d", KernelCom_Num);

    //try to open kernel comport until it ready
    do
    {
        retryTime += 1;
        Hcom = CreateFile(tmp_com, GENERIC_WRITE | GENERIC_READ, 0, NULL, OPEN_EXISTING, 0, NULL);
        if (INVALID_HANDLE_VALUE != Hcom)
        {
            CloseHandle(Hcom);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::TryToOpenSPKernelComport(): success, retryTime = %d", retryTime);
            ret_i = 0;
            break;
        }
        dwError_win = GetLastError();
        if (sz_error != NULL)
        {
            ResultToString_Win(dwError_win, sz_error, 1024u);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::TryToOpenSPKernelComport(): fail(%u: %s), retryTime = %d",
                   dwError_win, sz_error, retryTime);
        }
        else
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::TryToOpenSPKernelComport(): fail(%u), retryTime = %d",
                   dwError_win, retryTime);

        Sleep(1000);
    } while (retryTime < 50);

    if (sz_error != NULL)
        delete [] sz_error;
    return ret_i;
}

int SmartPhoneSN::ConnectWithKernelPort_Ex()
{
    int iRet = 0;

    PortEnumHelper cPortHelper;
    SP_COM_PROPERTY_S * psCOMProperty = NULL;
    META_ConnectByUSB_Req spUSBConnReq;
    META_ConnectByUSB_Report spUSBConnReport;

    // kernel comport filter white list
    cPortHelper.SetFilter(SP_WHITE_LIST, g_sMetaComm.sPortFilter.strKernelFilter);
    psCOMProperty = cPortHelper.GetPorts(true);
    memset(&spUSBConnReq, 0, sizeof(spUSBConnReq));
    memset(&spUSBConnReport, 0, sizeof(spUSBConnReport));

    if (g_sMetaComm.bUsbEnable)
    {
        // phase out SP_META_GetDynamicUSBComPort_r API, cause it have probabilistic can`t fetch kernel comport issue
        if (!g_sMetaComm.bAlreadyInMeata)
        {
            // timeout unit: s, but m_tMetaReq_Ex.ms_connect_timeout unit is ms
            int iTimeout = m_tMetaReq_Ex.ms_connect_timeout / 1000;
            MTRACE(g_hEBOOT_DEBUG, "SP_BROM::SP_GetUSBCOMPortWithFilter(): enum kernel comport...");
			
			while(1)
			{
				iRet = SP_GetUSBCOMPortWithFilter(cPortHelper.GetFilter(), psCOMProperty, false, m_pMetaStopFlag, iTimeout);
				if (iRet != 0)
				{
					MTRACE_ERR(g_hEBOOT_DEBUG, "SP_BROM::SP_GetUSBCOMPortWithFilter(): enum kernel comport fail(%d).", iRet);
					iRet = 1;
					goto Err;
				}
				
				if(psCOMProperty->m_uNumber == 0) 
				{
					MTRACE(g_hEBOOT_DEBUG, "SP_BROM::SP_GetUSBCOMPortWithFilter(): kernel comport %u. retry get port!", psCOMProperty->m_uNumber);
					continue;
				}
				else 
					break;
			}
            MTRACE(g_hEBOOT_DEBUG, "SP_BROM::SP_GetUSBCOMPortWithFilter(): kernel comport %u.", psCOMProperty->m_uNumber);
        }
        else
        {
            int nPortNum = 1;
            MTRACE(g_hEBOOT_DEBUG, "SP_BROM::SP_GetCurrentCOMPortInfoWithFilter(): enum kernel comport...");
            for (unsigned int time = 0; time <= m_tMetaReq_Ex.ms_connect_timeout; time += 500)
            {
                iRet = SP_GetCurrentCOMPortInfoWithFilter(cPortHelper.GetFilter(), &GUID_PORT_CLASS_USB2SER, false, psCOMProperty, &nPortNum);
                if (iRet == 0 && nPortNum > 0)
                    break;

                MTRACE_ERR(g_hEBOOT_DEBUG, "SP_BROM::SP_GetCurrentCOMPortInfoWithFilter(): enum fail(%d)", iRet);
                Sleep(500);
            }
            if (nPortNum > 1)
            {
                MTRACE_ERR(g_hEBOOT_DEBUG, "SP_BROM::SP_GetCurrentCOMPortInfoWithFilter(): enum %d kernel comports, please ensure only 1 valid port.", nPortNum);
                iRet = 1;
                goto Err;
            }
            if (iRet != 0 || nPortNum <= 0)
            {
                MTRACE_ERR(g_hEBOOT_DEBUG, "SP_BROM::SP_GetCurrentCOMPortInfoWithFilter(): enum kernel comport fail.");
                iRet = 1;
                goto Err;
            }
            MTRACE(g_hEBOOT_DEBUG, "SP_BROM::SP_GetCurrentCOMPortInfoWithFilter(): kernel comport %u.", psCOMProperty->m_uNumber);
        }
        m_nKernelComport = psCOMProperty->m_uNumber;

        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::TryToOpenSPKernelComport(): Try to open kernel comport...");
        spUSBConnReq.com_port = m_nKernelComport;
        spUSBConnReq.ms_connect_timeout = m_tMetaReq_Ex.ms_connect_timeout;
        iRet = TryToOpenSPKernelComport(m_nKernelComport);
        if (iRet != 0)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::TryToOpenSPKernelComport(): Try to open kernel comport fail.");
            iRet = 2;
            goto Err;
        }
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::TryToOpenSPKernelComport(): Try to open kernel com port ok.");

        MTRACE(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaModeByUSB_r(): Enter AP meta mode by comport %u...", m_nKernelComport);
        iRet = SP_META_ConnectInMetaModeByUSB_r (m_hSPMetaHandle, &spUSBConnReq, m_pMetaStopFlag, &spUSBConnReport);
        if (iRet == META_SUCCESS)
        {
            MTRACE(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaModeByUSB_r(): ok.");
            m_eMetaMode = SP_AP_META;
            m_bTargetInMetaMode = true;
            m_bStopBeforeUSBInsert = false;
            iRet = 0;
        }
        else if (iRet == META_MAUI_DB_INCONSISTENT)
        {
            m_eMetaMode = SP_AP_META;
            m_bTargetInMetaMode = true;
            m_bStopBeforeUSBInsert = false;
            if (!g_sMetaComm.sDBFileOption.bDBInitAP)
            {
                MTRACE(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaModeByUSB_r(): ap db inconsistent, directly ignore.");
                iRet = 0;
            }
            else if (g_sMetaComm.IgnoreDBInconsistent)
            {
                MTRACE_WARN(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaModeByUSB_r(): ap db inconsistent, but ignore.");
                iRet = 0;
            }
            else
            {
                MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaModeByUSB_r(): fail, ap db inconsistent.");
                iRet = 3;
            }
        }
        else
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaModeByUSB_r(): Enter meta fail, %s", ResultToString_SP(iRet));
            m_eMetaMode = SP_NOTIN_META;
            m_bTargetInMetaMode = false;
            m_bStopBeforeUSBInsert = true;
            iRet = 3;
        }
    } // if(g_sMetaComm.bUsbEnable)
    else
    {
        m_nKernelComport = g_sMetaComm.iCOMPort;

        WM_META_ConnectInMETA_Req uartMETA_Req;
        WM_META_ConnectInMETA_Report uartMETA_Report;
        memset(&uartMETA_Req, 0 , sizeof(WM_META_ConnectInMETA_Req));
        memset(&uartMETA_Report, 0 , sizeof(WM_META_ConnectInMETA_Report));

        uartMETA_Req.com_port = m_nKernelComport;
        uartMETA_Req.baudrate[0] = META_BAUD921600;
        uartMETA_Req.baudrate[1] = META_BAUD_END;
        uartMETA_Req.flowctrl    = META_NO_FLOWCTRL;//META_SW_FLOWCTRL;
        uartMETA_Req.ms_connect_timeout = m_tMetaReq_Ex.ms_connect_timeout;

        MTRACE(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaMode_r(): Enter AP meta mode by comport %d...", m_nKernelComport);
        iRet = SP_META_ConnectInMetaMode_r(m_hSPMetaHandle, &uartMETA_Req, m_pMetaStopFlag, &uartMETA_Report);
        if (iRet == META_SUCCESS)
        {
            MTRACE(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaMode_r(): ok.");
            m_eMetaMode = SP_AP_META;
            m_bTargetInMetaMode = true;
            m_bStopBeforeUSBInsert = false;
            iRet = 0;
        }
        else if (iRet == META_MAUI_DB_INCONSISTENT)
        {
            m_eMetaMode = SP_AP_META;
            m_bTargetInMetaMode = true;
            m_bStopBeforeUSBInsert = false;
            if (!g_sMetaComm.sDBFileOption.bDBInitAP)
            {
                iRet = 0;
                MTRACE(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaMode_r(): ap db inconsistent, directly ignore.");
            }
            else if (g_sMetaComm.IgnoreDBInconsistent)
            {
                iRet = 0;
                MTRACE_WARN(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaMode_r(): ap db inconsistent, but ignore.");
            }
            else
            {
                MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaMode_r(): fail, ap db inconsistent.");
                iRet = 3;
            }
        }
        else
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_ConnectInMetaMode_r(): Enter meta fail, %s", ResultToString_SP(iRet));
            m_eMetaMode = SP_NOTIN_META;
            m_bTargetInMetaMode = false;
            m_bStopBeforeUSBInsert = true;
            iRet = 3;
        }
    }

Err:
    return iRet;
}

int SmartPhoneSN::ConnectWithPreloader()
{
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ConnectWithPreloader(): Start to get dynamic preloader comport...");

    PortEnumHelper cPortHelper;
    SP_COM_PROPERTY_S sCOMProperty = {0};

    int iRet = 0;
    unsigned int eType;   //0:BoorROMUSB,1:PreloaderUSB

    cPortHelper.SetFilter(SP_WHITE_LIST, g_sMetaComm.sPortFilter.strBromFilter, BootROMUSB);
    cPortHelper.SetFilter(SP_WHITE_LIST, g_sMetaComm.sPortFilter.strPreloaderFilter, PreloaderUSB, true);

    if (g_sMetaComm.bUsbEnable == false)
    {
        m_stModeArg.m_uPortNumber = g_sMetaComm.iCOMPort;
        eType = PreloaderUSB;
    }
    else
    {
        int iTimeout = m_stModeArg.m_uTimeout / 1000;  //timeout unit: s, but m_stModeArg.m_uTimeout unit is ms
        iRet = SP_GetIncrementCOMPortWithFilter(cPortHelper.GetFilter(), &sCOMProperty, NULL, true, m_pMetaStopFlag, iTimeout);
        if (0 == iRet)
        {
            switch (cPortHelper.GetFlag(sCOMProperty.m_iFilterIndex))
            {
            case PreloaderUSB:
                eType = PreloaderUSB;
                break;
            case BootROMUSB:
                eType = BootROMUSB;
                break;
            default:
                //MessageBox (NULL, "Filter index error!", "fail", MB_OK);
                return META_FAILED;
            }

            m_stModeArg.m_uPortNumber = sCOMProperty.m_uNumber;
        }
        else
        {
            if (iRet == SP_S_TIMEOUT)
            {
                MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ConnectWithPreloader(): Get dynamic preloader comport timeout...");
                PopupMsgBox("Error", MB_OK | MB_ICONERROR, "SmartPhoneSN::ConnectWithPreloader(): Get dynamic preloader comport timeout...");
            }

            return iRet;
        }
    }

    ::Sleep(10);
    if (BootROMUSB == eType)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ConnectWithPreloader(): Get preloader comport successfully, comport = %d", m_stModeArg.m_uPortNumber);
        iRet = SP_BootROM_BootMode(&m_stModeArg);
        if (iRet == 0)
        {
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_BootROM_BootMode(): Preloader boot meta mode success!!");
        }
        else
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_BootROM_BootMode(): Preloader boot meta mode Fail, Err = %d", iRet);
        }
    }
    else if (PreloaderUSB == eType)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ConnectWithPreloader(): Get preloader comport successfully, comport = %d", m_stModeArg.m_uPortNumber);
		iRet = SP_Preloader_BootMode(&m_stModeArg, &m_stSLaArg);	
        if (iRet == 0)
        {
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_Preloader_BootMode(): Preloader boot meta mode success!!");
        }
        else
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_Preloader_BootMode(): Preloader boot meta mode Fail, Err = %d", iRet);
        }
    }

    return iRet;
}

void SmartPhoneSN::ModemMetaHandle_DeInit()
{
    META_Deinit_r(&m_hMauiMetaHandle);
}

void SmartPhoneSN::APMetaHandle_DeInit()
{
    SP_META_Deinit_r(&m_hSPMetaHandle);
}

META_RESULT SmartPhoneSN::APMetaHandle_Init()
{
    META_RESULT spMetaResult = META_SUCCESS;

    m_hSPMetaHandle = m_hMauiMetaHandle;

    spMetaResult = SP_META_Init_r (m_hSPMetaHandle , NULL);
    if ( spMetaResult != META_SUCCESS)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_Init_r(): Init AP handle fail, MetaResult = %s", ResultToString_SP(spMetaResult));
        return spMetaResult;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_Init_r(): Init AP handle success");

    return META_SUCCESS;
}

META_MD_Query_Result_T __stdcall MdQueryHandler(void* MdQuery_CB_Arg)
{
    SP_MODEMInfo_s *sMdInfo = (SP_MODEMInfo_s*)MdQuery_CB_Arg;
    META_MD_Query_Result_T result;

    result.number_of_md = sMdInfo->number_of_md;
    result.active_md_idx = sMdInfo->active_md_idx - 1;
    result.number_of_mdSwImg = sMdInfo->number_of_mdSwImg;
    result.active_mdtype_idx = sMdInfo->activeMdTypeIdx;
    result.multi_talk = (result.active_md_idx != 0 || result.number_of_md >= 2) ? TRUE : FALSE;
    result.multi_frame_type = 1;

    //result.multi_mdtype = (sMdInfo->current_mdtype != 0 && result.number_of_mdSwImg >= 2)?TRUE:FALSE;
    result.multi_mdtype = (result.number_of_mdSwImg > result.number_of_md) ? true : false;//zishuo modify
    result.multi_md_capability_support = sMdInfo->multi_md_capability_support;
    result.reserved = 5;

    return result;
}

int __stdcall MdTypeSwitchHandler(META_MDTYPE_Switch_Param_T mdtype_switch_param, void* MdTypeSwitch_CB_Arg)
{
    return 1;
}

void SmartPhoneSN::MetaHandle_DeInit()
{
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::MetaHandle_DeInit() : DeInit meta handle start...");
    if (m_hMauiMetaHandle != INVALID_META_HANDLE)
    {
        ModemMetaHandle_DeInit();
        m_hMauiMetaHandle = INVALID_META_HANDLE;
    }

    if (m_hSPMetaHandle != INVALID_META_HANDLE)
    {
        APMetaHandle_DeInit();
        m_hSPMetaHandle = INVALID_META_HANDLE;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::MetaHandle_DeInit() : DeInit meta handle end...");
}

META_RESULT SmartPhoneSN::ModemMetaHandle_Init()
{
    META_RESULT meta_result = META_SUCCESS;
    meta_result = META_GetAvailableHandle_Ex(&m_hMauiMetaHandle, DATA_LIBRARY_MODE_EDB);
    if (meta_result != META_SUCCESS)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::META_GetAvailableHandle(): Get available modem meta handle fail, MetaResult = %s", ResultToString(meta_result));
        return meta_result;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_GetAvailableHandle(): Get available modem meta handle success!");

    /*
    if (g_sMetaComm.eTargetType == SMART_PHONE_DUALMODEM)
    {
    meta_result =  META_Init_Ex_r( m_hMauiMetaHandle, NULL, MdQueryHandler, NULL, NULL, NULL);
    }
    else
    {
    meta_result = META_Init_Ex_2_r( m_hMauiMetaHandle, NULL, MdQueryHandler, NULL, NULL, NULL, MdTypeSwitchHandler, NULL);
    }
    */
    meta_result = META_Init_Ex_2_r( m_hMauiMetaHandle, NULL, MdQueryHandler, (void*)&m_sMdInfo, NULL, NULL, MdTypeSwitchHandler, NULL);
    if (meta_result != META_SUCCESS)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::META_Init_Ex_2_r(): Init modem meta handle fail, MetaResult = %s", ResultToString(meta_result));
        return meta_result;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::META_Init_Ex_2_r(): Init modem meta handle success!");

    return meta_result;
}

META_RESULT SmartPhoneSN::MetaHandle_Init()
{
    META_RESULT result;

	if ( /*g_sMetaComm.eTargetType == SMART_PHONE 
		||*/ g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS 
		|| g_sMetaComm.eTargetType == DATA_CARD 
		)
	{
		m_hSPMetaHandle = 0;
		m_hMauiMetaHandle = 0;
	}
	else
	{
		if (m_hMauiMetaHandle == INVALID_META_HANDLE)
		{
			result = ModemMetaHandle_Init();
			if (result != META_SUCCESS)
				return result;

			result = APMetaHandle_Init();
			if (result != META_SUCCESS)
				return result;
		}
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::REQ_ReadFromAPNVRAM(AP_FT_NVRAM_READ_REQ *psNVRAM_ReadReq, AP_FT_NVRAM_READ_CNF *psNVRAM_ReadCnf)
{
    DWORD wait_result;
    short iNVRAM_OPID;
    META_RESULT MetaResult;

    m_hReadFromNVRAMEvent = ::CreateEvent( NULL, TRUE, FALSE, NULL );
    ::ResetEvent(m_hReadFromNVRAMEvent);

    MetaResult = SP_META_NVRAM_Read_r(m_hSPMetaHandle,
        psNVRAM_ReadReq,
        psNVRAM_ReadCnf,
        CNF_SPReadFromNVRAM,
        &iNVRAM_OPID,
        (void*)&m_hReadFromNVRAMEvent);

    wait_result = ::WaitForSingleObject(m_hReadFromNVRAMEvent, 15000);
    ::CloseHandle (m_hReadFromNVRAMEvent);
    if (wait_result == WAIT_TIMEOUT)
        return META_TIMEOUT;
    if (wait_result != WAIT_OBJECT_0)
        return META_FAILED;

    return MetaResult;
}

META_RESULT SmartPhoneSN::REQ_BackupNvram2BinRegion_Start()
{
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_BackupNvram2BinRegion_Start() : Backup nvram to BinRegion start...");
    UpdateUIMsg("Start backup nvram to BinRegion...");

    META_RESULT spMetaResult = META_SUCCESS;
    SetCleanBootFlag_REQ req;
    SetCleanBootFlag_CNF cnf;

    req.Notused = 0;

    spMetaResult =  SP_META_SetCleanBootFlag_r ( m_hSPMetaHandle, 15000, &req, &cnf );
	if (spMetaResult == META_SUCCESS)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_SetCleanBootFlag_r() : Backup nvram successfully!!");
    }
    else
    {
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_SetCleanBootFlag_r() : Backup nvram Fail!! MetaResult = %s",ResultToString_SP(spMetaResult));
    }

    return spMetaResult;
}

int SmartPhoneSN::LockOTP()
{
    int ret_i;

    UpdateUIMsg("Lock OTP partition start...");
    MTRACE(g_hEBOOT_DEBUG, "Lock OTP partition start...");
    ret_i = META_NVRAM_OTP_LockDown_r(m_hMauiMetaHandle, 30000);
    if (ret_i != META_SUCCESS)
    {
        UpdateUIMsg("Lock OTP partition fail.");
        MTRACE_ERR(g_hEBOOT_DEBUG, "Lock OTP partition fail(%d:%s).", ret_i, ResultToString(ret_i));
        return 1;
    }
    MTRACE(g_hEBOOT_DEBUG, "Lock OTP partition ok.");

    return 0;
}

META_RESULT SmartPhoneSN::ConductProdInfoData(unsigned char *pBuf, int nBufLen)
{
    typedef struct _sn_write_flag
    {
        unsigned char     total_pass: 1;
        unsigned char     barcode: 1;
        unsigned char     IMEI: 1;
        unsigned char     bt: 1;
        unsigned char     wifi: 1;
        unsigned char     MEID: 1;
        unsigned int      reserved: 10;
        //unsigned char       sn_write_flag[2];
    } sn_write_flag;

    PRODUCT_INFO * pstProInfo = (PRODUCT_INFO *)pBuf;
    sn_write_flag * pstSnFlag = (sn_write_flag *) & (pstProInfo->mtk_test_flag.sn_write_flag);
    memset(pstSnFlag, 0, sizeof(sn_write_flag));
	
	

    if (g_sMetaComm.sWriteOption.bWriteBarcode)
    {
        MTRACE(g_hEBOOT_DEBUG, "Prod_Info->Barcode = [%s]", m_sScanData.strBarcode);
        memcpy(&pstProInfo->barcode, m_sScanData.strBarcode, BARCODE_MAX_LENGTH);
        pstSnFlag->barcode = 1;
    }

    if (g_sMetaComm.sWriteOption.bWriteIMEI)
    {
        int idIMEI = 0, idByte = 0;
        unsigned char tmpCh;

        for (idIMEI = 0; idIMEI < g_sMetaComm.sIMEIOption.iImeiNums; idIMEI++)
        {
            MTRACE(g_hEBOOT_DEBUG, "Prod_Info->IMEI[%d] = [%s]", idIMEI, m_sScanData.strIMEI[idIMEI]);
            for (int idByte = 0; idByte < 7; idByte++)
            {
                // High 4Bit
                tmpCh = m_sScanData.strIMEI[idIMEI][idByte * 2 + 1] - '0';
                tmpCh = ((tmpCh << 4) & 0xf0u);

                // Low 4Bit
                tmpCh += m_sScanData.strIMEI[idIMEI][idByte * 2] - '0';
                pstProInfo->IMEI[idIMEI].imei[idByte] = tmpCh;
            }

            // Checksum Byte : 0xf-
            tmpCh = m_sScanData.strIMEI[idIMEI][14] - '0';
            tmpCh |= 0xf0u;
            pstProInfo->IMEI[idIMEI].imei[7] = tmpCh;
        }

        pstSnFlag->IMEI = 1;
    }

    if (g_sMetaComm.sWriteOption.bWriteBT)
    {
		if(g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
		{
	        Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
	        std::string writableMac = codes.getWritableMacAddr(codes.bt_mac);
	        if (writableMac.empty())
	        {
	            MTRACE(g_hEBOOT_DEBUG, "bt address wrong: [%s]", codes.bt_mac.c_str());
	            return META_FAILED;
	        }

	        const char * pBtAddr = writableMac.c_str();
	        unsigned char tmpCh;

	        MTRACE(g_hEBOOT_DEBUG, "Prod_Info->BtAddress = [%s]", writableMac.c_str());
	        for (int i = 0; i < 6; i++)
	        {
	            // High 4Bit
	            if ('0' <= pBtAddr[i * 2] && pBtAddr[i * 2] <= '9')
	            {
	                tmpCh = pBtAddr[i * 2] - '0';
	                tmpCh = ((tmpCh << 4) & 0xf0u);
	            }
	            else if ('A' <= pBtAddr[i * 2] && pBtAddr[i * 2] <= 'F')
	            {
	                tmpCh = pBtAddr[i * 2] - 'A' + 10;
	                tmpCh = ((tmpCh << 4) & 0xf0u);
	            }

	            // Low 4Bit
	            if ('0' <= pBtAddr[(i * 2 + 1)] && pBtAddr[(i * 2 + 1)] <= '9')
	            {
	                tmpCh += pBtAddr[(i * 2 + 1)] - '0';
	            }
	            else if ('A' <= pBtAddr[(i * 2 + 1)] && pBtAddr[(i * 2 + 1)] <= 'F')
	            {
	                tmpCh += pBtAddr[(i * 2 + 1)] - 'A' + 10;
	            }

	            pstProInfo->target_info.BTAddr[i] = tmpCh;
	        }

	        pstSnFlag->bt = 1;
	    }
		else
		{
			char * pBtAddr = m_sScanData.strBTAddr;
        	unsigned char tmpCh;

       		MTRACE(g_hEBOOT_DEBUG, "Prod_Info->BtAddress = [%s]", m_sScanData.strBTAddr);
			for (int i = 0; i < 6; i++)
			{
				// High 4Bit
				if ('0' <= pBtAddr[i * 2] && pBtAddr[i * 2] <= '9')
				{
					tmpCh = pBtAddr[i * 2] - '0';
					tmpCh = ((tmpCh << 4) & 0xf0u);
				}
				else if ('A' <= pBtAddr[i * 2] && pBtAddr[i * 2] <= 'F')
				{
					tmpCh = pBtAddr[i * 2] - 'A' + 10;
					tmpCh = ((tmpCh << 4) & 0xf0u);
				}

				// Low 4Bit
				if ('0' <= pBtAddr[(i * 2 + 1)] && pBtAddr[(i * 2 + 1)] <= '9')
				{
					tmpCh += pBtAddr[(i * 2 + 1)] - '0';
				}
				else if ('A' <= pBtAddr[(i * 2 + 1)] && pBtAddr[(i * 2 + 1)] <= 'F')
				{
					tmpCh += pBtAddr[(i * 2 + 1)] - 'A' + 10;
				}

				pstProInfo->target_info.BTAddr[i] = tmpCh;
			}
		}
	}
	
    if (g_sMetaComm.sWriteOption.bWriteWifi)
    {
		if(g_sMetaComm.smesconnect.mesconnectcheck != TRUE)
		{
	        Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
	        std::string writableMac = codes.getWritableMacAddr(codes.wifi_mac);
	        if (writableMac.empty())
	        {
	            MTRACE(g_hEBOOT_DEBUG, "wifi address wrong: [%s]", codes.wifi_mac.c_str());
	            return META_FAILED;
	        }

	        const char * pWiFiAddr = writableMac.c_str();
	        unsigned char tmpCh;

	        MTRACE(g_hEBOOT_DEBUG, "Prod_Info->WiFiAddress = [%s]", writableMac.c_str());
	        for (int i = 0; i < 6; i++)
	        {
	            // High 4Bit
	            if ('0' <= pWiFiAddr[i * 2] && pWiFiAddr[i * 2] <= '9')
	            {
	                tmpCh = pWiFiAddr[i * 2] - '0';
	                tmpCh = ((tmpCh << 4) & 0xf0u);
	            }
	            else if ('A' <= pWiFiAddr[i * 2] && pWiFiAddr[i * 2] <= 'F')
	            {
	                tmpCh = pWiFiAddr[i * 2] - 'A' + 10;
	                tmpCh = ((tmpCh << 4) & 0xf0u);
	            }

	            // Low 4Bit
	            if ('0' <= pWiFiAddr[(i * 2 + 1)] && pWiFiAddr[(i * 2 + 1)] <= '9')
	            {
	                tmpCh += pWiFiAddr[(i * 2 + 1)] - '0';
	            }
	            else if ('A' <= pWiFiAddr[(i * 2 + 1)] && pWiFiAddr[(i * 2 + 1)] <= 'F')
	            {
	                tmpCh += pWiFiAddr[(i * 2 + 1)] - 'A' + 10;
	            }

	            pstProInfo->target_info.WifiAddr[i] = tmpCh;
	        }

	        pstSnFlag->wifi = 1;
	    }
		else
		{
			char * pWiFiAddr = m_sScanData.strWifiAddr;
        	unsigned char tmpCh;

       	 	MTRACE(g_hEBOOT_DEBUG, "Prod_Info->WiFiAddress = [%s]", m_sScanData.strWifiAddr);
			for (int i = 0; i < 6; i++)
			{
				// High 4Bit
				if ('0' <= pWiFiAddr[i * 2] && pWiFiAddr[i * 2] <= '9')
				{
					tmpCh = pWiFiAddr[i * 2] - '0';
					tmpCh = ((tmpCh << 4) & 0xf0u);
				}
				else if ('A' <= pWiFiAddr[i * 2] && pWiFiAddr[i * 2] <= 'F')
				{
					tmpCh = pWiFiAddr[i * 2] - 'A' + 10;
					tmpCh = ((tmpCh << 4) & 0xf0u);
				}

				// Low 4Bit
				if ('0' <= pWiFiAddr[(i * 2 + 1)] && pWiFiAddr[(i * 2 + 1)] <= '9')
				{
					tmpCh += pWiFiAddr[(i * 2 + 1)] - '0';
				}
				else if ('A' <= pWiFiAddr[(i * 2 + 1)] && pWiFiAddr[(i * 2 + 1)] <= 'F')
				{
					tmpCh += pWiFiAddr[(i * 2 + 1)] - 'A' + 10;
				}

				pstProInfo->target_info.WifiAddr[i] = tmpCh;
			}
		}
	}

    if (g_sMetaComm.sWriteOption.bWriteSerialNo)
    {
		if(g_sMetaComm.smesconnect.mesconnectcheck == TRUE)
		{
        	MTRACE(g_hEBOOT_DEBUG, "Prod_Info->ADBSeriaNo = [%s]", m_sScanData.strSerialNo);
        	strcpy_s((char *)pstProInfo->target_info.ADBSeriaNo, SERIAL_NO_BUF_LEN, m_sScanData.strSerialNo);

		}
		else
		{
	        Codes codes = CodeDatabase::inst()->getCode(m_sScanData, g_sMetaComm.eScanCodeType);
	        if (!codes.isSNValid(codes.sn))
	        {
	            MTRACE(g_hEBOOT_DEBUG, "sn wrong: [%s]", codes.sn.c_str());
	            return META_FAILED;
	        }

	        MTRACE(g_hEBOOT_DEBUG, "Prod_Info->ADBSeriaNo = [%s]", m_sScanData.strSerialNo);
	        strcpy_s((char *)pstProInfo->target_info.ADBSeriaNo, SERIAL_NO_BUF_LEN, m_sScanData.strSerialNo);
		}
        pstProInfo->target_info.ADBSeriaNo[SERIAL_NO_LEN] = '\0';
    }

    if (g_sMetaComm.sWriteOption.bWriteMeid)
        pstSnFlag->MEID = 1;

    pstSnFlag->total_pass = 1;
    MTRACE(g_hEBOOT_DEBUG, "Prod_Info->sn_flag = [0x%02x%02x] Bitmap: (15~0) Reserve|MEID|Wifi|Bt|IMEI|Barcode|Pass",
           pstProInfo->mtk_test_flag.sn_write_flag.sn_write_flag[1],
           pstProInfo->mtk_test_flag.sn_write_flag.sn_write_flag[0]);

    if (g_sMetaComm.bClearMetaBootFlag)
    {
        MTRACE(g_hEBOOT_DEBUG, "Clear MetaBootFlag: mode %u, type %u, id %u",
			pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.boot_mode,
               pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.com_type,
               pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.com_id);
        pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.boot_mode = 0;
        pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.com_type = 0;
        pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.com_id = 0;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::REQ_WriteAP_PRODINFO_Start()
{
    META_RESULT meta_result = META_SUCCESS;
    m_bWriteNvram = true;
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write Barcode/Imei/Bt/Wifi/Serial.No to prod_info start...");

    int iIMEINums = g_sMetaComm.sIMEIOption.iImeiNums;
    int iMetaTimeout = 5000;
    int iWriteBufSize = 0;
    char *pLID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    char *pFuncName = NULL;
    unsigned char *pWriteData = NULL;

    AP_FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;

    // Check
    if (g_sMetaComm.sWriteOption.bWriteBarcode)
    {
        if (m_sScanData.strBarcode == NULL || strlen(m_sScanData.strBarcode) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (g_sMetaComm.sWriteOption.bWriteIMEI)
    {
        for (int i = 0; i < g_sMetaComm.sIMEIOption.iImeiNums; i++)
        {
            if (m_sScanData.strIMEI[i] == NULL || strlen(m_sScanData.strIMEI[i]) == 0)
            {
                MTRACE(g_hEBOOT_DEBUG, "i = %d", i);
                return META_INVALID_ARGUMENTS;
            }
        }
    }
    if (g_sMetaComm.sWriteOption.bWriteBT)
    {
        if (m_sScanData.strBTAddr == NULL || strlen(m_sScanData.strBTAddr) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (g_sMetaComm.sWriteOption.bWriteWifi)
    {
        if (m_sScanData.strWifiAddr == NULL || strlen(m_sScanData.strWifiAddr) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (g_sMetaComm.sWriteOption.bWriteSerialNo)
    {
        if (m_sScanData.strSerialNo == NULL || strlen(m_sScanData.strSerialNo) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }

    memset(&sNVRAM_WriteReq, 0, sizeof(AP_FT_NVRAM_WRITE_REQ));
    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = SP_META_NVRAM_GetRecLen(pLID, &iWriteBufSize);
    if (  META_SUCCESS != meta_result)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", ResultToString_SP(meta_result));
        return meta_result;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", iWriteBufSize);
    if ( NULL != sNVRAM_ReadCnf.buf )
    {
        free(sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    if (NULL != pWriteData)
    {
        free(pWriteData);
        pWriteData = NULL;
    }

    sNVRAM_ReadReq.LID = pLID;
    sNVRAM_ReadReq.RID = 1;
    sNVRAM_ReadCnf.len = iWriteBufSize;
    sNVRAM_ReadCnf.buf = (unsigned char*) malloc(iWriteBufSize * sizeof(unsigned char));
    pWriteData = (unsigned char*) malloc(iWriteBufSize * sizeof(unsigned char));
    if (NULL == sNVRAM_ReadCnf.buf || NULL == pWriteData)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "Malloc heap memory cause fail!");
        return  META_FAILED;
    }

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromAPNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto Err;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ConductProdInfoData(): Conduct Prod_Info nvram data start...");
    meta_result = ConductProdInfoData(sNVRAM_ReadCnf.buf, iWriteBufSize);
    if (meta_result != META_SUCCESS)
    {
		if (g_sMetaComm.smesconnect.mesconnectcheck == TRUE)
		{
			if(g_sMetaComm.smesconnect.strMes[0] == 'L')
			{
				MesServerProxy proxy;
				char *error = (char *)malloc(sizeof(char)*1024);

				bool ret = proxy.SetAllInfo(g_sMetaComm.smesconnect.Sn, "0", error);
				if(!ret)
				{
					//MessageBox(NULL, error, "fail", MB_OK);
					UpdateUIMsg("%s", error);
				}
			}
		}
        pFuncName = "SmartPhoneSN::ConductProdInfoData()";
        goto Err;
    }
    else
    {
		if (g_sMetaComm.smesconnect.mesconnectcheck == TRUE)
		{
			if(g_sMetaComm.smesconnect.strMes[0] == 'L')
			{
				MesServerProxy proxy;
				char *error = (char *)malloc(sizeof(char)*1024);

				bool ret = proxy.SetAllInfo(g_sMetaComm.smesconnect.Sn, "1", error);
				if(!ret)
				{
					//MessageBox(NULL, error, "fail", MB_OK);
					UpdateUIMsg("%s", error);
				}
			}
		}
        memcpy(pWriteData, sNVRAM_ReadCnf.buf, iWriteBufSize);
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ConductProdInfoData(): Conduct Prod_Info nvram data successfully!!");
    }

    sNVRAM_WriteReq.LID = pLID;
    sNVRAM_WriteReq.RID = 1;
    sNVRAM_WriteReq.len = iWriteBufSize;
    sNVRAM_WriteReq.buf = pWriteData;
    m_sNVRAM_OPID = 1;

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Start to write nvram data...");
    meta_result =  REQ_WriteToAPNVRAM(sNVRAM_WriteReq);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_WriteToAPNVRAM()";
        goto Err;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Write nvram data successfully!");

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Read nvram data for check start...");
    memset(sNVRAM_ReadCnf.buf, 0 , sNVRAM_ReadCnf.len);
    meta_result = REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto Err;
    }
    else
    {
        if (memcmp(sNVRAM_ReadCnf.buf, pWriteData, sNVRAM_ReadCnf.len) != 0)
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN: Check prod_info data FAIL!!");
            meta_result = META_FAILED;
        }
        else
        {
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Check prod_info data PASS!!");
        }
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Read nvram data for check end...");

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    if (pWriteData != NULL)
    {
        free (pWriteData);
        pWriteData = NULL;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write Barcode/Imei/Bt/Wifi/Serial.No to prod_info end...");
    return meta_result;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    if (pWriteData != NULL)
    {
        free (pWriteData);
        pWriteData = NULL;
    }

    MTRACE_ERR (g_hEBOOT_DEBUG, "%s: fail! MetaResult = %s", pFuncName, ResultToString_SP(meta_result));
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write Barcode/Imei/Bt/Wifi/Serial.No to prod_info end...");
    return meta_result;
}

int SmartPhoneSN::LoadHDCPBinFile(const char *strHdcpBinPath)
{
    int iRet = 0;
    int file_size = 0;
    HANDLE hFile = INVALID_HANDLE_VALUE;
    FILE *fp = NULL;

    EN_HDCP_RET retHdcp = R_OK;
    LPWIN32_FIND_DATA file_data;

    char tempfile[1024] = {0};
    unsigned char bufNoEncrypt[HDCP_KEY_ARRAY_NUMBER_FILE] = {0};
    unsigned char bufEncrypt[HDCP_KEY_ARRAY_NUMBER] = {0};

    file_data = (WIN32_FIND_DATA*)malloc(sizeof(WIN32_FIND_DATA));
    sprintf_s(tempfile, "%s\\*.bin", strHdcpBinPath);
    hFile = FindFirstFile(tempfile, file_data);
    if (hFile == INVALID_HANDLE_VALUE)
    {
        UpdateUIMsg("FAIL: Can`t found Hdcp Key File!!");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadHDCPBinFile(): Can`t found Hdcp Key File!!");
        iRet = -1;
        goto End;
    }

    sprintf_s(m_strHdcpFileFullName, "%s\\%s", strHdcpBinPath, file_data->cFileName);
    if (fopen_s(&fp, m_strHdcpFileFullName, "rb") != 0 || fp == NULL)
    {
        UpdateUIMsg("FAIL: Can`t Open Hdcp Key File!!");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadHDCPBinFile(): Can`t Open Hdcp Key File!!");
        iRet = -1;
        goto End;
    }
    fseek(fp, 0, SEEK_SET);
    fread(bufNoEncrypt, sizeof(char), 308, fp);
    fseek(fp, 0, SEEK_END);
    file_size = ftell(fp);

    /*
    for( i = 0; i < HDCP_KEY_ARRAY_NUMBER_FILE; i++)
    {
    sprintf(strLog,"HDCP: HdcpProcess input bufNoEncrypt[%d]: 0x%2.2X.\n", i, bufNoEncrypt[i]&0xFF);
    }
    */

    retHdcp = HdcpProcess(bufNoEncrypt, bufEncrypt);

End:
    fclose(fp);
    FindClose( hFile );
    memcpy((char*)&m_sHDCPNvramStruct, bufEncrypt, sizeof(bufEncrypt));
    free (file_data);
    return iRet;
}

META_RESULT SmartPhoneSN::REQ_WriteHdcpBinToAPNvram_Start(const char *strHdcpBinPath)
{
    if (!strHdcpBinPath || !strlen(strHdcpBinPath))
    {
        return META_INVALID_ARGUMENTS;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteHdcpBinToAPNvram_Start(): Write Hdcp Bin start...");
    int i = 0;
    int iWriteBufSize = 0;
    int iReadBufSize = 0;
    char *pLID = "AP_CFG_RDCL_FILE_HDCP_KEY_LID";
    char *pFuncName = NULL;
    char* m_pcHDCPBuf = NULL;

    META_RESULT MetaResult = META_SUCCESS;
    AP_FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    memset(&sNVRAM_WriteReq, 0, sizeof(AP_FT_NVRAM_WRITE_REQ));
    memset(&m_sHDCPNvramStruct, 0, sizeof(m_sHDCPNvramStruct));
    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Start to get nvram struct size via LID = \"%s\"...", pLID);

    MetaResult = SP_META_NVRAM_GetRecLen ( pLID, &iWriteBufSize );
    if ( MetaResult != META_SUCCESS )
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", ResultToString_SP(MetaResult));
        return MetaResult;
    }
    else
    {
        m_pcHDCPBuf = (char*)malloc(iWriteBufSize * sizeof(char));
        if (!m_pcHDCPBuf)
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "Malloc heap memory cause fail!");
            return META_FAILED;
        }
    }


    //load hdcp file
    if (LoadHDCPBinFile(strHdcpBinPath) == -1)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteHdcpBinToAPNvram_Start(): Write Hdcp Bin end...");
        goto End;
    }

    MetaResult = SP_META_NVRAM_Compose_HDCP(&m_sHDCPNvramStruct, m_pcHDCPBuf, iWriteBufSize);
    if (MetaResult != META_SUCCESS)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_Compose_HDCP(): Compose hdcp data fail, MetaResult = %s", ResultToString_SP(MetaResult));
        goto End;
    }

    sNVRAM_WriteReq.LID = pLID;
    sNVRAM_WriteReq.RID = 1;
    sNVRAM_WriteReq.len = iWriteBufSize;
    sNVRAM_WriteReq.buf = (unsigned char *)m_pcHDCPBuf;

    MetaResult = REQ_WriteToAPNVRAM(sNVRAM_WriteReq);
    if (MetaResult != META_SUCCESS )
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Write hdcp key to nvram data Fail, MetaResult = %s", ResultToString_SP(MetaResult));
        goto End;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Write hdcp key to nvram data successfully!");

    //read hdcp 12.11 add by mtk71596
    iReadBufSize = iWriteBufSize;
    sNVRAM_ReadReq.LID = pLID;
    sNVRAM_ReadReq.RID = 1;
    sNVRAM_ReadCnf.len = iReadBufSize;
    sNVRAM_ReadCnf.buf = (unsigned char *)m_pcHDCPBuf;
    if (NULL == sNVRAM_ReadCnf.buf)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "Malloc heap memory cause fail!");
        goto End;
    }

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromAPNVRAM(): Start to read nvram data...");
    MetaResult =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (MetaResult != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto End;
    }
    MetaResult = SP_META_NVRAM_Decompose_HDCP(&m_sReadBackHdcp, m_pcHDCPBuf, iReadBufSize);

    for (i = 0; i < 287; i++)
    {
        if (m_sHDCPNvramStruct.HdcpKeyArray[i] != m_sReadBackHdcp.HdcpKeyArray[i])
        {
            MTRACE (g_hEBOOT_DEBUG, "Fail:The Key Read back is not the same as the write \n");
            goto End;
        }
    }

    remove(m_strHdcpFileFullName);
    MTRACE (g_hEBOOT_DEBUG, "OK: Write HDCP Key File\n");

End:
    if (m_pcHDCPBuf != NULL)
    {
        free (m_pcHDCPBuf);
        m_pcHDCPBuf = NULL;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteHdcpBinToAPNvram_Start(): Write Hdcp Bin end...");
    return MetaResult;
}

int SmartPhoneSN::LoadDRMKeyFile(char *szDrmKeyFile)
{
    HANDLE hFileEnum = INVALID_HANDLE_VALUE;
    WIN32_FIND_DATA stFileData = {0};
    BOOL bFindContinue = TRUE;

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadDRMKeyFile(): enum drmkey file start...");

    if (g_sMetaComm.sLoadFile.strDrmKeyPath == '\0')
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadDRMKeyFile(): invalide drmkey folder.");
        return -1;
    }

    ::PathCombine(szDrmKeyFile, g_sMetaComm.sLoadFile.strDrmKeyPath, "*");
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadDRMKeyFile(): pattern %s.", szDrmKeyFile);

    hFileEnum = ::FindFirstFile(szDrmKeyFile, &stFileData);
    bFindContinue = (hFileEnum != INVALID_HANDLE_VALUE);
    for (; bFindContinue == TRUE; bFindContinue = ::FindNextFile(hFileEnum, &stFileData))
    {
        if ((stFileData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) != 0)
            continue;

        ::PathCombine(szDrmKeyFile, g_sMetaComm.sLoadFile.strDrmKeyPath, stFileData.cFileName);
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadDRMKeyFile(): enum file %s.", szDrmKeyFile);

        // only get the first one
        break;
    }
    ::FindClose(hFileEnum);
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadDRMKeyFile(): enum drmkey file end.");

    return (bFindContinue == TRUE) ? 0 : -2;
}

int SmartPhoneSN::RecycleDRMKeyFile(const char *szDrmKeyFile)
{
    char szRecylceFolder[MAX_PATH] = {0};

    ::PathCombine(szRecylceFolder, g_sMetaComm.sLoadFile.strDrmKeyPath, "Recycle\\");
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RecycleDRMKeyFile(): recycle folder %s.", szRecylceFolder);
    if (::PathFileExists(szRecylceFolder) == FALSE)
    {
        MTRACE_WARN(g_hEBOOT_DEBUG, "SmartPhoneSN::RecycleDRMKeyFile(): recycle folder not exist, don't recycle key file.");
        return 0;
        // if (::CreateDirectory(szRecylceFolder, NULL) == FALSE)
        // {
        //     DWORD dwErrCode = ::GetLastError();
        //     ResultToString_Win(dwErrCode, szRecylceFolder, MAX_PATH);
        //     MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::RecycleDRMKeyFile(): create recycle folder fail(%s).", szRecylceFolder);
        //     return -1;
        // }
        // MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RecycleDRMKeyFile(): create recycle folder ok.");
    }

    ::PathAppend(szRecylceFolder, ::PathFindFileName(szDrmKeyFile));
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadDRMKeyFile(): new file path %s.", szRecylceFolder);
    if (::MoveFile(szDrmKeyFile, szRecylceFolder) == FALSE)
    {
        DWORD dwErrCode = ::GetLastError();
        ResultToString_Win(dwErrCode, szRecylceFolder, MAX_PATH);
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::RecycleDRMKeyFile(): move keyfile to recycle folder fail(%s).", szRecylceFolder);
        return -2;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RecycleDRMKeyFile(): move keyfile to recycle folder ok.");

    return 0;
}

META_RESULT SmartPhoneSN::REQ_WriteDRMKeyMCID_Start(const char *strDrmKeyMCIDData)
{
    if (!strDrmKeyMCIDData || strlen(strDrmKeyMCIDData) != DRMKEY_MCID_LENGTH)
    {
        return META_INVALID_ARGUMENTS;
    }

    META_RESULT MetaResult = META_SUCCESS;
    DRMKEY_WRITE_MCID_REQ pWriteReq;
    DRMKEY_WRITE_MCID_CNF pWriteCnf;
    DRMKEY_READ_MCID_REQ pReadReq;
    DRMKEY_READ_MCID_CNF pReadCnf;

    memset(&pWriteReq, 0, sizeof(DRMKEY_WRITE_MCID_REQ));
    memset(&pWriteCnf, 0, sizeof(DRMKEY_WRITE_MCID_CNF));
    memset(&pReadReq, 0, sizeof(DRMKEY_READ_MCID_REQ));
    memset(&pReadCnf, 0, sizeof(DRMKEY_READ_MCID_CNF));

    pWriteReq.keylength = DRMKEY_MCID_LENGTH;
    memcpy(pWriteReq.content, strDrmKeyMCIDData, DRMKEY_MCID_LENGTH);
    pWriteReq.content[DRMKEY_MCID_LENGTH] = '\0';
    MTRACE(g_hEBOOT_DEBUG, "DRMKeyMCID = [%s]", pWriteReq.content);

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Write_MCID_r(): Write DRMKeyMCID = [%s] to target start...", pWriteReq.content);
    MetaResult = SP_META_DRMKey_Write_MCID_r(m_hSPMetaHandle, 5000, &pWriteReq, &pWriteCnf);
    if (MetaResult != META_SUCCESS)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Write_MCID_r(): Write DRMKeyMCID Fail, MetaResult = %s", ResultToString_SP(MetaResult));
        return MetaResult;
    }
    else
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Write_MCID_r(): Write DRMKeyMCID successfully!!");

        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Read_MCID_r(): Read DRMKeyMCID from target for check start...");
        MetaResult = SP_META_DRMKey_Read_MCID_r(m_hSPMetaHandle, 5000, &pReadReq, &pReadCnf);
        if (MetaResult != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Read_MCID_r(): Read DRMKeyMCID Fail, MetaResult = %s", ResultToString_SP(MetaResult));
            return MetaResult;
        }
        else
        {
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Write_DRMKeyMCID[%s]", pWriteReq.content);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Read_DRMKeyMCID[%s]", pReadCnf.content);

            if (memcmp(pReadCnf.content, pWriteReq.content, DRMKEY_MCID_LENGTH) != 0)
            {
                MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN: Check DRMKeyMCID fail!! ");
                return META_FAILED;
            }
            else
            {
                MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Check DRMKeyMCID Pass!!");
            }
        }
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::REQ_ReadDRMKeyMCID_Start(char *strDrmKeyMCIDData)
{
    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::REQ_InstallDRMKey_Start()
{
    int iRet = 0;

    char szKeyFile[MAX_PATH] = "";
    META_RESULT MetaResult = META_SUCCESS;
    DRMKEY_INSTALL_QUERY_REQ Req;
    DRMKEY_INSTALL_SET_CNF pCnf;
    DRMKEY_INSTALL_QUERY_CNF QueCnf;

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallDRMKey_Start(): start...");

    memset(&Req, 0, sizeof(DRMKEY_INSTALL_QUERY_REQ));
    memset(&QueCnf, 0, sizeof(DRMKEY_INSTALL_QUERY_CNF));
    memset(&pCnf, 0, sizeof(DRMKEY_INSTALL_SET_CNF));

    iRet = LoadDRMKeyFile(szKeyFile);
    if (iRet != 0)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadDRMKeyFile(): load drmkey file fail.");
        return META_FAILED;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadDRMKeyFile(): load drmkey file %s.", szKeyFile);

    MetaResult =  SP_META_DRMKey_Install_Set_r(m_hSPMetaHandle, 10000, szKeyFile, &pCnf);
    if (MetaResult != META_SUCCESS || pCnf.result != 0)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Install_Set_r(): drmkey install fail, result %u, %s.",
                   pCnf.result, ResultToString_SP(MetaResult));
        return MetaResult;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Install_Set_r(): drmkey install ok.");

    MetaResult = SP_META_DRMKey_Install_Query_r(m_hSPMetaHandle, 5000, &Req, &QueCnf);
    if (MetaResult != META_SUCCESS)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Install_Query_r(): fail %s.", ResultToString_SP(MetaResult));
        return MetaResult;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_DRMKey_Install_Query_r(): keycount %d.", QueCnf.keycount);
    if (QueCnf.keycount == 0)
        return META_FAILED;

    iRet = RecycleDRMKeyFile(szKeyFile);
    if (iRet != 0)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RecycleDRMKeyFile(): recycle drmkey fail.");
        return META_FAILED;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RecycleDRMKeyFile(): recycle drmkey ok.");

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallDRMKey_Start(): end.");
    return MetaResult;
}

META_RESULT SmartPhoneSN::REQ_InstallHdcpData_Start(const char *strHdcpDataFilePath, const char *strHdcpCekFilePath)
{
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallHdcpData_Start(): Install Hdcp data start...");
    META_RESULT MetaResult = META_SUCCESS;
    HDCP_INSTALL_CNF Cnf;
    memset(&Cnf, 0, sizeof(HDCP_INSTALL_CNF));

    if (!strlen(strHdcpDataFilePath) || !strlen(strHdcpCekFilePath))
    {
        UpdateUIMsg("Error: Invalid HDCP file path");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallHdcpData_Start(): Invalid HDCP file path");

        MetaResult = META_INVALID_ARGUMENTS;
        goto End;
    }
    Sleep(2000);
    MetaResult  =  SP_META_HDCP_Install_r(m_hSPMetaHandle, 5000, strHdcpDataFilePath, strHdcpCekFilePath, &Cnf);
    if ((MetaResult == META_SUCCESS) &&  (Cnf.install_result == 0))
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallHdcpData_Start(): Install HDCP data successfully!!");
    }
    else
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallHdcpData_Start(): Install HDCP data Fail, MetaResult = %s", ResultToString_SP(MetaResult));
    }
    Sleep(2000);
End:
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallHdcpData_Start(): Install Hdcp data end...");
    return MetaResult;
}

META_RESULT SmartPhoneSN::REQ_InstallAttestationKey_Start(const char * strKeyFile)
{
    META_RESULT MetaResult = META_SUCCESS;
    ATTESTATIONKEY_INSTALL_SET_CNF Cnf;
    memset(&Cnf, 0, sizeof(ATTESTATIONKEY_INSTALL_SET_CNF));

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallAttestationKey_Start(): Install attestation key start...");
    if (strKeyFile == NULL || strKeyFile[0] == '\0')
    {
        UpdateUIMsg("Error: Invalid attestation key path.");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallAttestationKey_Start(): Invalid attestation key path");

        MetaResult = META_INVALID_ARGUMENTS;
        goto End;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallAttestationKey_Start(): key file \"%s\".", strKeyFile);

    MetaResult = SP_META_AttestationKey_Install_Set_r(m_hSPMetaHandle, 15000, strKeyFile, &Cnf);
    if (MetaResult != META_SUCCESS || Cnf.result != 0)
    {
        UpdateUIMsg("ERROR!! Install attestation key fail, %s", ResultToString_SP(MetaResult));
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallAttestationKey_Start(): Install attestation key fail, MetaResult = %s", ResultToString_SP(MetaResult));
    }

End:
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_InstallAttestationKey_Start(): Install attestation key end.");
    return MetaResult;
}

META_RESULT SmartPhoneSN::REQ_BackupNvram2PC_Start(char* NumFile)
{
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_BackupNvram2PC_Start() : Backup nvram to PC start....");
    UpdateUIMsg("Start backup nvram to PC...");
    META_RESULT spMetaResult = META_SUCCESS;
    char FolderPath[MAX_PATH];
    char FileFullPath[MAX_PATH];

    char MapFile[MAX_PATH];
    char DatFile[MAX_PATH];

    memset(MapFile, 0, sizeof(MapFile));
    memset(DatFile, 0, sizeof(DatFile));

    GetModuleFileName(NULL, FolderPath, MAX_PATH);
    (_tcsrchr(FolderPath, _T('\\')))[1] = 0;
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_BackupNvram2PC_Start() : GetModuleFileName folder path = %s ....", FolderPath);

    sprintf_s(FileFullPath, "%s\\%s", FolderPath, "BackupNvData");

    if (!PathFileExists(FileFullPath))
    {
        if (!CreateDirectory(FileFullPath, NULL))
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_BackupNvram2PC_Start() : Can`t create  backup nvram files folder path = %s ....", FileFullPath);
            return META_FAILED;
        }
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_BackupNvram2PC_Start() : The backup nvram files folder path = %s ....", FileFullPath);

    char temp1[10] = ".map";
    char temp2[10] = ".dat";


    sprintf_s(MapFile, "%s\\%s%s", FileFullPath, NumFile, temp1);
    MTRACE(g_hEBOOT_DEBUG, "debug!!!SmartPhoneSN::REQ_BackupNvram2PC_Start() : MapFile files folder path = %s ....", MapFile);


    sprintf_s(DatFile, "%s\\%s%s", FileFullPath, NumFile, temp2);
    MTRACE(g_hEBOOT_DEBUG, "debug!!!SmartPhoneSN::REQ_BackupNvram2PC_Start() : DatFile files folder path = %s ....", DatFile);
    spMetaResult = SP_META_Nvram_Backup_r( m_hSPMetaHandle, 15000, MapFile, DatFile);
    if (spMetaResult == META_SUCCESS)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_Nvram_Backup_r() : Backup nvram to PC successfully!!");
    }
    else
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_Nvram_Backup_r() : Backup nvram to PC Fail!! MetaResult = %s", ResultToString_SP(spMetaResult));
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_BackupNvram2PC_Start() : Backup nvram to PC end...");

    return spMetaResult;
}

META_RESULT SmartPhoneSN::REQ_WriteToAPNVRAM(AP_FT_NVRAM_WRITE_REQ sNVRAM_WriteReq)
{
    META_RESULT MetaResult;
    DWORD wait_result;
    short iNVRAM_OPID;

    m_hWriteToNVRAMEvent = CreateEvent(NULL, true, false, NULL);
    ResetEvent(m_hWriteToNVRAMEvent);

    MetaResult = SP_META_NVRAM_Write_r(m_hSPMetaHandle,
                 &sNVRAM_WriteReq,
                 CNF_SPWriteToNVRAM,
                 &iNVRAM_OPID,
                 (void*)&m_hWriteToNVRAMEvent);

    wait_result = WaitForSingleObject(m_hWriteToNVRAMEvent, 15000);

    if (WAIT_TIMEOUT == wait_result)
    {
        return META_TIMEOUT;
    }
    else if (WAIT_OBJECT_0)
    {
        CloseHandle (m_hWriteToNVRAMEvent);
    }

    if ( MetaResult != META_SUCCESS )
    {
        return MetaResult;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::REQ_ReadAP_NVRAM_Start(WriteData_Type_e dataType, char *pOutData, unsigned short iRID)
{
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadAP_NVRAM_Start()...");
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Read DataType is = %d", dataType);

    META_RESULT MetaResult = META_SUCCESS;
    int iBufLen = 0;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;
    switch (dataType)
    {
    case WRITE_BARCODE:
    case WRITE_IMEI:
	case WRITE_IMEI2:
        pLID = "AP_CFG_REEB_PRODUCT_INFO_LID";
        break;
	case WRITE_BT:
		iBufLen = 6;
		pLID = "AP_CFG_RDEB_FILE_BT_ADDR_LID";
		pDataFunc = &SmartPhoneSN::ConductBTAddrData;
		break;

	case WRITE_WIFI:
		iBufLen = 6;
		pLID = "AP_CFG_RDEB_FILE_WIFI_LID";
		pDataFunc = &SmartPhoneSN::ConductWifiAddrData;
		break;

	case WRITE_ETHERNET_MAC:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_ETHERNET_LID";
        pDataFunc = &SmartPhoneSN::ConductEthernetMacAddrData;
        break;

    default:
        return META_INVALID_ARGUMENTS;
    }

    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    int iWriteBufSize = 0;
    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = SP_META_NVRAM_GetRecLen(pLID, &iWriteBufSize);
    if (  META_SUCCESS != meta_result)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", ResultToString_SP(meta_result));
        return meta_result;
    }
    else
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", iWriteBufSize);
        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = iRID;
        sNVRAM_ReadCnf.len = iWriteBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize * sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromAPNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);

    if (meta_result != META_SUCCESS )
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "REQ_ReadFromAPNVRAM: fail! MetaResult = %s", ResultToString_SP(meta_result));
        return MetaResult;
    }

    if (dataType == WRITE_BARCODE)
    {
        // if(!strcmp(sNVRAM_ReadCnf.buf,"000000000000"))
        {
            int i = 0;
            for (i = 0; i < 64; i++)
            {
                if (!((sNVRAM_ReadCnf.buf[i] >= 'a' && sNVRAM_ReadCnf.buf[i] <= 'z')
                        || (sNVRAM_ReadCnf.buf[i] > 'A' && sNVRAM_ReadCnf.buf[i] < 'Z')
                        || (sNVRAM_ReadCnf.buf[i] >= '0' && sNVRAM_ReadCnf.buf[i] <= '9')))
                {
                    break;
                }
                else
                {
                    pOutData[i] = sNVRAM_ReadCnf.buf[i];
                }
            }

            pOutData[i] = '\0';
        }
    }

    if (dataType == WRITE_IMEI)
    {
        int i = 0;
        for (; i < 8; i++)
        {
            if ((sNVRAM_ReadCnf.buf[64 + i] & 15) >= '0' &&
                (sNVRAM_ReadCnf.buf[64 + i] & 15) <= '9' &&
                ((sNVRAM_ReadCnf.buf[64 + i] >> 4) & 15) >= '0' &&
                ((sNVRAM_ReadCnf.buf[64 + i] >> 4) & 240) <= '9')
            {
                break;
            }
            else
            {
                pOutData[2 * i] = (sNVRAM_ReadCnf.buf[64 + i] & 15 ) + '0';
            }

            pOutData[2 * i + 1] = (sNVRAM_ReadCnf.buf[64 + i] >> 4 & 15) + '0';
        }

        pOutData[2 * i - 1] = '\0';
    }
	if (dataType == WRITE_IMEI2)
	{
		int i = 0;
		for (; i < 8; i++)
		{
			if ((sNVRAM_ReadCnf.buf[74 + i] & 15) >= '0' &&
				(sNVRAM_ReadCnf.buf[74 + i] & 15) <= '9' &&
				((sNVRAM_ReadCnf.buf[74 + i] >> 4) & 15) >= '0' &&
				((sNVRAM_ReadCnf.buf[74 + i] >> 4) & 240) <= '9')
			{
				break;
			}
			else
			{
				pOutData[2 * i] = (sNVRAM_ReadCnf.buf[74 + i] & 15 ) + '0';
			}

			pOutData[2 * i + 1] = (sNVRAM_ReadCnf.buf[74 + i] >> 4 & 15) + '0';
		}

		pOutData[2 * i - 1] = '\0';
	}
	if (dataType == WRITE_BT)
	{
		//pDataType = "BT";
		for (int i = 0; i < iBufLen; i++)
		{
			pOutData[i] = sNVRAM_ReadCnf.buf[i];
		}
		char tmpReadData[13] = {0};
		(this->*pDataFunc)(tmpReadData, iRID, pOutData, iBufLen);
        strncpy(pOutData, tmpReadData, sizeof(tmpReadData));
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadAP_NVRAM_Start() BT:%s...",pOutData);
	}
	if (dataType == WRITE_WIFI)
	{
		//pDataType = "Wifi";
		//Wifi address offset is 0x4
		for (int i = 0; i < iBufLen; i++)
		{
			pOutData[i] = sNVRAM_ReadCnf.buf[0x4 + i];
		}
		char tmpReadData[13] = {0};
		(this->*pDataFunc)(tmpReadData, iRID, pOutData, iBufLen);
		strncpy(pOutData, tmpReadData, sizeof(tmpReadData));
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadAP_NVRAM_Start() WIFI:%s...",pOutData);
	}
	if (dataType == WRITE_ETHERNET_MAC)
	{
		//pDataType = "Ethernet_Mac";
		for (int i = 0; i < iBufLen; i++)
		{
			pOutData[i] = sNVRAM_ReadCnf.buf[i];
		}
		char tmpReadData[13] = {0};
		(this->*pDataFunc)(tmpReadData, iRID, pOutData, iBufLen);
		strncpy(pOutData, tmpReadData, sizeof(tmpReadData));
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadAP_NVRAM_Start() Ethernet Mac:%s...",pOutData);
	}

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadAP_NVRAM_Start() Success...");
    return MetaResult;
}

META_RESULT SmartPhoneSN::REQ_WriteAP_NVRAM_Start(WriteData_Type_e dataType, char *pInData, unsigned short iRID)
{
    if (!pInData)
    {
        return META_INVALID_ARGUMENTS;
    }
    m_bWriteNvram = true;

    int iBufLen = 0;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;

    switch (dataType)
    {
    case WRITE_BT:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_BT_ADDR_LID";
        pDataFunc = &SmartPhoneSN::ConductBTAddrData;
        break;

    case WRITE_WIFI:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_WIFI_LID";
        pDataFunc = &SmartPhoneSN::ConductWifiAddrData;
        break;

    case WRITE_ETHERNET_MAC:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_ETHERNET_LID";
        pDataFunc = &SmartPhoneSN::ConductEthernetMacAddrData;
        break;

    default:
        return META_INVALID_ARGUMENTS;
    }

    const int MAX_DATA_LENGTH = 6;
    int iMetaTimeout = 5000;

    AP_FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    char pWriteData[MAX_DATA_LENGTH] = {0};
    char pReadData[MAX_DATA_LENGTH] = {0};
    int iWriteBufSize = 0;
    unsigned long wifiChipVersion = 0;
    char *pFuncName = NULL;
    int rs = 0;

    memset(&sNVRAM_WriteReq, 0, sizeof(AP_FT_NVRAM_WRITE_REQ));
    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = SP_META_NVRAM_GetRecLen(pLID, &iWriteBufSize);
    if (  META_SUCCESS != meta_result)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", ResultToString_SP(meta_result));
        return meta_result;
    }
    else
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", iWriteBufSize);
        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = iRID;
        sNVRAM_ReadCnf.len = iWriteBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize * sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }


    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromAPNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto Err;
    }


    if (strcmp(pLID, "AP_CFG_RDEB_FILE_WIFI_LID") == 0)
    {
        MTRACE (g_hEBOOT_DEBUG, "SNBase::ConductWifiAddrData(): Start Conduct wifi nvram data...!");
        pFuncName = "SNBase::ConductWifiAddrData()";
        rs = (this->*pDataFunc)((char*)sNVRAM_ReadCnf.buf + 0x4, iRID, pInData, iBufLen);
        for (int i = 0; i < iBufLen; i++)
        {
            pWriteData[i] = sNVRAM_ReadCnf.buf[0x4 + i];
        }
    }
    else if (strcmp(pLID, "AP_CFG_RDEB_FILE_BT_ADDR_LID") == 0)
    {
        MTRACE (g_hEBOOT_DEBUG, "SNBase::ConductBTAddrData(): Start Conduct BT nvram data...!");
        pFuncName = "SNBase::ConductBTAddrData()";
        rs = (this->*pDataFunc)((char*)sNVRAM_ReadCnf.buf, iRID, pInData, iBufLen);
        for (int i = 0; i < iBufLen; i++)
        {
            pWriteData[i] = sNVRAM_ReadCnf.buf[i];
        }
    }
    else if (strcmp(pLID, "AP_CFG_RDEB_FILE_ETHERNET_LID") == 0)
    {
        MTRACE (g_hEBOOT_DEBUG, "SNBase::ConductEthernetMacAddrData(): Start Conduct Ethernet Mac nvram data...!");
        pFuncName = "SNBase::ConductEthernetMacAddrData()";
        rs = (this->*pDataFunc)((char*)sNVRAM_ReadCnf.buf, iRID, pInData, iBufLen);
        for (int i = 0; i < iBufLen; i++)
        {
            pWriteData[i] = sNVRAM_ReadCnf.buf[i];
        }
    }

    meta_result = (META_RESULT)rs;
    if (meta_result !=  META_SUCCESS)
    {
        goto Err;
    }
    MTRACE (g_hEBOOT_DEBUG, "%s: Conduct nvram data successfully!", pFuncName);

    sNVRAM_WriteReq.LID = pLID;
    sNVRAM_WriteReq.RID = iRID;
    sNVRAM_WriteReq.len = iWriteBufSize;
    sNVRAM_WriteReq.buf = sNVRAM_ReadCnf.buf;
    m_sNVRAM_OPID = iRID;

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Start to write nvram data...");
    meta_result =  REQ_WriteToAPNVRAM(sNVRAM_WriteReq);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_WriteToAPNVRAM()";
        goto Err;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Write nvram data successfully!");

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Read nvram data for check start...");
    memset(sNVRAM_ReadCnf.buf, 0 , sNVRAM_ReadCnf.len);
    meta_result = REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto Err;
    }
    else
    {
        char *pDataType = NULL;
        memset(pReadData, 0, sizeof(char)*MAX_DATA_LENGTH);
        if (strcmp(pLID, "AP_CFG_RDEB_FILE_WIFI_LID") == 0)
        {
            pDataType = "Wifi";
            //Wifi address offset is 0x4
            for (int i = 0; i < iBufLen; i++)
            {
                pReadData[i] = sNVRAM_ReadCnf.buf[0x4 + i];
            }
        }
        else if (strcmp(pLID, "AP_CFG_RDEB_FILE_BT_ADDR_LID") == 0)
        {
            pDataType = "BT";
            for (int i = 0; i < iBufLen; i++)
            {
                pReadData[i] = sNVRAM_ReadCnf.buf[i];
            }
        }
        else if (strcmp(pLID, "AP_CFG_RDEB_FILE_ETHERNET_LID") == 0)
        {
            pDataType = "Ethernet_Mac";
            for (int i = 0; i < iBufLen; i++)
            {
                pReadData[i] = sNVRAM_ReadCnf.buf[i];
            }
        }

        char tmpReadData[13] = {0};
        m_bWriteNvram = false;
        (this->*pDataFunc)(tmpReadData, iRID, pReadData, iBufLen);

        if (strncmp(pWriteData, pReadData, iBufLen) != 0)
        {
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Write_%s[%s]", pDataType, pInData);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Read_%s[%s]", pDataType, tmpReadData);
            MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN: Check nvram data FAIL!!");
            meta_result = META_FAILED;
        }
        else
        {
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Write_%s[%s]", pDataType, pInData);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Read_%s[%s]", pDataType, tmpReadData);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Check nvram data PASS!!");
        }
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToAPNVRAM(): Read nvram data for check end...");

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    return meta_result;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    MTRACE_ERR (g_hEBOOT_DEBUG, "%s: fail! MetaResult = %s", pFuncName, ResultToString_SP(meta_result));
    return meta_result;
}

META_RESULT SmartPhoneSN::SwitchMDByIndex(UINT MDIndex)
{
    if (m_bDSDAProject)
    {
        m_bInitExtMDdb = true;
        MDIndex = m_iMDChannelIndex[MDIndex];
        m_iCurMDChanelIndex = MDIndex;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SwitchMDByIndex(): Switch to MD[%d] start...", MDIndex);

    META_RESULT MetaResult;
    char *pFuncName = NULL;

    if (m_bDSDAProject)
    {
        pFuncName = "META_SwitchCurrentModemEx_r()";
        MetaResult = META_SwitchCurrentModemEx_r(m_hMauiMetaHandle, 10000, MDIndex,
                     m_SpMdCapList.modem_cap[MDIndex].md_service, m_SpMdCapList.modem_cap[MDIndex].ch_type, NULL, NULL);
    }
    else
    {
        pFuncName = "META_SwitchCurrentModem_r()";
        MetaResult = META_SwitchCurrentModem_r(m_hMauiMetaHandle, 5000, MDIndex);
    }

    if (MetaResult == META_SUCCESS)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::%s: Switch to MD[%d] successfully!!", pFuncName, MDIndex);
        /*
        if(MDIndex >= 1)
        {
        MetaResult = LoadModemDatabase(MDIndex);
        }
        */
    }
    else
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::%s: Switch to MD[%d] Fail, MetaResult = %s!!",
                   pFuncName, MDIndex, ResultToString(MetaResult));
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SwitchMDByIndex(): Switch to MD[%d] end...", MDIndex);
    return MetaResult;
}

META_RESULT SmartPhoneSN::REQ_ReadModem_NVRAM_Start(WriteData_Type_e dataType, char *pOutData, unsigned short iRID)
{

    if (!pOutData)
    {
        return META_INVALID_ARGUMENTS;
    }
    m_bWriteNvram = false;

    int iBufLen = 0;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;
    char *pFuncName = NULL;
    char *pDataType = NULL;

    switch (dataType)
    {
    case WRITE_BARCODE:
        iBufLen = 64;
        pDataType = "Barcode";
        pLID = "NVRAM_EF_BARCODE_NUM_LID";
        pDataFunc = &SmartPhoneSN::ConductBarcodeData;
        pFuncName = "SmartPhoneSN::ConductBarcodeData()";
        break;

    case WRITE_IMEI:
        iBufLen = 10;
        pDataType = "IMEI";
        pLID = "NVRAM_EF_IMEI_IMEISV_LID";
        pDataFunc = &SmartPhoneSN::ConductIMEIData;
        pFuncName = "SmartPhoneSN::ConductIMEIData()";
        break;

	case WRITE_MEID:
		iBufLen = 16;
		pDataType = "MEID";
		pLID = "NVRAM_EF_C2K_MOBILE_ID_LID";
		pDataFunc = &SmartPhoneSN::ConductMEIDData;
		pFuncName = "SmartPhoneSN::ConductMEIDData()";
		break;
    default:
        return META_INVALID_ARGUMENTS;
    }

    META_RESULT MetaResult = META_SUCCESS;

    int iMetaTimeout  = 5000;
    int iReadBufSize = 0;
    FT_NVRAM_READ_REQ  sNVRAM_ReadReq;
    FT_NVRAM_4BYTES_LID_READ_CNF  sNVRAM_ReadCnf;


    memset(&sNVRAM_ReadReq, 0, sizeof(FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_READ_CNF));

	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Start to get nvram struct size via LID = \"%s\"...", pLID);
	MetaResult = META_NVRAM_GetRecLen_r(m_hMauiMetaHandle, pLID, &iReadBufSize);
	if ( META_SUCCESS != MetaResult)
	{
		MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size fail, MetaResult = %s", ResultToString(MetaResult));
		return MetaResult;
	}
	else
	{
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size = %d successfully!", iReadBufSize);

        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = iRID;
        sNVRAM_ReadCnf.len = iReadBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iReadBufSize * sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Start to read nvram data...");
    MetaResult =  REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (MetaResult != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromModemNVRAM()";
        goto Err;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Read nvram data successfully!");

    memcpy(pOutData, sNVRAM_ReadCnf.buf, iReadBufSize);

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    return MetaResult;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    MTRACE_ERR (g_hEBOOT_DEBUG, "%s: fail! MetaResult = %s", pFuncName, ResultToString(MetaResult));

    return MetaResult;

}

//add peiqiang
META_RESULT SmartPhoneSN::REQ_WriteSignatureIMEI_NVRAM_Start( char *pInData, unsigned short iRID)
{
	if (!pInData || strlen(pInData) == 0)
	{
		return META_INVALID_ARGUMENTS;
	}
	char *pLID = "NVRAM_EF_CUSTOMER_SIGNED_CRITICAL_DATA_LID";
	FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
	FT_NVRAM_4BYTES_LID_WRITE_CNF sNVRAM_WriteCnf;
	FT_NVRAM_READ_REQ  sNVRAM_ReadReq;
	FT_NVRAM_4BYTES_LID_READ_CNF  sNVRAM_ReadCnf;
	META_RESULT meta_result = META_SUCCESS;
	int iMetaTimeout  = 5000;
	int iWriteBufSize = 0;
	memset(&sNVRAM_WriteReq, 0, sizeof(FT_NVRAM_WRITE_REQ));
	memset(&sNVRAM_WriteCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_WRITE_CNF));
	memset(&sNVRAM_ReadReq, 0, sizeof(FT_NVRAM_READ_REQ));
	memset(&sNVRAM_ReadCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_READ_CNF));
	MTRACE (g_hEBOOT_DEBUG, "REQ_WriteSignatureIMEI_NVRAM_Start(): pInData = \"%s\"...", pInData);
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Start to get nvram struct size via LID = \"%s\"...", pLID);
	meta_result = META_NVRAM_GetRecLen_r(m_hMauiMetaHandle, pLID, &iWriteBufSize);
	if ( META_SUCCESS != meta_result)
	{
		MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size fail, MetaResult = %s", ResultToString(meta_result));
		return meta_result;
	}
	else
	{
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size = %d successfully!", iWriteBufSize);

		if ( NULL != sNVRAM_ReadCnf.buf )
		{
			free ( sNVRAM_ReadCnf.buf );
			sNVRAM_ReadCnf.buf = NULL;
		}

		sNVRAM_ReadReq.LID = pLID;
		sNVRAM_ReadReq.RID = iRID;
		sNVRAM_ReadCnf.len = iWriteBufSize;
		sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize * sizeof(unsigned char));
		if (NULL == sNVRAM_ReadCnf.buf)
		{
			MTRACE_ERR (g_hEBOOT_DEBUG, "Malloc heap memory cause fail!");
			return  META_FAILED;
		}
	}
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Start to read nvram data...");
	meta_result =  REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
	if (meta_result != META_SUCCESS )
	{
		goto Err;
	}
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Read nvram data successfully!");
	sNVRAM_WriteReq.LID = pLID;
	sNVRAM_WriteReq.RID = iRID;
	sNVRAM_WriteReq.len = iWriteBufSize;
	sNVRAM_WriteReq.buf = (unsigned char*) pInData;
	sNVRAM_WriteCnf.RID = iRID;

	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToModemNVRAM(): Start to write nvram data...");
	meta_result =  REQ_WriteToModemNVRAM(&sNVRAM_WriteReq, &sNVRAM_WriteCnf);
	if (meta_result != META_SUCCESS )
	{

		goto Err;
	}
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToModemNVRAM(): Write nvram data successfully!");

	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToModemNVRAM(): Read nvram data for check start...");
	memset(sNVRAM_ReadCnf.buf, 0 , sNVRAM_ReadCnf.len);
	meta_result = REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
	if (meta_result != META_SUCCESS )
	{
		goto Err;
	}
	else
	{
		char pReadData[4096]={0};
		char pWriteData[4096]={0};
		memcpy(pReadData, sNVRAM_ReadCnf.buf, 4096);
		memcpy(pWriteData, pInData, 4096);
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Write[%s]", pInData);
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Read[%s]",sNVRAM_ReadCnf.buf);
		if (strncmp(pReadData, pWriteData, 4096) != 0)
		{
			MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN: Check nvram data FAIL!!");
			meta_result = META_FAILED;
		}
		else
		{
			MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Check nvram data PASS!!");
		}
	}
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToModemNVRAM(): Read nvram data for check end...");

	if (sNVRAM_ReadCnf.buf != NULL)
	{
		free (sNVRAM_ReadCnf.buf);
		sNVRAM_ReadCnf.buf = NULL;
	}

	return meta_result;

Err:
	if (sNVRAM_ReadCnf.buf != NULL)
	{
		free (sNVRAM_ReadCnf.buf);
		sNVRAM_ReadCnf.buf = NULL;
	}


	return meta_result;

}

META_RESULT SmartPhoneSN::REQ_WriteModem_NVRAM_Start(WriteData_Type_e dataType, char *pInData, unsigned short iRID)
{
    if (!pInData || strlen(pInData) == 0)
    {
        return META_INVALID_ARGUMENTS;
    }
    m_bWriteNvram = true;

    int iBufLen = 0;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;
    char *pFuncName = NULL;
    char pWriteData[64] = {0};
    char pReadData[64] = {0};
    char *pDataType = NULL;

    switch (dataType)
    {
    case WRITE_BARCODE:
        iBufLen = 64;
        pDataType = "Barcode";
        pLID = "NVRAM_EF_BARCODE_NUM_LID";
        pDataFunc = &SmartPhoneSN::ConductBarcodeData;
        pFuncName = "SmartPhoneSN::ConductBarcodeData()";
        break;

    case WRITE_IMEI:
        iBufLen = 10;
        pDataType = "IMEI";
        pLID = "NVRAM_EF_IMEI_IMEISV_LID";
        pDataFunc = &SmartPhoneSN::ConductIMEIData;
        pFuncName = "SmartPhoneSN::ConductIMEIData()";
        break;
			
	case WRITE_MEID:
		//iBufLen = 14;
		iBufLen = 16;
		pDataType = "MEID";
		pLID = "NVRAM_EF_C2K_MOBILE_ID_LID";
		pDataFunc = &SmartPhoneSN::ConductMEIDData;
		pFuncName = "SmartPhoneSN::ConductMEIDData()";
		break;
		
//zhangqi modify for set SN writer flag to barcode start
	case WRITE_BARCODE_FLAG:
		iBufLen = 64;
		pDataType = "Barcode";
		pLID = "NVRAM_EF_BARCODE_NUM_LID";
		pDataFunc = &SmartPhoneSN::ConductBarcodeDataFlag;
		pFuncName = "SmartPhoneSN::ConductBarcodeDataFlag()";
		break;
//zhangqi modify for set SN writer flag to barcode end

    default:
        return META_INVALID_ARGUMENTS;
    }


    int iMetaTimeout  = 5000;
    int iWriteBufSize = 0;
    FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
    FT_NVRAM_4BYTES_LID_WRITE_CNF sNVRAM_WriteCnf;
    FT_NVRAM_READ_REQ  sNVRAM_ReadReq;
    FT_NVRAM_4BYTES_LID_READ_CNF  sNVRAM_ReadCnf;
    META_RESULT meta_result = META_SUCCESS;

    memset(&sNVRAM_WriteReq, 0, sizeof(FT_NVRAM_WRITE_REQ));
    memset(&sNVRAM_WriteCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_WRITE_CNF));
    memset(&sNVRAM_ReadReq, 0, sizeof(FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_READ_CNF));

	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Start to get nvram struct size via LID = \"%s\"...", pLID);
	meta_result = META_NVRAM_GetRecLen_r(m_hMauiMetaHandle, pLID, &iWriteBufSize);
	if ( META_SUCCESS != meta_result)
	{
		MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size fail, MetaResult = %s", ResultToString(meta_result));
		return meta_result;
	}
	else
	{
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size = %d successfully!", iWriteBufSize);

        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = iRID;
        sNVRAM_ReadCnf.len = iWriteBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize * sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromModemNVRAM()";
        goto Err;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Read nvram data successfully!");

    meta_result = (this->*pDataFunc)((char*)sNVRAM_ReadCnf.buf, iRID, pInData, iBufLen);
    if (meta_result !=  META_SUCCESS)
    {
        goto Err;
    }
    else
    {
        /*
        for(int i = 0; i < iBufLen; i++)
        {
        writeData[i] = sNVRAM_ReadCnf.buf[i];
        }
        */
        memcpy(pWriteData, sNVRAM_ReadCnf.buf, iBufLen);
        MTRACE (g_hEBOOT_DEBUG, "%s: Conduct nvram data successfully!", pFuncName);
    }

    sNVRAM_WriteReq.LID = pLID;
    sNVRAM_WriteReq.RID = iRID;
    sNVRAM_WriteReq.len = iWriteBufSize;
    sNVRAM_WriteReq.buf = sNVRAM_ReadCnf.buf;
    m_sNVRAM_OPID = iRID;

    //sNVRAM_WriteCnf.status = 0;
    //sNVRAM_WriteCnf.LID = pLID;
    sNVRAM_WriteCnf.RID = iRID;

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToModemNVRAM(): Start to write nvram data...");
    meta_result =  REQ_WriteToModemNVRAM(&sNVRAM_WriteReq, &sNVRAM_WriteCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_WriteToModemNVRAM()";
        goto Err;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToModemNVRAM(): Write nvram data successfully!");

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToModemNVRAM(): Read nvram data for check start...");
    memset(sNVRAM_ReadCnf.buf, 0 , sNVRAM_ReadCnf.len);
    meta_result = REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_WriteToModemNVRAM()";
        goto Err;
    }
    else
    {
        char tmpReadData[64] = {0};
        m_bWriteNvram = false;
        memcpy(pReadData, sNVRAM_ReadCnf.buf, iBufLen);
        (this->*pDataFunc)(tmpReadData, iRID, pReadData, iBufLen);
        if (strncmp(pWriteData, pReadData, iBufLen) != 0)
        {
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Write_%s[%s]", pDataType, pInData);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Read_%s[%s]", pDataType, tmpReadData);
            MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN: Check nvram data FAIL!!");
            meta_result = META_FAILED;
        }
        else
        {

            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Write_%s[%s]", pDataType, pInData);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Read_%s[%s]", pDataType, tmpReadData);
            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN: Check nvram data PASS!!");
		}
		if (dataType == WRITE_IMEI)
		{
			strncpy_s(pInData, IMEI_ARRAY_LEN, tmpReadData, IMEI_ARRAY_LEN);
        }
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_WriteToModemNVRAM(): Read nvram data for check end...");

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    return meta_result;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    MTRACE_ERR (g_hEBOOT_DEBUG, "%s: fail! MetaResult = %s", pFuncName, ResultToString(meta_result));

    return meta_result;
}

META_RESULT SmartPhoneSN::GetNvramFileName(char * file_name, int buf_len)
{
    int offset;
    SYSTEMTIME time;

    if (g_sMetaComm.sWriteOption.bWriteIMEI)
    {
        file_name[0] = 'I';
        strcpy_s(file_name+1, buf_len-1, m_sScanData.strIMEI[0]);
    }
    else if(g_sMetaComm.sWriteOption.bWriteBarcode)
    {
        file_name[0] = 'B';
        strcpy_s(file_name+1, buf_len-1, m_sScanData.strBarcode);
    }
    else if (g_sMetaComm.sWriteOption.bWriteMeid)
    {
        file_name[0] = 'M';
        strcpy_s(file_name+1, buf_len-1, m_sScanData.strMeid);
    }
    else if(g_sMetaComm.sWriteOption.bWriteWifi)
    {
        file_name[0] = 'W';
        strcpy_s(file_name+1, buf_len-1, m_sScanData.strWifiAddr);
    }
    else if(g_sMetaComm.sWriteOption.bWriteBT)
    {
        file_name[0] = 'T';
        strcpy_s(file_name+1, buf_len-1, m_sScanData.strBTAddr);
    }
    else
    {
        file_name[0] = 'N';
    }

    offset = strlen(file_name);
    if (offset > 84) // buf size 100 - time length 14 - null end 1
        offset = 84;
    file_name[offset++] = '_';

    ::GetLocalTime(&time);
    sprintf_s(file_name + offset, buf_len - offset, "%04u%02u%02u%02u%02u%02u",
        time.wYear, time.wMonth, time.wDay, time.wHour, time.wMinute, time.wSecond);

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::QueryDBFromDUT()
{
    META_RESULT SPMetaResult = META_SUCCESS;
	unsigned int uiAPIRetry = 0;
	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
    	MTRACE(g_hEBOOT_DEBUG, "SP_META_QueryIfFunctionSupportedByTarget_r(): Query is suppport load db from DUT...");
    	SPMetaResult = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 3000, "SP_META_File_Operation_Parse_r");
		if (SPMetaResult == META_SUCCESS)
		{	
			break;
		}
	}
    if (SPMetaResult != META_SUCCESS)
        MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_QueryIfFunctionSupportedByTarget_r(): Query fail, %s.", ResultToString_SP(SPMetaResult));
    else
        MTRACE(g_hEBOOT_DEBUG, "SP_META_QueryIfFunctionSupportedByTarget_r(): Suppport load db from DUT.");

    return SPMetaResult;
}

META_RESULT SmartPhoneSN::GetAPDBFromDUT()
{
    META_RESULT SPMetaResult = META_SUCCESS;
    unsigned int tmp_m = 0;

    // init data for Travel
    char db_folder_dut[128] = "/system/etc/apdb";
    static const char * db_pattern = "APDB_";
    static const char * db_pattern_n = "_ENUM";
    char db_fullname_dut[256] = "";
    char db_fullname_pc[256] = "";
    FILE_OPERATION_PARSE_REQ fop_req;
    FILE_OPERATION_PARSE_CNF fop_cnf;
    FILE_OPERATION_GETFILEINFO_REQ fog_req;
    FILE_OPERATION_GETFILEINFO_CNF fog_cnf;
	unsigned int uiAPIRetry = 0;

	// query ap db path in dut
	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		SPMetaResult = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 3000, "SP_META_Query_APDBPath_r");
		if (SPMetaResult == META_SUCCESS)
		{	
			break;
		}
	}
    if (SPMetaResult != META_SUCCESS)
    {
        MTRACE(g_hEBOOT_DEBUG, "Don't support SP_META_Query_APDBPath_r(%d: %s), use default path %s. ",
            SPMetaResult, ResultToString_SP(SPMetaResult), db_folder_dut);
    }
    else
    {
        const int dbpathQueryCount = 30;
        QUERY_APDBPATH_REQ dbpath_req;
        QUERY_APDBPATH_CNF dbpath_cnf;

        memset(&dbpath_req, 0, sizeof(dbpath_req));
        memset(&dbpath_cnf, 0, sizeof(dbpath_cnf));
        for (tmp_m = 0; tmp_m < dbpathQueryCount; tmp_m++)
        {
            SPMetaResult = SP_META_Query_APDBPath_r(m_hSPMetaHandle, 3000, &dbpath_req, &dbpath_cnf);
            if (SPMetaResult == META_SUCCESS)
                break;
            MTRACE_WARN(g_hEBOOT_DEBUG, "Query AP DB path fail(%d, %s), wait 1s and retry.", SPMetaResult, ResultToString_SP(SPMetaResult));
            ::Sleep(1000);
        }
        if (tmp_m >= dbpathQueryCount)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "Query AP DB path fail, stop query.");
            return SPMetaResult;
        }
        strcpy_s(db_folder_dut, 128, (char *)dbpath_cnf.apdb_path);
        MTRACE(g_hEBOOT_DEBUG, "Get AP DB path %s.", db_folder_dut);
    }

	// get file count
	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		memset(&fop_req, 0, sizeof(fop_req));
		strcpy_s((char*)fop_req.path_name, 256, db_folder_dut);
		strcpy_s((char*)fop_req.filename_substr, 256, db_pattern);
		memset(&fop_cnf, 0, sizeof(fop_cnf));
		MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_Parse_r(): Find AP DB files from DUT...");
		SPMetaResult = SP_META_File_Operation_Parse_r(m_hSPMetaHandle, 3000, &fop_req, &fop_cnf);
		if (SPMetaResult == META_SUCCESS)
		{	
			break;
		}
	}
    if (SPMetaResult != META_SUCCESS)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_File_Operation_Parse_r(): Find fail, %s.", ResultToString_SP(SPMetaResult));
        return SPMetaResult;
    }
    MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_Parse_r(): Find %u AP DB file(s) from DUT.", fop_cnf.file_count);
    if (fop_cnf.file_count <= 0u)
        return META_FAILED;

    memset(&fog_req, 0, sizeof(fog_req));
    memset(&fog_cnf, 0, sizeof(fog_cnf));
    memset(db_fullname_dut, 0, 256);
    memset(db_fullname_pc, 0, 256);
    for (tmp_m = 0; tmp_m < fop_cnf.file_count; tmp_m++)
    {
        // get DB file info: type, size, full name
        fog_req.index = tmp_m;
        MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_GetFileInfo_r(): Get %dth AP DB file info...", tmp_m, ResultToString_SP(SPMetaResult));
        SPMetaResult = SP_META_File_Operation_GetFileInfo_r(m_hSPMetaHandle, 3000, &fog_req, &fog_cnf);
        if (SPMetaResult != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_File_Operation_GetFileInfo_r(): Get %dth AP DB file info fail, %s.", tmp_m, ResultToString_SP(SPMetaResult));
            continue;
        }
        MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_GetFileInfo_r(): %dth AP DB: type(%u) size(%u) name(%s).",
               tmp_m, fog_cnf.file_info.file_type, fog_cnf.file_info.file_size, (char*)fog_cnf.file_info.file_name);
        if (fog_cnf.file_info.file_size <= 0u)
            continue;

        // ap db file name don't has prefix "_APDB" and postfix "_ENUM"
        if (fog_cnf.file_info.file_name[0] == '_')
            continue;
        if (strstr((char *)fog_cnf.file_info.file_name, db_pattern_n) != NULL)
            continue;

        // trans file to pc
        sprintf_s(db_fullname_dut, "%s/%s", db_folder_dut, (char*)fog_cnf.file_info.file_name);
        sprintf_s(db_fullname_pc, "%s%s", m_strLogDir_Sub, (char*)fog_cnf.file_info.file_name);
        MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_ReceiveFile_r(): Save %dth AP DB file to %s...", tmp_m, db_fullname_pc, ResultToString_SP(SPMetaResult));
        SPMetaResult = SP_META_File_Operation_ReceiveFile_r(m_hSPMetaHandle, 8000, db_fullname_dut, db_fullname_pc);
        if (SPMetaResult != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_File_Operation_ReceiveFile_r(): Save AP DB file fail, %s.", tmp_m, db_fullname_pc, ResultToString_SP(SPMetaResult));
            continue;
        }
        MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_ReceiveFile_r(): Save %dth AP DB file ok.", tmp_m, db_fullname_pc, ResultToString_SP(SPMetaResult));

        // get the db path on pc
        strcpy_s(g_sMetaComm.sDBFileOption.strAPDbPath_DUT, db_fullname_pc);

        // current: only support ubin, only one modem db
        break;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::GetMDDBFromDUT()
{
    META_RESULT SPMetaResult = META_SUCCESS;
    unsigned int tmp_m = 0;
	unsigned int uiAPIRetry = 0;

    // init data for Travel
    char db_folder_dut[64] = "/system/etc/mddb";
    static const char * db_pattern = "MDDB.META_";
	const char * db_pattern_odb = "MDDB.META.O";//load odb,add 2020/4/28 
    char db_fullname_dut[256] = "";
    char db_fullname_pc[256] = "";
    FILE_OPERATION_PARSE_REQ fop_req;
    FILE_OPERATION_PARSE_CNF fop_cnf;
    FILE_OPERATION_GETFILEINFO_REQ fog_req;
    FILE_OPERATION_GETFILEINFO_CNF fog_cnf;

    // query ap db path in dut
	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		SPMetaResult = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 3000, "SP_META_MODEM_Query_MDDBPath_r");
		if (SPMetaResult == META_SUCCESS)
		{	
			break;
		}
	}
    if (SPMetaResult != META_SUCCESS)
    {
        MTRACE(g_hEBOOT_DEBUG, "Don't support SP_META_MODEM_Query_MDDBPath_r(%d: %s), use default path %s. ",
            SPMetaResult, ResultToString_SP(SPMetaResult), db_folder_dut);
    }
    else
    {
        const int dbpathQueryCount = 30;
        MODEM_QUERY_MDDBPATH_REQ dbpath_req;
        MODEM_QUERY_MDDBPATH_CNF dbpath_cnf;

        memset(&dbpath_req, 0, sizeof(dbpath_req));
        memset(&dbpath_cnf, 0, sizeof(dbpath_cnf));
        for (tmp_m = 0; tmp_m < dbpathQueryCount; tmp_m++)
        {
            SPMetaResult = SP_META_MODEM_Query_MDDBPath_r(m_hSPMetaHandle, 3000, &dbpath_req, &dbpath_cnf);
            if (SPMetaResult == META_SUCCESS)
                break;
            MTRACE_WARN(g_hEBOOT_DEBUG, "Query MD DB path fail(%d, %s), wait 1s and retry.", SPMetaResult, ResultToString_SP(SPMetaResult));
            ::Sleep(1000);
        }
        if (tmp_m >= dbpathQueryCount)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "Query MD DB path fail, stop query.");
            return SPMetaResult;
        }
        strcpy_s(db_folder_dut, 64, (char *)dbpath_cnf.mddb_path);
        MTRACE(g_hEBOOT_DEBUG, "Get MD DB path %s.", db_folder_dut);
    }

    // get file count
	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		memset(&fop_req, 0, sizeof(fop_req));
		strcpy_s((char*)fop_req.path_name, 256, db_folder_dut);
		//strcpy_s((char*)fop_req.filename_substr, 256, db_pattern);
		strcpy_s((char*)fop_req.filename_substr, sizeof(fop_req.filename_substr), db_pattern_odb);//load odb,add 2020/4/28 
		memset(&fop_cnf, 0, sizeof(fop_cnf));
		MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_Parse_r(): Find MD DB files from DUT...");
		SPMetaResult = SP_META_File_Operation_Parse_r(m_hSPMetaHandle, 3000, &fop_req, &fop_cnf);
		if (SPMetaResult == META_SUCCESS)
		{	
			break;
		}
	}
    if (SPMetaResult != META_SUCCESS)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_File_Operation_Parse_r(): Find fail, %s.", ResultToString_SP(SPMetaResult));
        return SPMetaResult;
    }
    MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_Parse_r(): Find %u MD DB file(s) from DUT.", fop_cnf.file_count);
    if (fop_cnf.file_count <= 0u)
	{
		MTRACE(g_hEBOOT_DEBUG, "Md ODb Path from DUT is zero, find edb next");
		Sleep(100);
		memset(&fop_req, 0, sizeof(fop_req));
		strcpy_s((char*)fop_req.path_name, sizeof(fop_req.path_name), db_folder_dut);
		strcpy_s((char*)fop_req.filename_substr, sizeof(fop_req.filename_substr), db_pattern);
		memset(&fop_cnf, 0, sizeof(fop_cnf));
		SPMetaResult = SP_META_File_Operation_Parse_r(m_hSPMetaHandle, 3000, &fop_req, &fop_cnf);
		if(SPMetaResult != META_SUCCESS)
		{
			return SPMetaResult;
		}
		if(fop_cnf.file_count <= 0u)
		{
			return META_FAILED;
		}
	}
    memset(&fog_req, 0, sizeof(fog_req));
    memset(&fog_cnf, 0, sizeof(fog_cnf));
    memset(db_fullname_dut, 0, 256);
    memset(db_fullname_pc, 0, 256);
    for (tmp_m = 0; tmp_m < fop_cnf.file_count; tmp_m++)
    {
        // get DB file info: type, size, full name
        fog_req.index = tmp_m;
        MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_GetFileInfo_r(): Get %dth MD DB file info...", tmp_m, ResultToString_SP(SPMetaResult));
        SPMetaResult = SP_META_File_Operation_GetFileInfo_r(m_hSPMetaHandle, 3000, &fog_req, &fog_cnf);
        if (SPMetaResult != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_File_Operation_GetFileInfo_r(): Get %dth MD DB file info fail, %s.", tmp_m, ResultToString_SP(SPMetaResult));
            continue;
        }
        MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_GetFileInfo_r(): %dth MD DB: type(%u) size(%u) name(%s).",
               tmp_m, fog_cnf.file_info.file_type, fog_cnf.file_info.file_size, (char*)fog_cnf.file_info.file_name);
        if (fog_cnf.file_info.file_size <= 0u)
            continue;

        // trans file to pc
        sprintf_s(db_fullname_dut, "%s/%s", db_folder_dut, (char*)fog_cnf.file_info.file_name);
        sprintf_s(db_fullname_pc, "%s%s", m_strLogDir_Sub, (char*)fog_cnf.file_info.file_name);
        MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_ReceiveFile_r(): Save %dth MD DB file to %s...", tmp_m, db_fullname_pc, ResultToString_SP(SPMetaResult));
        SPMetaResult = SP_META_File_Operation_ReceiveFile_r(m_hSPMetaHandle, 8000, db_fullname_dut, db_fullname_pc);
        if (SPMetaResult != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "SP_META_File_Operation_ReceiveFile_r(): Save MD DB file fail, %s.", tmp_m, db_fullname_pc, ResultToString_SP(SPMetaResult));
            continue;
        }
        MTRACE(g_hEBOOT_DEBUG, "SP_META_File_Operation_ReceiveFile_r(): Save %dth MD DB file ok.", tmp_m, db_fullname_pc, ResultToString_SP(SPMetaResult));

        // get the db path on pc
        strcpy_s(g_sMetaComm.sDBFileOption.strMD1DbPath_DUT, db_fullname_pc);

        // current: only support ubin, only one modem db
        break;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::LoadAPDatabase()
{
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::LoadAPDatabase() start...");

    META_RESULT MetaResult = META_SUCCESS;
    const char * dbpath = NULL;
    unsigned long db;

    if (g_sMetaComm.sDBFileOption.bAPDBFromDUT && g_sMetaComm.sDBFileOption.strAPDbPath_DUT[0] != '\0')
        dbpath = g_sMetaComm.sDBFileOption.strAPDbPath_DUT;
    else
        dbpath = g_sMetaComm.sDBFileOption.strAPDbpath;

    if (g_sMetaComm.sDBFileOption.bDBInitAP)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadAPDatabase(): Already init AP database done, DB path = %s", dbpath);
        MetaResult = META_SUCCESS;
        goto _end;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadAPDatabase(): Start to init AP database, DB path = %s", dbpath);
    MetaResult = SP_META_NVRAM_Init_r(m_hSPMetaHandle, dbpath, &db);
    if ( MetaResult == META_SUCCESS)
    {
        g_sMetaComm.sDBFileOption.bDBInitAP = true;
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadAPDatabase(): ok.");
    }
    else if (MetaResult == META_MAUI_DB_INCONSISTENT && g_sMetaComm.IgnoreDBInconsistent)
    {
        g_sMetaComm.sDBFileOption.bDBInitAP = true;
        MTRACE_WARN(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadAPDatabase(): ap db inconsistent, but ignore.");
        MetaResult = META_SUCCESS;
    }
    else
    {
        g_sMetaComm.sDBFileOption.bDBInitAP = false;
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadAPDatabase(): fail, %s", ResultToString_SP(MetaResult));
    }

_end:
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::LoadAPDatabase() end.");
    return MetaResult;
}

META_RESULT SmartPhoneSN::LoadModemDatabase(int MDindex)
{
    META_RESULT meta_result = META_SUCCESS;
    char *pFuncName = NULL;

    bool bInitMD1Done = true;

    bool *pbInitDBDone = NULL;
    const char *pStrMDDbpath = NULL;
    unsigned long db = 0;
	uintptr_t db1 = 0;

    switch (MDindex)
    {
    case 0:
        pbInitDBDone = &g_sMetaComm.sDBFileOption.bDBInitModem_1;
        if (g_sMetaComm.sDBFileOption.bMDDBFromDUT && g_sMetaComm.sDBFileOption.strMD1DbPath_DUT[0] != '\0')
            pStrMDDbpath = g_sMetaComm.sDBFileOption.strMD1DbPath_DUT;
        else
            pStrMDDbpath = g_sMetaComm.sDBFileOption.strMD1Dbpath;
        break;

    case 1:
        pbInitDBDone = &g_sMetaComm.sDBFileOption.bDBInitModem_2;
        pStrMDDbpath = g_sMetaComm.sDBFileOption.strMD2Dbpath;
        break;

    default:
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::LoadModemDatabase(): Incorrect MDIndex = %d!", MDindex);
        return META_INVALID_ARGUMENTS;
        break;
    }

    if (pStrMDDbpath == NULL || pStrMDDbpath[0] == '\0')
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadModemDatabase(): Incorrect database file path!");
        return META_INVALID_ARGUMENTS;
    }

    if (*pbInitDBDone)
    {
        // For MT6595 DSDA project
        if (m_bDSDAProject)
        {
            MDindex = m_iCurMDChanelIndex;
        }
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_Init_r(): MD[%d] database already init done!",  MDindex);
        return META_SUCCESS;
    }

    //if (g_sMetaComm.eTargetType == SMART_PHONE_DUALMODEM)
    if ((m_bDualModem == true && m_bWorldPhone == false) /*|| g_sMetaComm.eTargetType == SMART_PHONE_DUALMODEM*/)
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_Init_Ex_r(): start to init MD[%d] database, DB path = \"%s\"...",
                MDindex, pStrMDDbpath);
        meta_result = META_NVRAM_Init_Ex_r(m_hMauiMetaHandle, MDindex, pStrMDDbpath, &db);
        pFuncName = "META_NVRAM_Init_Ex_r";
    }
    else
    {
        MDindex = m_sMdInfo.active_md_idx - 1;
        //For MT6595 DSDA project
        if (m_bDSDAProject)
        {
            MDindex = m_iCurMDChanelIndex;
            if (m_bInitExtMDdb)
            {
                m_sMdInfo.activeMdTypeIdx = 0;
            }
        }

		/*MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_Init_Ex_Mdtype_r(): start to init MD[%d] database, DB path = \"%s\"...",
			MDindex, pStrMDDbpath);
		meta_result = META_NVRAM_Init_Ex_Mdtype_r(m_hMauiMetaHandle, MDindex, m_sMdInfo.activeMdTypeIdx, pStrMDDbpath, &db);
		pFuncName = "META_NVRAM_Init_Ex_Mdtype_r";*/

		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::META_NVRAM_Init_Md_Mdtype_r(): start to init MD[%d] database, DB path = \"%s\"...",
			MDindex, pStrMDDbpath);
		meta_result = META_NVRAM_Init_Md_Mdtype_r(m_hMauiMetaHandle, MDindex, m_sMdInfo.activeMdTypeIdx, pStrMDDbpath, &db1);
		pFuncName = "META_NVRAM_Init_Md_Mdtype_r";
    }

    if (meta_result == META_SUCCESS)
    {
        *pbInitDBDone = true;
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::%s(): Init MD[%d] database successfully!", pFuncName, MDindex);
    }
    else if (meta_result == META_MAUI_DB_INCONSISTENT && g_sMetaComm.IgnoreDBInconsistent)
    {
        *pbInitDBDone = true;
        MTRACE_WARN(g_hEBOOT_DEBUG, "SmartPhoneSN::%s(): md db inconsistent, but ignore.", pFuncName);
        meta_result = META_SUCCESS;
    }
    else
    {
        *pbInitDBDone = false;
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::%s(): Init MD[%d] database Fail, MetaResult = %s",
                   pFuncName, MDindex, ResultToString(meta_result));
    }

    return meta_result;
}

META_RESULT SmartPhoneSN::ExitAPMetaMode()
{
    META_RESULT spMetaResult;
    char *pFuncName = NULL;

    if (!m_bTargetInMetaMode)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ExitAPMetaMode(): not in meta mode.");
        return META_SUCCESS;
    }

    if (g_sMetaComm.bKeepInMeta)
    {
        spMetaResult = SP_META_DisconnectInMetaMode_r (m_hSPMetaHandle);
        pFuncName = "SP_META_DisconnectInMetaMode_r()";
    }
    else
    {
        spMetaResult = SP_META_DisconnectWithTarget_r (m_hSPMetaHandle);
        pFuncName = "SP_META_DisconnectWithTarget_r()";
    }
    if (spMetaResult == META_SUCCESS)
        m_bTargetInMetaMode = false;

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ExitAPMeta(): Exit meta mode by %s successfully! MetaResult = %s", pFuncName, ResultToString_SP(spMetaResult));
    return spMetaResult;
}

META_RESULT SmartPhoneSN::EnableModemMeta()
{
    int iRet = 0;

    MDLogSetParam(m_sMdInfo.active_md_idx-1);

    MTRACE(g_hEBOOT_DEBUG, "META_DLL::META_ConnectWithMultiModeTarget_r(): Connect to MODEM meta mode...");
    m_tMetaReq_Ex.com_port = m_nKernelComport;
    iRet = META_ConnectWithMultiModeTarget_r(m_hMauiMetaHandle, &m_tMetaReq_Ex, sizeof(m_tMetaReq_Ex), m_pMetaStopFlag, &m_tMetaConnReport_Ex);
    if (iRet == META_SUCCESS)
    {
        MTRACE(g_hEBOOT_DEBUG, "META_DLL::META_ConnectWithMultiModeTarget_r(): ok.");
        iRet = META_SUCCESS;
        m_eMetaMode = SP_MODEM_META;
    }
    else if (iRet == META_MAUI_DB_INCONSISTENT)
    {
        m_eMetaMode = SP_MODEM_META;
        if (!g_sMetaComm.sDBFileOption.bDBInitModem_1)
        {
            MTRACE(g_hEBOOT_DEBUG, "META_DLL::META_ConnectWithMultiModeTarget_r(): md db inconsistent, directly ignore.");
            iRet = META_SUCCESS;
        }
        else if (g_sMetaComm.IgnoreDBInconsistent)
        {
            MTRACE_WARN(g_hEBOOT_DEBUG, "META_DLL::META_ConnectWithMultiModeTarget_r(): md db inconsistent, but ignore.");
            iRet = META_SUCCESS;
        }
        else
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "META_DLL::META_ConnectWithMultiModeTarget_r(): fail, md db inconsistent.");
        }
    }
    else
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "META_DLL::META_ConnectWithMultiModeTarget_r(): fail, %s", ResultToString(iRet));
    }

    if (iRet == META_SUCCESS)
        MDLogOn();

    return (META_RESULT)iRet;
}

void SmartPhoneSN::SetupMetaModeParameters()
{
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SetupMetaModeParameters() start...");
    //For preloader handshake parameters
    memset (&m_stModeArg, 0x0, sizeof(SP_BOOT_ARG_S));
    memset(&m_tMetaReq_Ex, 0, sizeof(m_tMetaReq_Ex));
    memset(&m_tMetaConnReport_Ex, 0, sizeof(m_tMetaConnReport_Ex));

    if (g_sMetaComm.iPre_Connect_Timeout < 10000)
    {
        g_sMetaComm.iPre_Connect_Timeout = 10000;
    }

    if (g_sMetaComm.iKernel_Connect_Timeout < 20000)
    {
        g_sMetaComm.iKernel_Connect_Timeout = 20000;
    }

    m_nKernelComport = g_sMetaComm.iCOMPort;

    //Old parameters
    m_stModeArg.m_bbchip_type = SP_AUTO_DETECT_BBCHIP;
    m_stModeArg.m_ext_clock = SP_AUTO_DETECT_EXT_CLOCK;
    m_stModeArg.m_ms_boot_timeout = SP_BOOT_INFINITE;
    //m_stModeArg.m_max_start_cmd_retry_count = SP_DEFAULT_BROM_START_CMD_RETRY_COUNT;
    m_stModeArg.m_max_start_cmd_retry_count = 0;
    //New parameters
    m_stModeArg.m_uTimeout = g_sMetaComm.iPre_Connect_Timeout;
    m_stModeArg.m_uRetryTime = 2000;
    m_stModeArg.m_uInterval = 10;
    m_stModeArg.m_uBaudrate = CBR_115200;
    m_stModeArg.m_pStopFlag  = &g_iMetaStopFlag;

    if (g_sMetaComm.bSecurityUSB)
    {
        m_stModeArg.m_auth_handle = SPATE_Get_AuthHandle();
        m_stModeArg.m_scert_handle = SPATE_Get_ScertHandle();
        m_stModeArg.m_cb_sla_challenge = SLA_Challenge;
        m_stModeArg.m_cb_sla_challenge_arg = NULL;
        m_stModeArg.m_cb_sla_challenge_end = SLA_Challenge_END;
        m_stModeArg.m_cb_sla_challenge_end_arg = NULL;

		//PL_SLA start
		m_stSLaArg.m_cb_pl_sla_challenge = PL_SLA_Challenge;
		m_stSLaArg.m_cb_pl_sla_challenge_arg = NULL;
		m_stSLaArg.m_cb_pl_sla_challenge_end = PL_SLA_Challenge_END;
		m_stSLaArg.m_cb_pl_sla_challenge_end_arg = NULL;
		//PL_SLA end
    }
    else
    {
        m_stModeArg.m_auth_handle = NULL;
        m_stModeArg.m_scert_handle = NULL;
        m_stModeArg.m_cb_sla_challenge = NULL;
        m_stModeArg.m_cb_sla_challenge_arg = NULL;
        m_stModeArg.m_cb_sla_challenge_end = NULL;
        m_stModeArg.m_cb_sla_challenge_end_arg = NULL;
    }
    m_stModeArg.m_bIsUSBEnable = g_sMetaComm.bUsbEnable;
    m_stModeArg.m_bIsSymbolicEnable = false;
    m_stModeArg.m_bIsCompositeDeviceEnable = g_sMetaComm.bCompositeDeviceEnable;
    m_stModeArg.m_euBootMode = SP_META_BOOT;
    m_stModeArg.m_uPortNumber = 0;
    m_stModeArg.m_uMDMode = 0;

    //Common
    m_tMetaReq_Ex.com_port = m_nKernelComport;
    m_tMetaReq_Ex.ms_connect_timeout = g_sMetaComm.iKernel_Connect_Timeout;
    m_tMetaReq_Ex.close_com_port = 1;
    m_tMetaReq_Ex.InMetaMode = 1;
    m_tMetaReq_Ex.protocol = 1;          // DHL
    m_tMetaReq_Ex.channel_type = 1;      // Native channel
	m_tMetaReq_Ex.boot_meta_arg.m_bbchip_type = SP_AUTO_DETECT_BBCHIP;
	m_tMetaReq_Ex.boot_meta_arg.m_ext_clock = SP_AUTO_DETECT_EXT_CLOCK;
    //UART parameters
    m_tMetaReq_Ex.baudrate[0] = META_BAUD921600;
    m_tMetaReq_Ex.baudrate[1] = META_BAUD_END;
    m_tMetaReq_Ex.flowctrl = META_SW_FLOWCTRL;
    //USB
    if (g_sMetaComm.bUsbEnable)
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SetupMetaModeParameters(): Target enter meta mode by [USB].");
        m_tMetaReq_Ex.usb_enable = 1;
        m_tMetaReq_Ex.boot_meta_arg.m_usb_enable = 1;
    }
    else
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SetupMetaModeParameters(): Target enter meta mode by [UART].");
        m_tMetaReq_Ex.usb_enable = 0;
        m_tMetaReq_Ex.boot_meta_arg.m_usb_enable = 0;
        m_tMetaReq_Ex.escape = 1;
    }

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SetupMetaModeParameters() end...");
}

META_RESULT SmartPhoneSN::EnterAPMetaMode()
{
    META_RESULT spMetaResult = META_SUCCESS;
    int bootResult = 0;

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaMode() : Enter ap meta start...");

	if (g_sMetaComm.eTargetType != THIN_MODEM_FLASHLESS)
	{
		UpdateUIMsg("Disable MIPC Port.");
		UpdateProgress(0.07);
		Sleep(500);
	}
	
	if (g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
	{   
		int iRet = 0;
		METAAPP_DATACARD_CONNCT_CNF_T pCnf;
		memset(&pCnf,0,sizeof(pCnf));

		UpdateUIMsg("Enable MIPC Port.");
		Sleep(500);
		UpdateProgress(0.07);
		ArgBootDCMetaMode();

		//Assign comport
		m_DCMetaAppConnSet.autoScanPort = false;
		m_DCMetaAppConnSet.connectSetting[MD_META].bConnection = true;
		m_DCMetaAppConnSet.connectSetting[AP_META].bConnection = false; 
		m_DCMetaAppConnSet.connectSetting[AP_META].kernelComPort = g_sMetaComm.iMDKernelComport;
		m_DCMetaAppConnSet.connectSetting[MD_META].kernelComPort = g_sMetaComm.iMDKernelComport;

		MTRACE (g_hEBOOT_DEBUG, "METAAPP_ConnectDCToAPMeta_r() : Begin to connect META ...");
		UpdateUIMsg("Begin to connect META ...");
		iRet = METAAPP_ConnectDCToAPMeta_r(m_hSPMetaHandle,&m_DCMetaAppConnSet,&pCnf,sizeof(m_DCMetaAppConnSet));

		if (iRet == 0)
		{
			UpdateUIMsg("Connect META success!");
			MTRACE (g_hEBOOT_DEBUG, "CMtkSpDataCardMetaBase::BootMetaMode(): METAAPP_ConnectDCToAPMeta_r success!");
		}
		else if(iRet == METAAPP_HIGH_LEVEL_AP_DATABASE_NOT_MATCH)
		{
			UpdateUIMsg("META DB DISMATCH!");
			MTRACE_ERR(g_hEBOOT_DEBUG, "CMtkSpDataCardMetaBase::BootMetaMode(): METAAPP_ConnectDCToAPMeta_r DB DISMATCH!"); 
			if(!g_sMetaComm.IgnoreDBInconsistent)
			{   
				m_bTargetInMetaMode = true;
				m_eMetaMode = SP_AP_META;
				return META_MAUI_DB_INCONSISTENT;
			}
			else
			{
				UpdateUIMsg("METAAPP_ConnectDCToAPMeta_r success!");
				MTRACE (g_hEBOOT_DEBUG, "CMtkSpDataCardMetaBase::BootMetaMode(): METAAPP_ConnectDCToAPMeta_r success!");
				//iRet = 0;
			}
		}
		else
		{
			UpdateUIMsg("Connect Fail!");
			MTRACE_ERR(g_hEBOOT_DEBUG, "CMtkSpDataCardMetaBase::BootMetaMode(): METAAPP_ConnectDCToAPMeta_r, Err = %d", iRet);
			return META_FAILED;
		}
		//m_hSPMetaHandle = pCnf.hHandle;
		//m_hMauiMetaHandle = pCnf.hHandle;

		UpdateUIMsg("Connect META success!");
		MTRACE(g_hEBOOT_DEBUG, "CMtkSpDataCardMetaBase::BootMetaMode(): Connect success!");

		return META_SUCCESS;
	}

    if (!g_sMetaComm.bAlreadyInMeata)
    {
        UpdateUIMsg("Start searching preloader com port and handshake...");
        UpdateProgress(0.08);

        bootResult = ConnectWithPreloader();
        if (bootResult != 0)
        {
            UpdateUIMsg("Searching preloader com port and handshake Fail.");

            spMetaResult = (META_RESULT)bootResult;
            return spMetaResult;
        }

        // Kernel Port apear at least take 6s, Wait 4s for Preloader Port disapear
        //  when Preloader and Kernel has the same port
        Sleep(4000);
    }
    else
    {
        UpdateUIMsg("Already in Meta Mode, bypass preloader com port handshake.");
        UpdateProgress(0.08);
    }

    UpdateUIMsg("Start searching kernel com port and handshake...");
    UpdateProgress(0.10);

    bootResult = ConnectWithKernelPort_Ex();
    UpdateProgress(0.12);
    if (bootResult == 0)
    {
        UpdateUIMsg("Searching kernel com port and handshake OK.");
    }
    else
    {
        UpdateUIMsg("Searching kernel com port and handshake Fail.");
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaMode() : Enter ap meta end.");
    spMetaResult = (META_RESULT)bootResult;
    return spMetaResult;
}

//Add for DSDA Extern Modem Native Download
#if 0
int SmartPhoneSN::DSDA_QueryNativeDownload()    //0:query fail   1:need to download   2:do not need to download
{
    int iRet = 0;
    META_RESULT spMetaResult = META_SUCCESS;

    MODEM_QUERY_DOWNLOAD_STATUS_REQ qDownloadReq;
    MODEM_QUERY_DOWNLOAD_STATUS_CNF qDownloadCnf;

    memset(&qDownloadReq, 0, sizeof(MODEM_QUERY_DOWNLOAD_STATUS_REQ));
    memset(&qDownloadCnf, 0, sizeof(MODEM_QUERY_DOWNLOAD_STATUS_CNF));

    for (int i = 0; i < 50; i++)
    {
        spMetaResult = SP_META_MODEM_Query_Download_Status_r(m_hSPMetaHandle, 2000, &qDownloadReq, &qDownloadCnf);
        if (spMetaResult == META_SUCCESS) break;
    }

    if (spMetaResult != META_SUCCESS)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_QueryNativeDownload() : Query extern modem download status fail! MetaResult = %s!", ResultToString_SP(spMetaResult));
        UpdateUIMsg("Query download extern modem status fail!");
        iRet = 1;
    }
    else
    {
        if (qDownloadCnf.percentage != m_iLastPercentage)
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_QueryNativeDownload() : Download status = %d / %d", qDownloadCnf.percentage, qDownloadCnf.status_code);
        }
        m_iLastPercentage = qDownloadCnf.percentage;

        if (qDownloadCnf.percentage == 110 && qDownloadCnf.status_code == 0000)
        {
            //Have downloaded already
            iRet = 0;
        }
        else
        {
            //Need to download
            iRet = 2;
        }
    }
    return iRet;
}
META_RESULT SmartPhoneSN::DSDA_StartNativeDownload()
{
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_StartNativeDownload() : Extern modem native download start!");
    META_RESULT spMetaResult = META_SUCCESS;

    MODEM_TRIGGER_NATIVE_DOWNLOAD_REQ DownloadReq;
    MODEM_TRIGGER_NATIVE_DOWNLOAD_CNF DownloadCnf;

    memset(&DownloadReq, 0, sizeof(MODEM_TRIGGER_NATIVE_DOWNLOAD_REQ));
    memset(&DownloadCnf, 0, sizeof(MODEM_TRIGGER_NATIVE_DOWNLOAD_CNF));

    spMetaResult = SP_META_MODEM_TRIGGER_NATIVE_DOWNLOAD_r(m_hSPMetaHandle, 20000, &DownloadReq, &DownloadCnf);
    if (spMetaResult != META_SUCCESS)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_StartNativeDownload() : Extern modem native download fail! MetaResult = %s!", ResultToString_SP(spMetaResult));
        return spMetaResult;
    }
    else
    {
        //Sleep(10000);
        int QueryResult = -1;
        m_iLastPercentage = 0;
        do
        {
            QueryResult = DSDA_QueryNativeDownload();
        } while (QueryResult == 2);

        if (QueryResult != 0)
        {
            spMetaResult = META_FAILED;
        }
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_StartNativeDownload() : Extern modem native download finish!");
    return spMetaResult;
}
#endif

//Add for DSDA Extern Modem Backup and Restore
bool SmartPhoneSN::DSDA_ExternalModemBackup()
{
    META_RESULT MetaResult;
    // query if bakcup function support
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_ExternalModemBackup() : Start query if target support backup function...");
    MetaResult = META_QueryIfFunctionSupportedByTarget_r(m_hMauiMetaHandle, 5000, "META_MISC_EX_BackupCalibrationToStorage_r");
    if (META_SUCCESS != MetaResult)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_ExternalModemBackup() : Query target backup function fail! MetaResult = %s", ResultToString(MetaResult));
        return false;
    }
    // trigger external MODEM backup
    unsigned int status = 0;
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_ExternalModemBackup() : Start backup data to SDS...");
    MetaResult = META_MISC_EX_BackupCalibrationToStorage_r(m_hMauiMetaHandle, 2000, 0, &status);
    if (META_SUCCESS != MetaResult || status != 0)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_ExternalModemBackup() : Backup fail! MetaResult = %s", ResultToString(MetaResult));
        return false;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::DSDA_ExternalModemBackup() : Backup finish!");
    return true;
}


void SmartPhoneSN::ThreadMainEntryPoint()
{
    int iTestCount = 0;
    bool bNeedScanData = false;
    bool bSkipScanData = false;
    bool bNeedLoadAPDB = false;
    int times = 0;
    int Ret = 0;
	unsigned int uiAPIRetry = 0;

    /*For dual talk(external modem) project
    * Due to dual talk project can`t switch back and forth between ap and external modem
    * When target type select tablet  and enable external modem option, mean that will write ap nvram, so need disable write external modem nvram
    * so it is mean(ex md + tablet to write bt wifi; ex md + tablet to write barcode imei;)
    */
    bool bWriteBarcode = g_sMetaComm.sWriteOption.bWriteBarcode;
    bool bWriteIMEI = g_sMetaComm.sWriteOption.bWriteIMEI;
    bool bNeedSupportTEE = g_sMetaComm.bCheckSupportTEE;

    if (g_sMetaComm.bDualTalk)
    {
        g_sMetaComm.sWriteOption.bWriteBarcode = false;
        g_sMetaComm.sWriteOption.bWriteIMEI = false;
    }

    bNeedScanData = NeedScanData();
    if (bNeedScanData == false)
    {
        if (!g_sMetaComm.sWriteOption.bWriteDrm &&
            !g_sMetaComm.sWriteOption.bWriteHdcp &&
            !g_sMetaComm.sWriteOption.bInstallHdcpData &&
            !g_sMetaComm.sWriteOption.bWriteAttestationKey &&
			!g_sMetaComm.sWriteOption.bGetCsrFromDut)
		{
#if 0
            PopupMsgBox("Warning", MB_OK | MB_ICONWARNING, "At least select one write option!!");
            return;
#endif
        }

        // Don't need to Popup scan data dialog
        bSkipScanData = true;
        if (g_sMetaComm.sWriteOption.bWriteHdcp || g_sMetaComm.sWriteOption.bGetCsrFromDut)
        {
            // Write DRM Key & Install Hdcp data don't need to load ap database
            bNeedLoadAPDB = true;
        }
    }
    else
    {
        if (g_sMetaComm.sWriteOption.bWriteBarcode ||
            g_sMetaComm.sWriteOption.bWriteIMEI ||
			g_sMetaComm.sWriteOption.bWriteSIMEI ||
            g_sMetaComm.sWriteOption.bWriteBT ||
            g_sMetaComm.sWriteOption.bWriteWifi ||
            g_sMetaComm.sWriteOption.bWriteEthernetMac ||
            g_sMetaComm.sWriteOption.bWriteDrmkeyMCID ||
            g_sMetaComm.sWriteOption.bWriteSerialNo ||
            g_sMetaComm.sWriteOption.bWriteMeid ||
            g_sMetaComm.bClearMetaBootFlag ||
			g_sMetaComm.sWriteOption.bGetCsrFromDut)
        {
            bNeedLoadAPDB = true;
        }
    }
	if(g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
	{
		bNeedLoadAPDB = false;
	}

    bool bAnyOperationFail = false;
    META_RESULT MetaResult = META_SUCCESS;
    META_RESULT SPMetaResult = META_SUCCESS;

    if (g_sMetaComm.bSwithTool)
        EnableUSBSwitch();

    if (g_sMetaComm.bCheckFastbootLock)
    {
        UpdateUIMsg("Start Adb_Manager_Init() and Set_Fastboot_EnvironmentVariable()...");
        if (!Set_Fastboot_EnvironmentVariable())
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable() FAIL...");
            UpdateUIMsg("Set Fastboot Environment Variable Fail!!");
            return;
        }
        UpdateUIMsg("Set Fastboot Environment Variable OK!!");
        if (!Adb_Manager_Init())
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Manager_Init() FAIL...");

            Adb_Manager_Deinit();
            UpdateUIMsg("SmartPhoneSN::Adb_Manager_Init() FAIL...");
            return;
        }
        UpdateUIMsg("SmartPhoneSN::Adb_Manager_Init() OK...");
    }
    m_hMauiMetaHandle = INVALID_META_HANDLE;
    m_hSPMetaHandle = INVALID_META_HANDLE;
    //Init meta handle
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::MetaHandle_Init() start...");
    MetaResult = MetaHandle_Init();
    if (MetaResult != META_SUCCESS)
    {
        bAnyOperationFail = true;
        UpdateUIMsg("MetaHandle_Init() : MetaResult = %s", ResultToString_SP(MetaResult));
        *m_pMetaStopFlag = BOOT_STOP;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::MetaHandle_Init() end...");

    CScanData scanDlg;

    while(true)
    {
        if (*m_pMetaStopFlag == BOOT_STOP)
            break;
        if (g_sMetaComm.iStressTest > 1 && iTestCount > 0)
        {
            if (iTestCount >= g_sMetaComm.iStressTest)
                break;
        }
        else if (!bSkipScanData && scanDlg.DoModal() != IDOK)
            break;
        else if (bSkipScanData && iTestCount > 0 &&
            ::AfxMessageBox(_T("Start write next devices ?"), MB_YESNO|MB_ICONQUESTION) != IDYES)
            break;
        iTestCount++;

		DebugOnOff(true);

		if (g_sMetaComm.smesconnect.mesconnectcheck == TRUE)
		{
			bool ret;
			char buf[1024] = {0};

			if(g_sMetaComm.smesconnect.strMes[0] == 'L')
			{
				MesServerProxy proxy;
				ret = proxy.CheckUserAndResourcePassed(g_sMetaComm.smesconnect.Username, g_sMetaComm.smesconnect.Resource, g_sMetaComm.smesconnect.Password, buf);

				if(!ret)
				{
					//MessageBox(NULL, buf, "fail", MB_OK);
					UpdateUIMsg("%s", buf);
					break;
				}
			}
			else
			{
				char* Imei1 = (char*)malloc(100 * sizeof(char));
				char* Imei2 = (char*)malloc(100 * sizeof(char));
				char* Meid1 = (char*)malloc(100 * sizeof(char));
				char* Meid2 = (char*)malloc(100 * sizeof(char));
				char* Msn = (char*)malloc(100 * sizeof(char));
				char* Psn = (char*)malloc(100 * sizeof(char));
				char* Bt = (char*)malloc(100 * sizeof(char));
				char* Wifi = (char*)malloc(100 * sizeof(char));
				char* attestationkey = (char*)malloc(100 * sizeof(char));
				char* keybox = (char*)malloc(100 * sizeof(char));
				char* row = (char*)malloc(100 * sizeof(char));
				char* readext = (char*)malloc(100 * sizeof(char));
				char* wallpaper_id = (char*)malloc(100 * sizeof(char));
				char* skuid = (char*)malloc(100 * sizeof(char));
				char* emmc = (char*)malloc(100 * sizeof(char));
				char* memory = (char*)malloc(100 * sizeof(char));
				char sw_version[1024] = { 0 };
				char* message = (char*)malloc(1024 * sizeof(char));
				memset(message,0,sizeof(message));

				bool ret2;

				bool ret = HTMesProxy::inst()->DB_LoadScx(buf);
				if (!ret)
				{
					HTMesProxy::inst()->DB_CloseDBConnect();
					UpdateUIMsg("%s", buf);
					break;
				}

				char DutInfo[256] = {0};
				sprintf_s(DutInfo, sizeof(DutInfo), "%s,ST02", g_sMetaComm.smesconnect.Imei);
				ret2 = HTMesProxy::inst()->DB_GetImei3(g_sMetaComm.smesconnect.Gdh,
							DutInfo,
							Imei1,Imei2,Meid1,Meid2,Msn,Psn,Bt,Wifi,
							attestationkey,keybox,row,readext,wallpaper_id,skuid,emmc,memory,sw_version,
							message);

				if(!ret2)
				{
					HTMesProxy::inst()->DB_CloseDBConnect();
					UpdateUIMsg("%s", message);
					MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::HTMesProxy::inst()->DB_GetImei3 message = %s", message);
					break;
				}
				else
				{
					strcpy(m_sScanData.strIMEI[0], Imei1);
					strcpy(g_sMetaComm.sScanData.strIMEI[0], Imei1);
					strcpy(g_sMetaComm.smesconnect.Imei, Imei1);
					strcpy(m_sScanData.strIMEI[1], Imei2);
					strcpy(g_sMetaComm.sScanData.strIMEI[1], Imei2);
					strcpy(g_sMetaComm.smesconnect.imei2, Imei2);
					strcpy(m_sScanData.strBTAddr, Bt);
					strcpy(g_sMetaComm.sScanData.strBTAddr, Bt);
					strcpy(g_sMetaComm.smesconnect.Bt, Bt);
					strcpy(m_sScanData.strWifiAddr, Wifi);
					strcpy(g_sMetaComm.sScanData.strWifiAddr, Wifi);
					strcpy(g_sMetaComm.smesconnect.Wifi, Wifi);
					strcpy(m_sScanData.strSerialNo, Msn);
					strcpy(g_sMetaComm.sScanData.strSerialNo, Msn);
					strcpy(g_sMetaComm.smesconnect.Msn, Msn);
					strcpy(m_sScanData.strMeid, Meid1);
					strcpy(g_sMetaComm.sScanData.strMeid, Meid1);
					strcpy(g_sMetaComm.smesconnect.Meid, Meid1);
					strcpy(g_sMetaComm.smesconnect.swversion, sw_version);
				}
				delete(Imei1);
				delete(Imei2);
				delete(Meid1);
				delete(Meid2);
				delete(Msn);
				delete(Psn);
				delete(Bt);
				delete(Wifi);
				delete(attestationkey);
				delete(keybox);
				delete(row);
				delete(readext);
				delete(wallpaper_id);
				delete(skuid);
				delete(emmc);
				delete(memory);
				delete(sw_version);
				delete(message);
			}
		}

        MTRACE(g_hEBOOT_DEBUG, "------------------------------------ START -------------------------------------");
        LogConfig();
        bAnyOperationFail = false;
        UpdateMainDlgUI(false, NORMAL);

		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::IMEI[0] = %s", m_sScanData.strIMEI[0]);
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::IMEI[1] = %s", m_sScanData.strIMEI[1]);
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::BT = %s", m_sScanData.strBTAddr);
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Wifi = %s", m_sScanData.strWifiAddr);
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SerialNo = %s", m_sScanData.strSerialNo);
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Meid = %s", m_sScanData.strMeid);

		// TrustKernel add
		if (!g_sMetaComm.bCheckSupportTEE) {
			g_pMainDlg->SetDlgItemText(IDC_COUNT_KEY, "Not  Write  TEE");
		}
		else {
			g_pMainDlg->SetDlgItemText(IDC_COUNT_KEY, " ");
		}
		// TrustKernel add end
        //SPInit() can`t call before SNThread_Init() function
        SNThread_Init();
        //MTRACE (g_hEBOOT_DEBUG, "m_bWriteModemNvram = %d,m_sMdInfo.number_of_md = %d", m_bWriteModemNvram, m_sMdInfo.number_of_md);
        SPInit();

        SetupMetaModeParameters();

        UpdateUIMsg("Start load and init AP database...");

        UpdateProgress(0.06);

        if (g_sMetaComm.bUsbEnable)
        {
            UpdateUIMsg("Wait for usb insert...");
        }

        EnableStartBTN(true);

        MetaResult = (META_RESULT)EnterAPMetaMode();
        if (MetaResult != META_SUCCESS)
        {
            bAnyOperationFail = true;
            goto End;
        }

        SetDutClock(m_hSPMetaHandle);


		//Customed by Trustkernel TEE start
		if (bNeedSupportTEE == true) {
			int r = 0;
            char module_path[MAX_PATH];
            char kph_base_path[256];

			char *log_path = NULL;
			char *in_dir = NULL;

			char errormsg[256];

			Licenser *licenser = NULL;
            GetModuleFileName(NULL, module_path, MAX_PATH);
            std::string::size_type pos = string(module_path).find_last_of("/\\");
            sprintf(kph_base_path, "%s\\tee_stuff", string(module_path).substr(0, pos).c_str());

            MTRACE(g_hEBOOT_DEBUG, "TEE stuff path: %s", kph_base_path);

            if ((r = KPHA_Init2(m_hSPMetaHandle, kph_base_path)) < 0) {
                bAnyOperationFail = true;
                UpdateUIMsg("TEE PL Failed with %d. \n Path: %s", r, kph_base_path);
                MTRACE(g_hEBOOT_DEBUG, "KPHA_Init failed\n");
                goto End;
			}
			{
				char *in_dir = NULL;
				Licenser *licenser = NULL;
				int cn = 0;
				if ((in_dir = new char[strlen(kph_base_path) + strlen("\\kph_in") + 1]) == NULL) {
						if (in_dir)
					delete[] in_dir;
				delete licenser;
				}

				sprintf(in_dir, "%s\\kph_in", kph_base_path);
				vector<string> vec;
				vec.push_back(string(in_dir) + "\\kph_env.ini");
				Config::setConfigFile(vec[0]);
				Config *cfg = Config::getConfig();
				if (cfg == NULL) {
					r = -1;
					sprintf_s(errormsg, "%s\\kph_env.ini: Invalid configuration", in_dir);
					goto err;
				}

				if (cfg->getEnableVTurkey()) {
				MTRACE(g_hEBOOT_DEBUG, "use VTurkeyLicenser");
					licenser = new VTurkeyLicenser(trustkernel::Network::getNetwork());
					if (licenser == NULL) {
					r = -1;
					sprintf_s(errormsg,  "Failed to init vTurkey licenser");
					goto err;
					}
					g_conter_key = licenser->getSecondaryLicenseLeftCount();
				}
				else {
					MTRACE(g_hEBOOT_DEBUG, "TurkeyLicenser::getTurkey()");
					Turkey *turkey = TurkeyLicenser::getTurkey();
					if (turkey == NULL) {
						sprintf_s(errormsg, "Failed to init Turkey");
						r = -1;
						goto err;
					}

					try {
						g_conter_key = turkey->GetLicense();
					} catch (...) {
						sprintf_s(errormsg, "Did you insert Turkey?");
						r = -1;
						goto err;
					}
				}
err:
				if (in_dir)
					delete[] in_dir;
				delete licenser;
			}
			UpdateProgress(0.08);
        }
	  //Customed by Trustkernel TEE end

        if (g_sMetaComm.sDBFileOption.bAPDBFromDUT || g_sMetaComm.sDBFileOption.bMDDBFromDUT)
        {
            SPMetaResult = QueryDBFromDUT();
            if (SPMetaResult != 0)
            {
                UpdateUIMsg("Don't support load db from device.");
                bAnyOperationFail = true;
                goto End;
            }
        }

        if (bNeedLoadAPDB == true)
        {
            // load ap db from dut
            if (!g_sMetaComm.sDBFileOption.bDBInitAP && g_sMetaComm.sDBFileOption.bAPDBFromDUT)
            {
                MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::GetAPDBFromDUT() : Get AP DB from DUT start...");
                SPMetaResult = GetAPDBFromDUT();
                if (SPMetaResult != META_SUCCESS)
                    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::GetMDDBFromDUT() : fail, will try load config DB.");
                else
                    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::GetAPDBFromDUT() : end.");
            }

            MetaResult = (META_RESULT)LoadAPDatabase();
            if (MetaResult != META_SUCCESS)
            {
                bAnyOperationFail = true;
                UpdateUIMsg("Load AP Database fail, %s", ResultToString_SP(MetaResult));
                goto End;
            }
        }

		for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
		{
        	SPMetaResult = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 500, "SP_META_ENCRYPTED_Support_r");
	        if (SPMetaResult == META_SUCCESS)
	        {
				break;
			}
		}
		if (SPMetaResult == META_SUCCESS)
		{
            if (QueryEncryptionSupport(&m_hSPMetaHandle))
            {
                Encryption Edlg;
                //Edlg.DoModal();
                while ((IDOK == Edlg.DoModal() ) && times < 3)
                {
                    if (!VeritifiEncryption(&m_hSPMetaHandle))
                    {
                        MTRACE(g_hEBOOT_DEBUG, "VeritifiEncryption() retry : %d", times + 1);
                        times = times + 1;
                    }
                    else
                        break;
                }
                if (3 == times)
                {
                    bAnyOperationFail = true;
                    MTRACE(g_hEBOOT_DEBUG, "VeritifiEncryption() retry times more than 3 times!");
                    goto End;
                }
            }
        }

		if(g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
		{
			UpdateUIMsg("Switch To Modem META");
			MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToDCModemMeta(): start...");
			Ret = APSwitchToDCModemMeta();
			if (Ret != META_SUCCESS)
			{
				UpdateUIMsg("Query modem information fail, %s", ResultToString_SP(Ret));
				goto End;
			}
			MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToDCModemMeta():  end.");
		}
		else
		{
	        if (g_sMetaComm.eTargetType != TABLET_WIFI_ONLY)
	        {
	            // query modem info must after queryEncryption.
	            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::GetSPModemInfo_Ex(): Query modem information start...");
	            Ret = GetSPModemInfo_Ex();
	            if (Ret != 0)
	            {
	                bAnyOperationFail = true;
	                MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::GetSPModemInfo_Ex(): Query modem information fail, MetaResult = %s", ResultToString_SP(Ret));
	                UpdateUIMsg("Query modem information fail, %s", ResultToString_SP(Ret));
	                goto End;
	            }
	            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::GetSPModemInfo_Ex(): Query modem information end.");
	        }

	        //Special case: m_bWriteModemNvram = true && m_sMdInfo.number_of_md = 0
	        //So skip to load modem database
	        MTRACE (g_hEBOOT_DEBUG, "m_bWriteModemNvram = %d,m_sMdInfo.number_of_md = %d", m_bWriteModemNvram, m_sMdInfo.number_of_md);
	        if ((m_bWriteModemNvram == true || g_sMetaComm.bCheckCalFlag || g_sMetaComm.bCheckFtFlag) && m_sMdInfo.number_of_md >= 1)
	        {
	            UpdateUIMsg("Start switch to MD meta and init Modem database...");

	            UpdateProgress(0.14);

	            if (!g_sMetaComm.sDBFileOption.bDBInitModem_1 && g_sMetaComm.sDBFileOption.bMDDBFromDUT)
	            {
	                MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::GetMDDBFromDUT() : Get Modem DB from DUT start...");
	                SPMetaResult = GetMDDBFromDUT();
	                if (SPMetaResult != META_SUCCESS)
	                    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::GetMDDBFromDUT() : fail, will try load config DB.");
	                else
	                    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::GetMDDBFromDUT() : end.");
	            }

	            UpdateProgress(0.18);

				MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::MDSLA_Connect():start...");
				MetaResult = MDSLA_Connect();
	            if (MetaResult != META_SUCCESS)
	            {
	                bAnyOperationFail = true;
	                MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::ApSwithToModemMeta_Ex() : Enable MODEM meta from AP meta fail.");
	                UpdateUIMsg("SmartPhoneSN::ApSwithToModemMeta_Ex() : Enable MODEM meta from AP meta fail.");
	                goto End;
	            }
				MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::MDSLA_Connect(): end.");

	            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadModemDatabase(): start...");
	            MetaResult = LoadModemDatabase(0);
	            if (MetaResult != META_SUCCESS)
	            {
	                bAnyOperationFail = true;
	                MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadModemDatabase() : MetaResult = %s", ResultToString(MetaResult));
	                UpdateUIMsg("SmartPhoneSN::LoadModemDatabase() : MetaResult = %s", ResultToString(MetaResult));
	                goto End;
	            }
	            MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::LoadModemDatabase(): end.");
	        }
		}

        UpdateProgress(0.22);

        EnableStartBTN(false);
        UpdateUIMsg("Start loop write data to nvram...");

        MetaResult = WriteNvramLoop();
        if (MetaResult != META_SUCCESS)
        {
            bAnyOperationFail = true;
            goto End;
        }

End:
        EnableStartBTN(false);
        if (m_bStopBeforeUSBInsert == true)
        {
            UpdateProgress(0.0);
            UpdateUIMsg("...");
        }
        else
        {
            if (m_eMetaMode == SP_AP_META || m_eMetaMode == SP_MODEM_META)
            {
				if(g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
				{
					ExitDCMetaMode(!bAnyOperationFail);
				}
				else if(g_sMetaComm.eTargetType == SMART_PHONE)
				{
					ExitMetaMode(!bAnyOperationFail);
				}
				else
				{
					ExitAPMetaMode();
				}
			}

            //The backup nvram api return success, but the operator pull up usb cable immediately before all operation successfully.
            //This case will cause nvram data lose when firmware upgrate, so that put indicator turn green after exit meta.
            if (g_sMetaComm.bCheckFastbootLock && m_bBackupNvramSuccess && !bAnyOperationFail)
            {
                if (REQ_FastbootLockOem() == -1)
                {
                    bAnyOperationFail = true;
                    UpdateUIMsg("ERROR!! REQ_FastbootLockOem()");
                }
            }

            if (bAnyOperationFail)
            {
                m_Process->SendMessage(PBM_SETBARCOLOR, 0, RGB(255, 0, 0));
                UpdateUICountResult(FAIL);
            }
            else
            {
                UpdateUIMsg("All Operate successfully!!");
                MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::All Operate successfully!!");

#ifdef _AUTO_GEN_FEATURE_
                if (g_AutoGenData.bEnableAutoGen)
                {
                    UpdateAutoGenParaToSetupFile();
                }
#endif
                UpdateUICountResult(PASS);
            }

            UpdateProgress(1.0);
        }

        if (g_sMetaComm.sWriteOption.bWriteDrm)
        {
            *m_pMetaStopFlag = BOOT_STOP;
        }
        MTRACE(g_hEBOOT_DEBUG, "------------------------------------ END --------------------------------------");
        DebugOnOff(false);
        Sleep(bSkipScanData ? 3000 : 1000);
    }

    if (g_sMetaComm.bCheckFastbootLock)
    {
        Adb_Manager_Deinit();
    }
    g_sMetaComm.sWriteOption.bWriteBarcode = bWriteBarcode;
    g_sMetaComm.sWriteOption.bWriteIMEI = bWriteIMEI;

    if (g_sMetaComm.bSwithTool)
        DisableUSBSwitch();

    MetaHandle_DeInit();
    UpdateMainDlgUI(true, CANCEL);
}


void SmartPhoneSN::ThreadMainEntryPointEx()
{
	int iTestCount = 0;
	bool bNeedScanData = false;
	bool bSkipScanData = false;
	bool bNeedLoadAPDB = false;
	int times = 0;
	int Ret = 0;
	unsigned int uiAPIRetry = 0;

	/*For dual talk(external modem) project
	* Due to dual talk project can`t switch back and forth between ap and external modem
	* When target type select tablet  and enable external modem option, mean that will write ap nvram, so need disable write external modem nvram
	* so it is mean(ex md + tablet to write bt wifi; ex md + tablet to write barcode imei;)
	*/
	bool bWriteBarcode = g_sMetaComm.sWriteOption.bWriteBarcode;
	bool bWriteIMEI = g_sMetaComm.sWriteOption.bWriteIMEI;

	if (g_sMetaComm.bDualTalk)
	{
		g_sMetaComm.sWriteOption.bWriteBarcode = false;
		g_sMetaComm.sWriteOption.bWriteIMEI = false;
	}

	bNeedScanData = NeedScanData();
	if (bNeedScanData == false)
	{
		if (!g_sMetaComm.sWriteOption.bWriteDrm &&
			!g_sMetaComm.sWriteOption.bWriteHdcp &&
			!g_sMetaComm.sWriteOption.bInstallHdcpData &&
			!g_sMetaComm.sWriteOption.bWriteAttestationKey)
		{
			PopupMsgBox("Warning", MB_OK | MB_ICONWARNING, "At least select one write option!!");
			return;
		}

		// Don't need to Popup scan data dialog
		bSkipScanData = true;
		if (g_sMetaComm.sWriteOption.bWriteHdcp)
		{
			// Write DRM Key & Install Hdcp data don't need to load ap database
			bNeedLoadAPDB = true;
		}
	}
	else
	{
		if (g_sMetaComm.sWriteOption.bWriteBarcode ||
			g_sMetaComm.sWriteOption.bWriteIMEI ||
			g_sMetaComm.sWriteOption.bWriteSIMEI ||
			g_sMetaComm.sWriteOption.bWriteBT ||
			g_sMetaComm.sWriteOption.bWriteWifi ||
			g_sMetaComm.sWriteOption.bWriteEthernetMac ||
			g_sMetaComm.sWriteOption.bWriteDrmkeyMCID ||
			g_sMetaComm.sWriteOption.bWriteSerialNo ||
			g_sMetaComm.sWriteOption.bWriteMeid ||
			g_sMetaComm.bClearMetaBootFlag)
		{
			bNeedLoadAPDB = true;
		}
	}
	if(g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
	{
		bNeedLoadAPDB = false;
	}

	bool bAnyOperationFail = false;
	META_RESULT MetaResult = META_SUCCESS;
	META_RESULT SPMetaResult = META_SUCCESS;

	if (g_sMetaComm.bSwithTool)
		EnableUSBSwitch();

	if (g_sMetaComm.bCheckFastbootLock)
	{
		UpdateUIMsg("Start Adb_Manager_Init() and Set_Fastboot_EnvironmentVariable()...");
		if (!Set_Fastboot_EnvironmentVariable())
		{
			MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable() FAIL...");
			UpdateUIMsg("Set Fastboot Environment Variable Fail!!");
			return;
		}
		UpdateUIMsg("Set Fastboot Environment Variable OK!!");
		if (!Adb_Manager_Init())
		{
			MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Manager_Init() FAIL...");

			Adb_Manager_Deinit();
			UpdateUIMsg("SmartPhoneSN::Adb_Manager_Init() FAIL...");
			return;
		}
		UpdateUIMsg("SmartPhoneSN::Adb_Manager_Init() OK...");
	}
	//m_hMauiMetaHandle = INVALID_META_HANDLE;
	//m_hSPMetaHandle = INVALID_META_HANDLE;
	m_hMauiMetaHandle = 0;
	m_hSPMetaHandle = 0;

	CScanData scanDlg;

	while(true)
	{
		if (*m_pMetaStopFlag == BOOT_STOP)
			break;
		if (g_sMetaComm.iStressTest > 1 && iTestCount > 0)
		{
			if (iTestCount >= g_sMetaComm.iStressTest)
				break;
		}
		else if (!bSkipScanData && scanDlg.DoModal() != IDOK)
			break;
		else if (bSkipScanData && iTestCount > 0 &&
			::AfxMessageBox(_T("Start write next devices ?"), MB_YESNO|MB_ICONQUESTION) != IDYES)
			break;
		iTestCount++;

		DebugOnOffEx(true);
		MTRACE(g_hEBOOT_DEBUG, "------------- SmartPhoneSN::ThreadMainEntryPointEx -------------");
		MTRACE(g_hEBOOT_DEBUG, "------------------------------------ START -------------------------------------");
		LogConfig();
		bAnyOperationFail = false;
		UpdateMainDlgUI(false, NORMAL);

		//SPInit() can`t call before SNThread_Init() function
		SNThread_Init();
		//MTRACE (g_hEBOOT_DEBUG, "m_bWriteModemNvram = %d,m_sMdInfo.number_of_md = %d", m_bWriteModemNvram, m_sMdInfo.number_of_md);
		SPInit();

		SetupMetaModeParameters();

		UpdateUIMsg("Start load and init AP database...");

		UpdateProgress(0.06);

		if (g_sMetaComm.bUsbEnable)
		{
			UpdateUIMsg("Wait for usb insert...");
		}

		EnableStartBTN(true);

		/* Connect AP META*/
		UpdateUIMsg("Boot META Start...");
		MetaResult = (META_RESULT)EnterAPMetaModeEx();
		if (MetaResult != META_SUCCESS)
		{
			UpdateUIMsg("Connect AP META Fail...");
			bAnyOperationFail = true;
			goto End;
		}
		UpdateUIMsg("Connect AP META End...");
		
		MetaResult = Encrypt_Check();
		if (Ret != META_SUCCESS)
		{
			UpdateUIMsg("Encrypt_Check fail");
			MTRACE(g_hEBOOT_DEBUG, "VeritifiEncryption() retry times more than 3 times!");
			goto End;
		}
		
		if(g_sMetaComm.eTargetType == TABLET_WIFI_ONLY)
		{
			m_eMetaMode = SP_AP_META;
			m_bTargetInMetaMode = true;
			m_bStopBeforeUSBInsert = false;
		}
		else
		{
			/* Connect Modem META*/
			UpdateUIMsg("Connect Modem META Start...");
			if(g_sMetaComm.eTargetType == SMART_PHONE)
			{
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToModemMeta():  start...");
				Ret = APSwitchToModemMeta();
				if (Ret != META_SUCCESS)
				{
					UpdateUIMsg("Connect Modem META fail, %s", ResultToString_SP(Ret));
					goto End;
				}
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToModemMeta():  end.");
			}
			else if(g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS
					|| (g_sMetaComm.eTargetType == DATA_CARD))
			{
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToDCModemMeta(): start...");
				Ret = APSwitchToDCModemMeta();
				if (Ret != META_SUCCESS)
				{
					UpdateUIMsg("Connect Modem META fail, %s", ResultToString_SP(Ret));
					goto End;
				}
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToDCModemMeta():  end.");
			}
			else
			{
			}
			MetaResult = SLA_Check();
			UpdateUIMsg("Connect Modem META End...");
		}
		
		UpdateProgress(0.22);

		EnableStartBTN(false);
		UpdateUIMsg("Start loop write data to nvram...");
		MetaResult = WriteNvramLoop();
		if (MetaResult != META_SUCCESS)
		{
			bAnyOperationFail = true;
			goto End;
		}

End:
		EnableStartBTN(false);
		if (m_bStopBeforeUSBInsert == true)
		{
			UpdateProgress(0.0);
			UpdateUIMsg("...");
		}
		else
		{
			UpdateUIMsg("Exit META Start...");
			if(g_sMetaComm.eTargetType == SMART_PHONE
				|| (g_sMetaComm.eTargetType == TABLET_WIFI_ONLY))
			{
				RunClearSlaStatus();
				ExitMetaMode(!bAnyOperationFail);
			}
			else if((g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
					|| (g_sMetaComm.eTargetType == DATA_CARD))
			{
				RunClearSlaStatus();
				ExitDCMetaMode(!bAnyOperationFail);
			}
			else
			{

			}
			UpdateUIMsg("Exit META End...");

			//The backup nvram api return success, but the operator pull up usb cable immediately before all operation successfully.
			//This case will cause nvram data lose when firmware upgrate, so that put indicator turn green after exit meta.
			if (g_sMetaComm.bCheckFastbootLock && m_bBackupNvramSuccess && !bAnyOperationFail)
			{
				if (REQ_FastbootLockOem() == -1)
				{
					bAnyOperationFail = true;
					UpdateUIMsg("ERROR!! REQ_FastbootLockOem()");
				}
			}

			if (bAnyOperationFail)
			{
				m_Process->SendMessage(PBM_SETBARCOLOR, 0, RGB(255, 0, 0));
				UpdateUICountResult(FAIL);
			}
			else
			{
				UpdateUIMsg("All Operate successfully!!");
				MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::All Operate successfully!!");

#ifdef _AUTO_GEN_FEATURE_
				if (g_AutoGenData.bEnableAutoGen)
				{
					UpdateAutoGenParaToSetupFile();
				}
#endif
				UpdateUICountResult(PASS);
			}

			UpdateProgress(1.0);
		}

		if (g_sMetaComm.sWriteOption.bWriteDrm)
		{
			*m_pMetaStopFlag = BOOT_STOP;
		}
		MTRACE(g_hEBOOT_DEBUG, "------------------------------------ END --------------------------------------");
		DebugOnOffEx(false);
		Sleep(bSkipScanData ? 3000 : 1000);
	}

	if (g_sMetaComm.bCheckFastbootLock)
	{
		Adb_Manager_Deinit();
	}
	g_sMetaComm.sWriteOption.bWriteBarcode = bWriteBarcode;
	g_sMetaComm.sWriteOption.bWriteIMEI = bWriteIMEI;

	if (g_sMetaComm.bSwithTool)
		DisableUSBSwitch();

	MetaHandle_DeInit();
	UpdateMainDlgUI(true, CANCEL);
}

bool SmartPhoneSN::Adb_Manager_Init()
{
    HANDLE hread_cmd = NULL, hwrite_out = NULL;
    SECURITY_ATTRIBUTES sa;
    STARTUPINFO si;
    PROCESS_INFORMATION pi;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.lpSecurityDescriptor = NULL;
    sa.bInheritHandle = TRUE;

    if (!CreatePipe(&hread_cmd, &hdle_write_cmd, &sa, 0))
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Manager_Init() : CreatePipe() write fail...");
        return FALSE;
    }
    //MTRACE (g_hEBOOT_DEBUG, "Adb_Manager::Adb_Manager_Init(): createpipe1 success!");
    if (!CreatePipe(&hdle_read_out, &hwrite_out, &sa, 0))
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Manager_Init() : CreatePipe() read fail...");
        return FALSE;
    }
    //MTRACE (g_hEBOOT_DEBUG, "Adb_Manager::Adb_Manager_Init(): createpipe2 success!");

    ZeroMemory(&si, sizeof(STARTUPINFO));
    si.cb = sizeof(STARTUPINFO);
    GetStartupInfo(&si);
    si.hStdError = hwrite_out;
    si.hStdOutput = hwrite_out;
    si.hStdInput = hread_cmd;
    si.wShowWindow = SW_HIDE;
    si.dwFlags = STARTF_USESHOWWINDOW | STARTF_USESTDHANDLES;

    if (!CreateProcess("c:\\windows\\system32\\cmd.exe", NULL, NULL, NULL, TRUE, NULL, NULL, NULL, &si, &pi))
    {
        //MTRACE_ERR (g_hEBOOT_DEBUG, "Adb_Manager::Adb_Manager_Init(): create cmd.exe process fail!");
        return FALSE;
    }
    //MTRACE (g_hEBOOT_DEBUG, "Adb_Manager::Adb_Manager_Init(): create cmd.exe process success!");

    CloseHandle(hread_cmd);
    CloseHandle(hwrite_out);
    CloseHandle(pi.hThread);
    CloseHandle(pi.hProcess);
    //MTRACE (g_hEBOOT_DEBUG, "Adb_Manager::Adb_Manager_Init(): hdle_write_cmd = %08X, hdle_read_out = %08X",hdle_write_cmd,hdle_read_out);
    return TRUE;
}

int SmartPhoneSN::Adb_Manager_Write(char *cmd_line, int cmd_length)
{
    char cmd_buf[100];
    memset(cmd_buf, 0, sizeof(cmd_buf));
    memcpy(cmd_buf, cmd_line, cmd_length);

    cmd_buf[cmd_length] = '\r';
    cmd_buf[cmd_length + 1] = '\n';
    DWORD bytesWritten = 0;

    if (!WriteFile(hdle_write_cmd, cmd_buf, cmd_length + 2, &bytesWritten, NULL))
    {
        return 0;
    }

    return (int)(bytesWritten);
}

int SmartPhoneSN::Adb_Manager_Read(ADB_READ_BUF *adb_read_buf, int buf_size)
{
    DWORD bytesRead = 0;

    if (!ReadFile(hdle_read_out, adb_read_buf->rd_buf, buf_size, &bytesRead, NULL))
    {
        return 0;
    }
    adb_read_buf->rd_buf[bytesRead] = '\0';

    return (int)bytesRead;
}



bool SmartPhoneSN::Adb_WR_Cmd(char *cmd_line, int cmd_length)
{

    if (!(Adb_Manager_Write(cmd_line, cmd_length)))
    {
        return FALSE;
    }
    Sleep(800);
    memset(&read_buf, 0, r_bufsize);
    if (!(Adb_Manager_Read(&read_buf, r_bufsize)))
    {
        return FALSE;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_WR_Cmd(): read_buf is = %s", read_buf.rd_buf);
    return TRUE;
}

int SmartPhoneSN::Adb_Manager_Deinit()
{

    //Adb_WR_Cmd("exit",strlen("exit"));
    Adb_WR_Cmd("adb kill-server", strlen("adb kill-server"));
    //  MTRACE(g_hEBOOT_DEBUG, "ATB_MR::Scan_Devices_FunProc(): close cmd.exe");
    memset(&read_buf, 0, r_bufsize);
    CloseHandle(hdle_write_cmd);
    //  MTRACE(g_hEBOOT_DEBUG, "Adb_Manager::~Adb_Manager(): close hdle_write_cmd");
    CloseHandle(hdle_read_out);
    //  MTRACE(g_hEBOOT_DEBUG, "Adb_Manager::~Adb_Manager(): close hdle_read_out");
    return 0;
}
int SmartPhoneSN::REQ_FastbootLockOem()
{
    int timer = 0;
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : Fastboot Lock OEM start....");
    UpdateUIMsg("Start Lock OEM...");

    int bootResult = 0;
    SetupMetaModeParameters();
    m_stModeArg.m_euBootMode = SP_FAST_BOOT;
    UpdateUIMsg("Start searching preloader com port and handshake...");

    bootResult = ConnectWithPreloader();
    if (bootResult != 0)
    {
        UpdateUIMsg("Searching preloader com port and handshake Fail!!");
        return -1;
    }
    else
    {
        UpdateUIMsg("Searching preloader com port and handshake OK!!");

        UpdateUIMsg("Start searching fastboot devices...");

        // MTRACE (g_hEBOOT_DEBUG, "ATB_MR::ATB_MR_Init(): Adb_Manager_Init success!");
        if (!Adb_WR_Cmd("adb start-server", strlen("adb start-server")))
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : adb start-server FAIL...");
            Adb_Manager_Deinit();
            return -1;
        }

        if (Adb_Detect_Devact() > 0)
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : Adb_Detect_Devact() OK...");
            UpdateUIMsg("Find a fastboot devices OK...");
        }
        else
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : Adb_Detect_Devact() timeout...");
            Adb_Manager_Deinit();
            return -1;
        }

        //if(!Adb_WR_Cmd("fastboot oem lock",strlen("fastboot oem lock")))
        if (!Fastboot_Lock_Oem())
        {
            MTRACE_ERR (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : fastboot oem lock fail...");
            Adb_Manager_Deinit();
            return -1;
        }
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : fastboot oem lock ok...");
        UpdateUIMsg("fastboot oem lock OK...");
        return 0;
    }
}

int SmartPhoneSN::REQ_FastbootUnLockOem()
{
    int timer = 0;
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : Fastboot Lock OEM start....");
    UpdateUIMsg("Start Lock OEM...");

    int bootResult = 0;
    SetupMetaModeParameters();
    m_stModeArg.m_euBootMode = SP_FAST_BOOT;
    UpdateUIMsg("Start searching preloader com port and handshake...");
    bootResult = ConnectWithPreloader();
    if (bootResult != 0)
    {
        UpdateUIMsg("Searching preloader com port and handshake Fail!!");
        return -1;
    }
    else
    {
        UpdateUIMsg("Searching preloader com port and handshake OK!!");

        UpdateUIMsg("Start searching ADB devices...");
        if (!Adb_Manager_Init())
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : Adb_Manager_Init() OK...");
            Adb_Manager_Deinit();
            return -1;
        }
        //  MTRACE (g_hEBOOT_DEBUG, "ATB_MR::ATB_MR_Init(): Adb_Manager_Init success!");
        if (!Adb_WR_Cmd("adb start-server", strlen("adb start-server")))
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : Adb_Manager_Init() OK...");
            Adb_Manager_Deinit();
            return -1;
        }
        while (1)
        {
            timer++;
            if (Adb_Detect_Devact() > 0)
            {
                MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : Adb_Detect_Devact() OK...");
                break;
            }
            else if (timer > 60)
            {
                MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : Adb_Detect_Devact() timeout...");
                Adb_Manager_Deinit();
                return -1;
            }
        }
        if (!Adb_WR_Cmd("fastboot oem unlock", strlen("fastboot oem unlock")))
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : fastboot oem lock fail...");
            Adb_Manager_Deinit();
            return -1;
        }
        if (!Adb_WR_Cmd("fastboot reboot", strlen("fastboot reboot")))
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_FastbootLockOem() : fastboot oem lock fail...");
            Adb_Manager_Deinit();
            return -1;
        }
        Adb_Manager_Deinit();
        return 0;
    }
}
#if 0
int SmartPhoneSN::Adb_Detect_Devact()
{
    if (!Adb_WR_Cmd("fastboot devices", strlen("fastboot devices")))
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Detect_Devact(): Adb_WR_Cmd() fastboot devices fail!");
        return 0;
    }

    // MTRACE (g_hEBOOT_DEBUG, "Adb_Manager::Adb_Detect_Devact(): send adb devices success!");
    // MTRACE (g_hEBOOT_DEBUG, "Adb_Manager::Adb_Detect_Devact(): %s",read_buf.rd_buf);

    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Detect_Devact(): read_buf is = %s", read_buf.rd_buf);
    int r_leg = strlen(read_buf.rd_buf);
    char *f_str2 = "fastboot";
    int str2_leg = strlen("fastboot");
    if (strstr(read_buf.rd_buf, f_str2) != NULL)
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Detect_Devact(): find device ok....");
        return 1;
    }
    return 0;
}

int SmartPhoneSN::Fastboot_Lock_Oem()
{
    if (!Adb_WR_Cmd("fastboot oem lock", strlen("fastboot oem lock")))
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Detect_Devact(): Adb_WR_Cmd() fastboot devices fail!");
        return 0;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Fastboot_Lock_Oem(): read_buf is = %s", read_buf.rd_buf);
    int r_leg = strlen(read_buf.rd_buf);
    char *f_str2 = "OKAY";
    int str2_leg = strlen("OKAY");
    if (strstr(read_buf.rd_buf, f_str2) != NULL)
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Fastboot_Lock_Oem(): find OKAY");
        return 1;
    }
    return 0;
}

#else
int SmartPhoneSN::Adb_Detect_Devact()
{
    for (int i = 0; i < 10; i++)
    {
        if (!(Adb_Manager_Write("fastboot devices", strlen("fastboot devices"))))
        {
            return 0;
        }
        Sleep(800);
        memset(&read_buf, 0, r_bufsize);
        if (!(Adb_Manager_Read(&read_buf, r_bufsize)))
        {
            return 0;
        }
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Detect_Devact(): read_buf is = %s", read_buf.rd_buf);
        int r_leg = strlen(read_buf.rd_buf);
        char *f_str1 = strstr(read_buf.rd_buf, "devices");
        if (f_str1 != NULL)
        {
            if (strstr(f_str1, "fastboot") != NULL)
            {
                MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Adb_Detect_Devact(): find device ok....");
                return 1;
            }
        }
    }
    return 0;

}
int SmartPhoneSN::Fastboot_Lock_Oem()
{
    //if(!Adb_WR_Cmd("fastboot oem lock",strlen("fastboot oem lock")))
    if (!(Adb_Manager_Write("fastboot oem lock", strlen("fastboot oem lock"))))
    {
        return 0;
    }
    for (int i = 0; i < 3; i++)
    {
        memset(&read_buf, 0, r_bufsize);
        if (!(Adb_Manager_Read(&read_buf, r_bufsize)))
        {
            return 0;
        }
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Fastboot_Lock_Oem(): read_buf is = %s", read_buf.rd_buf);
        int r_leg = strlen(read_buf.rd_buf);
        char *f_str2 = "OKAY";
        int str2_leg = strlen("OKAY");
        if (strstr(read_buf.rd_buf, f_str2) != NULL)
        {
            MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Fastboot_Lock_Oem(): fastboot oem lock ok....");
            return 1;
        }
        Sleep(1500);
    }
    return 0;
}

int SmartPhoneSN::Set_Fastboot_EnvironmentVariable(void)
{
    char chBuf[0x8000] = {0};
    DWORD dwSize = GetEnvironmentVariable("path", chBuf, 0x10000);
    if (dwSize == 0)
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable(): GetEnvironmentVariable() fail....");
        return 0;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable(): GetEnvironmentVariable() chBuf is: %s....", chBuf);

    CString strEnvPaths(chBuf);

    // find current path
    if (!GetModuleFileName(NULL, chBuf, MAX_PATH))
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable(): GetModuleFileName() fail....");
        return 0;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable(): GetModuleFileName() chBuf is: %s...", chBuf);

    CString strAppPath(chBuf);
    const int nPos = strAppPath.ReverseFind(_T('\\'));
    if (nPos > 0)
    {
        // add path backslash
        strAppPath = strAppPath.Mid(0, nPos + 1);
    }
    //add fastboot path
    strEnvPaths.TrimRight(";");
    strEnvPaths += ";" + strAppPath + "Android;";
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable(): SetEnvironmentVariable() strEnvPaths is: %s....", strEnvPaths);

    dwSize = SetEnvironmentVariable("path", strEnvPaths);
    if (dwSize == 0)
    {
        MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable(): SetEnvironmentVariable() fail....");
        return 0;
    }
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Set_Fastboot_EnvironmentVariable(): SetEnvironmentVariable() strEnvPaths is: %s....", strEnvPaths);
    return 1;
}

#endif

// C2K
META_RESULT SmartPhoneSN::EnterC2KGen90()
{
    META_RESULT ret_last = META_SUCCESS;
    META_RESULT MetaResult = META_SUCCESS;

    bool bInited = false;
    bool bSwitchMd = false;
    bool bConnected = false;
    char szLogFilePath[MAX_PATH] = "";
    C2K_LIBCONFIGPARMS c2kConfig;
    PHONEATTRIBUTE_PARMS phoneAttr;
    C2K_CONNECT_PARAMS connectReq;
    char szPath[s_MAX_PATH] = {0};

    int preMdChannelIdx = m_sMdInfo.active_md_idx;
    int c2kMdChannelIdx = -1;
    unsigned int c2k_protocol = META_MODEM_SRV_ETS;
    unsigned int c2k_ch_type = META_MODEM_CH_TUNNELING;

    UpdateUIMsg("Start init C2K Meta...");


    // get c2k modem index and channel protocol + type
    for (int i = 0; i < (int)m_sMdInfo.number_of_md + 2; i++)
    {
        MTRACE(g_hEBOOT_DEBUG, "[C2K] i= %d, protocol = %d, channel_type=%d",
            i, m_SpMdCapList.modem_cap[i].md_service, m_SpMdCapList.modem_cap[i].ch_type);

        if (m_SpMdCapList.modem_cap[i].md_service == META_MODEM_SRV_ETS)
        {
            c2kMdChannelIdx = i;
            c2k_protocol = m_SpMdCapList.modem_cap[i].md_service;
            c2k_ch_type = m_SpMdCapList.modem_cap[i].ch_type;
            break;
        }
    }
    if (c2kMdChannelIdx == -1)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] can not find C2K modem.");
        return META_FAILED;
    }
    UpdateProgress(0.14);


    // init c2k
    sprintf_s(szLogFilePath, "%sETS_LOG.txt", m_strLogDir_Sub);
    c2kConfig.bLog = true;
    c2kConfig.bScript = false;
    c2kConfig.szLogPath = szLogFilePath;
    c2kConfig.szScriptPath = "";
    memset(&phoneAttr, 0, sizeof(PHONEATTRIBUTE_PARMS));
    phoneAttr.eAfcControlMode = C2K_AFC_TCXO;
    phoneAttr.eRfMode = C2K_RF_MODE_EVDO;
    phoneAttr.eRxCalibrationType = C2K_RX_CAL_MAIN;
    phoneAttr.eSidbAccessMode = C2K_SIDB_FSI;
    phoneAttr.bWriteSIDBFlag = false;       //if write calibration flag?
    phoneAttr.uPllSettleTime = 100;
    phoneAttr.uRxAGCGainTableSelectSettleTime = 200;
    phoneAttr.uRxCtrlSettleTime = 200;
    phoneAttr.uRxGainStateSettleTime = 200;
    phoneAttr.uTxAGCConfigSettleTime = 50;
    phoneAttr.uTxCtrlSettleTime = 50;
    phoneAttr.uTxTestRateSettleTime = 100;
    phoneAttr.uEnableRxTxSpySettleTime = 0;
    phoneAttr.bAFCSlopeStepPerPpmWithQ6 = 0;

    MTRACE (g_hEBOOT_DEBUG, "[C2K] META_C2K_Init() start...");
    MetaResult = META_C2K_Init(m_hMauiMetaHandle, &c2kConfig, &phoneAttr);
    if (MetaResult != META_SUCCESS)
    {
        MTRACE_ERR (g_hEBOOT_DEBUG, "[C2K] META_C2K_Init() fail");
        UpdateUIMsg("META_C2K_Init() fail");
        return MetaResult;
    }
    bInited = true; // inited
    MTRACE (g_hEBOOT_DEBUG, "[C2K] META_C2K_Init() end...");
    UpdateProgress(0.18);


    // connect to c2k
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Connect C2K USB port.");
    if (c2k_ch_type == META_MODEM_CH_TUNNELING)
    {
        MTRACE(g_hEBOOT_DEBUG, "[C2K] Switch to C2K modem start...");
        if (c2kMdChannelIdx != preMdChannelIdx)
        {
            MTRACE(g_hEBOOT_DEBUG, "[C2K] META_DLL::META_SwitchCurrentModemEx_r() : Switch to C2K modem start...");
            MetaResult = META_SwitchCurrentModemEx_r(m_hMauiMetaHandle, 10000, c2kMdChannelIdx, c2k_protocol, c2k_ch_type, NULL, NULL);
            if (MetaResult != META_SUCCESS)
            {
                MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_DLL::META_SwitchCurrentModemEx_r() : fail, %s", ResultToString(MetaResult));
                goto end;
            }
            bSwitchMd = true;
            MTRACE(g_hEBOOT_DEBUG, "[C2K] META_DLL::META_SwitchCurrentModemEx_r() : ok.");
        }
        MTRACE(g_hEBOOT_DEBUG, "[C2K] Switch to C2K modem end.");

        MTRACE(g_hEBOOT_DEBUG, "[C2K] us_com_port = %d", c2kMdChannelIdx);
        connectReq.eComType = C2K_COM_VC;
        connectReq.uComNum = c2kMdChannelIdx;
    }
    else
    {
        connectReq.eComType = C2K_COM_USB;
        connectReq.uComNum = 0;

        GetModuleFileName(NULL, szPath, s_MAX_PATH);
        (_tcsrchr(szPath, _T('\\')))[1] = 0;
        strcat_s(szPath, "EtsMsg.txt");

        MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_LoadMessage_r() : start...");
        MetaResult = META_C2K_LoadMessage_r(m_hMauiMetaHandle, szPath);
        if (MetaResult != META_SUCCESS)
        {
            UpdateUIMsg("META_C2K_LoadMessage_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_LoadMessage_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            UpdateProgress(1.00);
            goto end;
        }
        UpdateUIMsg("META_C2K_LoadMessage_r() : successful!");
        MTRACE (g_hEBOOT_DEBUG, "[C2K] META_C2K_LoadMessage_r() : successful!");
        UpdateProgress(0.28);
    }

    UpdateUIMsg("Start connect with C2K Meta...");
    UpdateProgress(0.34);

    MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_ConnectWithTarget_r() start...");
    for (int i = 0; i < 20; i++)
    {
        MetaResult = META_C2K_ConnectWithTarget_r(m_hMauiMetaHandle, 2000, &connectReq);
        if (MetaResult == META_SUCCESS)
            break;
    }
    if (MetaResult != META_SUCCESS)
    {
        UpdateUIMsg("META_C2K_ConnectWithTarget_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_ConnectWithTarget_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        UpdateProgress(1.00);
        goto end;
    }
    bConnected = true; // connected
    UpdateUIMsg("META_C2K_ConnectWithTarget_r() : successful!");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_ConnectWithTarget_r() : successful!");
    UpdateProgress(0.48);


    // handshake with c2k
    for (int i = 0; i < 10; i++)
    {
        MetaResult = META_C2K_LoopbackTest_r(m_hMauiMetaHandle, 2000, i + 1);
        if (MetaResult == META_SUCCESS)
            break;
    }
    if (MetaResult != META_SUCCESS )
    {
        UpdateUIMsg("META_C2K_LoopbackTest_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_LoopbackTest_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        UpdateProgress(1.00);
        goto end;
    }
    UpdateProgress(0.60);


    // task
    if (g_sMetaComm.sWriteOption.bWriteMeid)
        ret_last = (META_RESULT)WriteMEID90();
    else if (g_sMetaComm.sWriteOption.bWriteEsn)
        ret_last = (META_RESULT)WriteESN90();

    UpdateProgress(0.85);


end:
    // disconnect
    if (bConnected)
    {
        MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_DisconnectWithTarget_r start...");
        MetaResult = META_C2K_DisconnectWithTarget_r(m_hMauiMetaHandle);
        if (MetaResult != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_DisconnectWithTarget_r : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            UpdateUIMsg("META_C2K_DisconnectWithTarget_r : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            return MetaResult;
        }
        MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_DisconnectWithTarget_r end.");
    }

    // deinit
    if (bInited)
    {
        MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_DeInit start...");
        MetaResult = META_C2K_DeInit(m_hMauiMetaHandle);
        if (MetaResult != META_SUCCESS)
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_DeInit : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            UpdateUIMsg("META_C2K_DeInit : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            return MetaResult;
        }
        MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_DeInit end!");
    }

    if (bSwitchMd)
    {
        MTRACE(g_hEBOOT_DEBUG, "[C2K] switch to 234G modem start!");
        MetaResult = META_SwitchCurrentModemEx_r(m_hMauiMetaHandle, 10000, 0, m_SpMdCapList.modem_cap[0].md_service,
        m_SpMdCapList.modem_cap[0].ch_type, NULL, NULL);
        if (MetaResult == META_SUCCESS)
        {
            MTRACE(g_hEBOOT_DEBUG, "[C2K] META_SwitchCurrentModemEx_r(): [C2K] switch to 234G modem successful!");
            UpdateUIMsg("META_SwitchCurrentModemEx_r(): [C2K] switch to 234G modem successful!");
        }
        else if (MetaResult == META_MAUI_DB_INCONSISTENT)
        {
            MetaResult = META_SUCCESS;
            MTRACE(g_hEBOOT_DEBUG, "[C2K] META_SwitchCurrentModemEx_r(): [C2K] switch successful, md db inconsistent, directly ignore.");
        }
        else
        {
            MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_SwitchCurrentModemEx_r(): [C2K] switch to 234G modem fail!");
            UpdateUIMsg("META_SwitchCurrentModemEx_r(): [C2K] switch to 234G modem fail!");
            return MetaResult;
        }
        MTRACE(g_hEBOOT_DEBUG, "[C2K] switch to 234G modem end!");
    }

    if (ret_last != META_SUCCESS)
    {
        if (g_sMetaComm.sWriteOption.bWriteMeid)
            UpdateUIMsg("[C2K] Write MEID fail.");
        else if (g_sMetaComm.sWriteOption.bWriteEsn)
            UpdateUIMsg("[C2K] Write ESN fail.");
        UpdateProgress(1.00);
    }

    return ret_last;
}

int SmartPhoneSN::EnterC2KGen93()
{
    int ret_i = 0;
    META_RESULT ret_meta = META_SUCCESS;

    MMRfTestCmdRfCapabilityReq mreq_rfcap;
    MMRfTestCmdRfCapabilityCnf mcnf_rfcap;

    UpdateUIMsg("Start enter C2K...");

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] Query if support query c2k...");
    ret_meta = META_QueryIfFunctionSupportedByTarget_r(m_hMauiMetaHandle, 30000, "META_MMRf_GetRfCapability_r");
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Don't support query C2K, may be not support C2K.");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] Query if support api META_MMRf_GetRfCapability_r fail. %s.", ResultToString(ret_meta));
        return 1;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] Query if support c2k...");
    memset(&mreq_rfcap, 0, sizeof(mreq_rfcap));
    mreq_rfcap.capabilityItemsSize = sizeof(MMRfCapabilityItemSet);
    mreq_rfcap.calibrationItemsSize = sizeof(MMRfCalibrationItemSet);
    memset(&mcnf_rfcap, 0, sizeof(mcnf_rfcap));
    ret_meta = META_MMRf_GetRfCapability_r(m_hMauiMetaHandle, 30000, &mreq_rfcap, sizeof(mreq_rfcap), &mcnf_rfcap, sizeof(mcnf_rfcap));
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Query C2K info fail, may be not support C2K.");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMRf_GetRfCapability_r fail. %s.", ResultToString(ret_meta));
        return 2;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] calibrationItems.ratmap_support_WM: is_capable(%d), parameter(%d).",
        mcnf_rfcap.calibrationItems.ratmap_support_WM.is_capable,
        mcnf_rfcap.calibrationItems.ratmap_support_WM.parameter);

    if (!mcnf_rfcap.calibrationItems.ratmap_support_WM.is_capable ||
        (mcnf_rfcap.calibrationItems.ratmap_support_WM.parameter & 0x04) == 0)
    {
        UpdateUIMsg("Don't suport C2K.");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] Don't support c2k.", ResultToString(ret_meta));
        return -1;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_RegisterHandler_r start...");
    ret_meta = META_MMC2K_RegisterHandler_r(m_hMauiMetaHandle);
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Register C2K fail.");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_RegisterHandler_r fail. %s.", ResultToString(ret_meta));
        return 3;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_SetTargetGenType_r start...");
    ret_meta = META_MMC2K_SetTargetGenType_r(m_hMauiMetaHandle, 1);
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Set C2K info fail.");
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_SetTargetGenType_r fail. %s.", ResultToString(ret_meta));
        return 4;
    }
    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_SetTargetGenType_r end.");

	C2kMsCapability capabilityV3;
	memset(&capabilityV3, 0, sizeof(capabilityV3));
	ret_meta = META_MMC2K_QueryTargetCapability_r(m_hMauiMetaHandle, 50000, &capabilityV3);
	if (ret_meta != META_SUCCESS)
	{
		UpdateUIMsg("Query C2K fail.");
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_QueryTargetCapability_r fail. %s.", ResultToString(ret_meta));
		return 3;
	}
    UpdateUIMsg("Enter C2K success, start to write mied/esn...");

    // task
    if (g_sMetaComm.sWriteOption.bWriteMeid)
	{
		if((capabilityV3.rxCalCW & 0x20) == 0x20)
		{
			//ret_i = REQ_WriteModem_NVRAM_Start(WRITE_MEID, m_sScanData.strMeid, 1);
			ret_i = WriteMEIDToFTNVRAM();
		}
		else
		{
			ret_i = WriteMEID93();
		}
	}
    else if (g_sMetaComm.sWriteOption.bWriteEsn)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterC2KGen93(): [C2K] MT6293 don't support ESN writing.");
        UpdateUIMsg("[C2K] MT6293 don't support ESN writing!");
        ret_i = META_FAILED;
    }
    if (ret_i != 0)
        return 5;

    return 0;
}

int SmartPhoneSN::WriteMEID90()
{
    META_RESULT ret_meta = META_SUCCESS;
    MEID_TABLE data_meid;

    UpdateUIMsg("Start to Write MEID (>=MT6290)...");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_WriteNvram_r() : Write MEID start...");
    memset(&data_meid, 0, sizeof(data_meid));
    data_meid.idType = 0;
    strcpy_s(data_meid.meid, g_sMetaComm.sScanData.strMeid);
    for (int i = 0; i < 20; i++)
    {
        ret_meta = META_C2K_WriteNvram_r(m_hMauiMetaHandle, 3000, 0, C2K_DB_MEID, &data_meid, sizeof(data_meid));
        if (ret_meta == META_SUCCESS)
            break;
    }
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("META_C2K_WriteNvram_r() : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_WriteNvram_r() : Write MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }
    MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_WriteNvram_r() : Write MEID end.");
    UpdateProgress(0.70);


    UpdateUIMsg("Read MEID for check start...");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Read MEID for check start...");
    memset(&data_meid, 0, sizeof(data_meid));
    data_meid.idType = 0;
    ret_meta = META_C2K_ReadNvram_r(m_hMauiMetaHandle, 3000, 0, C2K_DB_MEID, &data_meid, sizeof(data_meid));
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("META_C2K_ReadNvram_r() : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_ReadNvram_r(): Read MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }
    UpdateProgress(0.75);

    MTRACE(g_hEBOOT_DEBUG, "[C2K] Write MEID :%s", g_sMetaComm.sScanData.strMeid);
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Read MEID :%s", data_meid.meid);
    if (_stricmp(data_meid.meid, g_sMetaComm.sScanData.strMeid) != 0)
    {
        UpdateUIMsg("[C2K] Checking MEID fail!");
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] Checking MEID fail!");
        return META_FAILED;
    }
    UpdateUIMsg("Checking MEID succesfully!");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Checking MEID succesfully!");
    UpdateProgress(0.80);

    return META_SUCCESS;
}

int SmartPhoneSN::WriteESN90()
{
    META_RESULT ret_meta = META_SUCCESS;
    MEID_TABLE data_meid;

    UpdateUIMsg("Start to Write ESN (>=MT6290)...");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_WriteNvram_r() : Write ESN start...");
    memset(&data_meid, 0, sizeof(data_meid));
    data_meid.idType = 1;
    strcpy_s(data_meid.esn, g_sMetaComm.sScanData.strEsn);
    for (int i = 0; i < 20; i++)
    {
        ret_meta = META_C2K_WriteNvram_r(m_hMauiMetaHandle, 3000, 0, C2K_DB_MEID, &data_meid, sizeof(data_meid));
        if (ret_meta == META_SUCCESS)
            break;
    }
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("META_C2K_WriteNvram_r() : Write ESN fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_WriteNvram_r() : Write ESN fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }
    UpdateUIMsg("META_C2K_WriteNvram_r() : Write ESN succesful!");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] META_C2K_WriteNvram_r() : Write ESN succesful!");
    UpdateProgress(0.70);

    UpdateUIMsg("Read ESN for check start...");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Read ESN for check start...");
    memset(&data_meid, 0, sizeof(data_meid));
    data_meid.idType = 1;
    ret_meta = META_C2K_ReadNvram_r(m_hMauiMetaHandle, 3000, 0, C2K_DB_MEID, &data_meid, sizeof(data_meid));
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("META_C2K_ReadNvram_r() : Read ESN fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_C2K_ReadNvram_r() : Read ESN fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }

    MTRACE(g_hEBOOT_DEBUG, "[C2K] Write ESN :%s", g_sMetaComm.sScanData.strMeid);
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Read ESN :%s", data_meid.esn);
    if (_stricmp(data_meid.esn, g_sMetaComm.sScanData.strEsn) != 0)
    {
        UpdateUIMsg("Checking ESN Fail!");
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] Checking ESN Fail!");
        return ret_meta;
    }
    UpdateUIMsg("Checking Esn Successfully!");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Checking ESN successfully!");
    UpdateProgress(0.80);

    return META_SUCCESS;
}

int SmartPhoneSN::WriteMEID93()
{
    META_RESULT ret_meta = META_SUCCESS;
    C2kTestCmdGetSetMeid data_meid;

    UpdateUIMsg("Start to Write MEID (>=MT6293)...");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] META_MMC2K_SetMeid_r() : Write MEID start...");
    memset(&data_meid, 0, sizeof(data_meid));
    strcpy_s(data_meid.meid, g_sMetaComm.sScanData.strMeid);
    for (int i = 0; i < 20; i++)
    {
        ret_meta = META_MMC2K_SetMeid_r(m_hMauiMetaHandle, 3000, &data_meid);
        if (ret_meta == META_SUCCESS)
            break;
    }
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("META_MMC2K_SetMeid_r() : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_MMC2K_SetMeid_r() : Write MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }
    MTRACE(g_hEBOOT_DEBUG, "[C2K] META_MMC2K_SetMeid_r() : Write MEID end.");
    UpdateProgress(0.70);


    UpdateUIMsg("Read MEID for check start...");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Read MEID for check start...");
    memset(&data_meid, 0, sizeof(data_meid));
    ret_meta = META_MMC2K_GetMeid_r(m_hMauiMetaHandle, 3000, &data_meid);
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("META_MMC2K_GetMeid_r() : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_MMC2K_GetMeid_r(): Read MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }
    UpdateProgress(0.75);

    MTRACE(g_hEBOOT_DEBUG, "[C2K] Write MEID :%s", g_sMetaComm.sScanData.strMeid);
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Read MEID :%s", data_meid.meid);
    if (_stricmp(data_meid.meid, g_sMetaComm.sScanData.strMeid) != 0)
    {
        UpdateUIMsg("[C2K] Checking MEID fail!");
        MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] Checking MEID fail!");
        return META_FAILED;
    }
    UpdateUIMsg("Checking MEID succesfully!");
    MTRACE(g_hEBOOT_DEBUG, "[C2K] Checking MEID succesfully!");
    UpdateProgress(0.80);

    return META_SUCCESS;
}

int SmartPhoneSN::WriteMEIDToFTNVRAM()
{
	META_RESULT meta_result = META_SUCCESS;

	UpdateUIMsg("Start to Write MEID (>=MT6293)...");
	MTRACE(g_hEBOOT_DEBUG, "UI Input MEID = \"%s\"", g_sMetaComm.sScanData.strMeid);
	MTRACE(g_hEBOOT_DEBUG, "[C2K] WriteMEIDToFTNVRAM() : Write MEID start...");

	int C2K_META_TIMEOUT  = 5000;
	int m_meidBufSizeForNv = 0;
	char *pLID = NULL;
	FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
	FT_NVRAM_4BYTES_LID_WRITE_CNF sNVRAM_WriteCnf;
	FT_NVRAM_READ_REQ  sNVRAM_ReadReq;
	FT_NVRAM_4BYTES_LID_READ_CNF  sNVRAM_ReadCnf;
	char *m_meidBufForNv;
	MEID_struct_T m_WriterMEID;
	MEID_struct_T m_ReadMEID;
	
	pLID = "NVRAM_EF_C2K_MOBILE_ID_LID";
	memset(&m_WriterMEID, 0, sizeof(m_WriterMEID));	
	memset(&m_ReadMEID, 0, sizeof(m_ReadMEID));
	memset(&sNVRAM_WriteReq, 0, sizeof(FT_NVRAM_WRITE_REQ));
	memset(&sNVRAM_WriteCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_WRITE_CNF));	
	memset(&sNVRAM_ReadReq, 0, sizeof(FT_NVRAM_READ_REQ));
	memset(&sNVRAM_ReadCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_READ_CNF));

	strncpy_s(m_WriterMEID.meid, sizeof(m_WriterMEID.meid), g_sMetaComm.sScanData.strMeid, 14);

	MTRACE (g_hEBOOT_DEBUG, "META_NVRAM_GetRecLen_r(): Start to get nvram struct size via LID = \"%s\"...", pLID);
	meta_result = META_NVRAM_GetRecLen_r(m_hMauiMetaHandle, pLID, &m_meidBufSizeForNv);
	if (meta_result != META_SUCCESS)
	{
		UpdateUIMsg("META_NVRAM_GetRecLen_r() : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
		MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_NVRAM_GetRecLen_r() : Write MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
		goto Err;
	}
	
	m_meidBufForNv = new char[m_meidBufSizeForNv];
	memset(m_meidBufForNv, 0, m_meidBufSizeForNv);
	
	m_WriterMEID.idType = 2;
	meta_result = META_NVRAM_Compose_MEID_r(m_hMauiMetaHandle, &m_WriterMEID, m_meidBufForNv, m_meidBufSizeForNv);

    sNVRAM_WriteReq.LID = pLID;
    sNVRAM_WriteReq.RID = 1;
    sNVRAM_WriteReq.len = m_meidBufSizeForNv;
    sNVRAM_WriteReq.buf = (unsigned char *)m_meidBufForNv;

    meta_result = META_NVRAM_4Bytes_LID_Write_Ex_r(m_hMauiMetaHandle, C2K_META_TIMEOUT, &sNVRAM_WriteReq, &sNVRAM_WriteCnf);
	if (meta_result != META_SUCCESS)
	{
		UpdateUIMsg("META_NVRAM_4Bytes_LID_Write_Ex_r() : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
		MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_NVRAM_4Bytes_LID_Write_Ex_r(): Read MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
		goto Err;
	}
	MTRACE(g_hEBOOT_DEBUG, "[C2K] META_NVRAM_4Bytes_LID_Write_Ex_r() : Write MEID end.");
	UpdateProgress(0.70);

	UpdateUIMsg("Read MEID for check start...");
	MTRACE(g_hEBOOT_DEBUG, "[C2K] Read MEID for check start...");

	memset(m_meidBufForNv, 0, m_meidBufSizeForNv);
	
	sNVRAM_ReadReq.LID = pLID;
	sNVRAM_ReadReq.RID = 1;
	
	sNVRAM_ReadCnf.len = m_meidBufSizeForNv;
	sNVRAM_ReadCnf.buf = (unsigned char *)m_meidBufForNv;
	
	meta_result = META_NVRAM_4Bytes_LID_Read_Ex_r(m_hMauiMetaHandle, C2K_META_TIMEOUT, &sNVRAM_ReadReq, &sNVRAM_ReadCnf);
	if (meta_result != META_SUCCESS)
	{
		UpdateUIMsg("META_NVRAM_4Bytes_LID_Read_Ex_r() : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
		MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] META_NVRAM_4Bytes_LID_Read_Ex_r(): Read MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
		goto Err;
	}
	MTRACE(g_hEBOOT_DEBUG, "[C2K] META_NVRAM_4Bytes_LID_Read_Ex_r() : Read MEID end.");
	
	meta_result = META_NVRAM_Decompose_MEID_r(m_hMauiMetaHandle, &m_ReadMEID, m_meidBufForNv, m_meidBufSizeForNv);

	UpdateProgress(0.75);

	MTRACE(g_hEBOOT_DEBUG, "[C2K] Write MEID :%s", g_sMetaComm.sScanData.strMeid);
	MTRACE(g_hEBOOT_DEBUG, "[C2K] Read MEID :%s", m_ReadMEID.meid);
	if (_stricmp(m_ReadMEID.meid, g_sMetaComm.sScanData.strMeid) != 0)
	{
		UpdateUIMsg("[C2K] Checking MEID fail!");
		MTRACE_ERR(g_hEBOOT_DEBUG, "[C2K] Checking MEID fail!");
		return META_FAILED;
	}
	
	UpdateUIMsg("Checking MEID succesfully!");
	MTRACE(g_hEBOOT_DEBUG, "[C2K] Checking MEID succesfully!");
	UpdateProgress(0.80);

	if (m_meidBufForNv != NULL)
	{
		delete[] m_meidBufForNv;
		m_meidBufForNv = NULL;
	}

	return META_SUCCESS;

Err:
	if (m_meidBufForNv != NULL)
	{
		delete[] m_meidBufForNv;
		m_meidBufForNv = NULL;
	}
	MTRACE_ERR (g_hEBOOT_DEBUG, " Write MEID Fail");

	return meta_result;

}

bool SmartPhoneSN::QueryEncryptionSupport(int * MetaHandle )
{
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::QueryEncryptionSupport() : Start");
	CRYPTFS_QUERYSUPPORT_CNF cryptfs_Support_cnf;
	META_RESULT MetaResult;
	unsigned int uiAPIRetry = 0;
	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		MetaResult = SP_META_ENCRYPTED_Support_r(*MetaHandle, 60000, &cryptfs_Support_cnf);
		if (MetaResult == META_SUCCESS)
		{	
			break;
		}
	}

	if ((MetaResult == META_SUCCESS) && (cryptfs_Support_cnf.support == 1))
	{
		MTRACE(g_hEBOOT_DEBUG, "Do support Encryption!");
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::QueryEncryptionSupport() : End");
		return true;
	}
	MTRACE(g_hEBOOT_DEBUG, "Do not support Encryption : MetaResult = %s!", ResultToString_SP(MetaResult));
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::QueryEncryptionSupport() : End");
	return false;
}

bool SmartPhoneSN::VeritifiEncryption(int *MetaHandle)
{
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::VeritifiEncryption() : Start");
	META_RESULT MetaResult;
	unsigned int uiAPIRetry = 0;
    CRYPTFS_VERITIF_REQ cryptfs_Veritif_Req;
    CRYPTFS_VERITIF_CNF cryptfs_Veritif_Cnf;

	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		memset(&cryptfs_Veritif_Req, 0, sizeof(CRYPTFS_VERITIF_REQ));
		strcpy_s((char *)cryptfs_Veritif_Req.pwd, 32, g_cPwd);
		cryptfs_Veritif_Req.length = strlen(g_cPwd);
		memset(&cryptfs_Veritif_Cnf, 0, sizeof(CRYPTFS_VERITIF_CNF));

		MetaResult = SP_META_ENCRYPTED_VertifyPwd_r(*MetaHandle, 60000, &cryptfs_Veritif_Req, &cryptfs_Veritif_Cnf);
		if (MetaResult == META_SUCCESS)
		{	
			break;
		}
	}
	if ((MetaResult == META_SUCCESS) && cryptfs_Veritif_Cnf.match_result == 1)
	{
		MTRACE(g_hEBOOT_DEBUG, "Do match the password!");
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::VeritifiEncryption() : End");
		return true;
	}
	else
	{
		UpdateUIMsg("Do not match: MetaResult = %s!", ResultToString_SP(MetaResult));
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::VeritifiEncryption() : End");
		return false;
	}
}

bool SmartPhoneSN::SetDutClock(int hApMeta)
{
    META_RESULT ret_mt;
    SET_TARGET_CLOCK_REQ req;
    SET_TARGET_CLOCK_CNF cnf;
    SYSTEMTIME time;
	unsigned int uiAPIRetry = 0;

	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		ret_mt = SP_META_QueryIfFunctionSupportedByTarget_r(hApMeta, 2000, "SP_META_Set_TargetClock_r");
		if (ret_mt == META_SUCCESS)
		{	
			break;
		}
	}
    if (ret_mt != META_SUCCESS)
    {
        MTRACE_WARN(g_hEBOOT_DEBUG, "SmartPhoneSN::SetDutClock(): don't support(%d): %s.",
            ret_mt, ResultToString_SP(ret_mt));
        return false;
    }

	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		memset(&req, 0, sizeof(req));
		memset(&cnf, 0, sizeof(cnf));
		::GetLocalTime(&time);
		req.year = time.wYear;
		req.mon = time.wMonth;
		req.day = time.wDay;
		req.hour = time.wHour;
		req.min = time.wMinute;
		req.sec = time.wSecond;
		req.ms = time.wMilliseconds;

		ret_mt = SP_META_Set_TargetClock_r(hApMeta, 3000, &req, &cnf);
		if (ret_mt == META_SUCCESS)
		{	
			break;
		}
	}
    if (ret_mt != META_SUCCESS)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::SetDutClock(): set fail(%d): %s.",
            ret_mt, ResultToString_SP(ret_mt));
        return false;
    }

    MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SetDutClock(): set ok.");
    return true;
}

bool SmartPhoneSN::Read_AP_Flag(char *AP_Flag)
{
	AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq1;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf1;
    META_RESULT meta_result;
	PRODUCT_INFO* product_info;

    memset(&sNVRAM_ReadReq1, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf1, 0, sizeof(AP_FT_NVRAM_READ_CNF));

	sNVRAM_ReadReq1.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    sNVRAM_ReadReq1.RID = 1;
    sNVRAM_ReadCnf1.len = sizeof(PRODUCT_INFO);
    sNVRAM_ReadCnf1.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));


	meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq1, &sNVRAM_ReadCnf1);
    if (meta_result != META_SUCCESS )
    {
        return false;
    }
	product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf1.buf;
    AP_Flag[0] = product_info->barcode[58];
	AP_Flag[1] = product_info->barcode[53];
	AP_Flag[2] = product_info->barcode[56];
	AP_Flag[52] = product_info->barcode[52];
	AP_Flag[53] = product_info->barcode[53];
	AP_Flag[55] = product_info->barcode[55];
	AP_Flag[56] = product_info->barcode[56];
	AP_Flag[57] = product_info->barcode[57];
	return true;
}

bool SmartPhoneSN::FactoryReset()
{
	EMMC_CLEAR_CNF_S cnf;
	memset(&cnf,0,sizeof(EMMC_CLEAR_CNF_S));
	META_RESULT iRet = SP_META_ClearValueEx_r(m_hSPMetaHandle, 15000, &cnf);
	if (iRet != META_SUCCESS)
	{
		return false;
	}
	else
	{
		return true;
	}
}

bool SmartPhoneSN::Reboot()
{
	META_RESULT iRet = SP_META_CloseComPortReboot_r(m_hSPMetaHandle);
	if (iRet != META_SUCCESS)
	{
		return false;
	}
	else
	{
		return true;
	}
}

bool SmartPhoneSN::checkversion_EX()
{
	META_RESULT MetaResult;
	BUILD_PROP_REQ_S build_prop_key;
	BUILD_PROP_CNF_S build_prop_value;
    int m_hSPMetaHandle=0;
	
	memset(&build_prop_key, 0, sizeof(BUILD_PROP_REQ_S));
	memset(&build_prop_value, 0, sizeof(BUILD_PROP_CNF_S));
	strcpy((char*)(build_prop_key.tag), "ro.software.version");
	MetaResult = SP_META_GetTargetBuildProp_r(m_hSPMetaHandle, &build_prop_key, &build_prop_value);
	if(MetaResult!=META_SUCCESS)
    {
        return false;
    }

	// 若固件没有ro.software.version这个systemproperty，则按照之前的方式比较软件版本
	if(strcmp((char*)(build_prop_value.content), "unknown") == 0)
	{
		// 有些固件里保存软件版本的system property改成了ro.bdmisc.software_version，必须兼容它
        memset(&build_prop_key, 0, sizeof(BUILD_PROP_REQ_S));
        memset(&build_prop_value, 0, sizeof(BUILD_PROP_CNF_S));
        strcpy((char*)(build_prop_key.tag), "ro.bdmisc.software_version");
        MetaResult = SP_META_GetTargetBuildProp_r(m_hSPMetaHandle, &build_prop_key, &build_prop_value);
        if(MetaResult!=META_SUCCESS)
        {
			return false;
        }
		
		if(strcmp((char*)(build_prop_value.content), "unknown") == 0)
		{
			// 有些固件里保存软件版本的system property改成了ro.bdfun.inner_sw_version，必须兼容它
			memset(&build_prop_key, 0, sizeof(BUILD_PROP_REQ_S));
			memset(&build_prop_value, 0, sizeof(BUILD_PROP_CNF_S));
			//strcpy((char*)(build_prop_key.tag), "ro.bdfun.inner_sw_version");
			strcpy((char*)(build_prop_key.tag), "ro.bdmisc.build_number");
			MetaResult = SP_META_GetTargetBuildProp_r(m_hSPMetaHandle, &build_prop_key, &build_prop_value);
			if(MetaResult!=META_SUCCESS)
			{
				return false;
			}
			if(strcmp((char*)(build_prop_value.content), "unknown") == 0)
			{
				// 有些固件里保存软件版本的system property改成了ro.bdfun.inner_sw_version，必须兼容它
				memset(&build_prop_key, 0, sizeof(BUILD_PROP_REQ_S));
				memset(&build_prop_value, 0, sizeof(BUILD_PROP_CNF_S));
				strcpy((char*)(build_prop_key.tag), "ro.build.display.id");
				MetaResult = SP_META_GetTargetBuildProp_r(m_hSPMetaHandle, &build_prop_key, &build_prop_value);
				if(MetaResult!=META_SUCCESS)
				{
					return false;
				}

				if(strcmp((char*)(build_prop_value.content), "unknown") == 0)
				{
					return checkversion();
				}
			}
		}
	}


	if (0 == strcmp((char*)(build_prop_value.content), g_sMetaComm.version))
		return true;
	else
	{
		g_sMetaComm.SW_Version = (char *)build_prop_value.content;
		return false;
	}
}

bool SmartPhoneSN::checkversion()
{
	short GETID_VERSION;
	META_RESULT MetaResult;
	size_t strLen;
	int m_hSPMetaHandle=0;
	VerInfo.SW_VER[0] = 0;
	bVerCallBackRet = false;

	MetaResult = SP_META_GetTargetVerInfo_r(m_hSPMetaHandle, GetTargetVersionCallBack, &GETID_VERSION, NULL);
    if(MetaResult!=META_SUCCESS)
    {
        return false;
    }
	while(true != bVerCallBackRet)
		Sleep(1);
	strLen = strlen(VerInfo.HW_VER);    //注意以前Meta版本是读取SW_VER，现在用OneXA读取是HW_VER
	if (strLen < 1)
		return false;
	
	while (0x0D == VerInfo.HW_VER[strlen(VerInfo.HW_VER)-1]
			||0x0A == VerInfo.HW_VER[strlen(VerInfo.HW_VER)-1])
		VerInfo.HW_VER[strlen(VerInfo.HW_VER)-1] = 0;

	if (g_sMetaComm.smesconnect.mesconnectcheck && g_sMetaComm.smesconnect.strMes[0] != 'L')
	{
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::checkversion(): Get version From mes is %s.", g_sMetaComm.smesconnect.swversion);
		sprintf(g_sMetaComm.version, "%s", g_sMetaComm.smesconnect.swversion);
	}

	if (0 == strcmp(VerInfo.HW_VER, g_sMetaComm.version))
		return true;
	else
	{
		g_sMetaComm.SW_Version = VerInfo.HW_VER;
		return false;
	}
}
bool SmartPhoneSN::checkIntNumber()
{
	META_RESULT MetaResult;
	BUILD_PROP_REQ_S build_prop_key;
	BUILD_PROP_CNF_S build_prop_value;
   int m_hSPMetaHandle=0;
	


	memset(&build_prop_key, 0, sizeof(BUILD_PROP_REQ_S));
	memset(&build_prop_value, 0, sizeof(BUILD_PROP_CNF_S));
	strcpy((char*)(build_prop_key.tag), "odm.agn.int_number");
	MetaResult = SP_META_GetTargetBuildProp_r(m_hSPMetaHandle, &build_prop_key, &build_prop_value);
	if(MetaResult!=META_SUCCESS)
    {
        return false;
    }
	
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::checkversion(): build_prop_value.content = %s", build_prop_value.content);
	if (0 == strcmp((char*)(build_prop_value.content), g_sMetaComm.intnumber))
		return true;
	else
	{

		return false;
	}
}
bool SmartPhoneSN::Check_Flag(char *The_Flag)
{
	char TotalFlag[65]= {0};
	char AP_Flag[64] = {0};
	if(REQ_ReadModem_NVRAM_Start(WRITE_BARCODE, TotalFlag, 1) == META_SUCCESS)
	{
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::Check_Flag():The MD Flag is: %s",TotalFlag);
		bool ret = Read_AP_Flag(AP_Flag);
		if(AP_Flag[52] != '\0')TotalFlag[52]=AP_Flag[52];
		if(AP_Flag[53] != '\0')TotalFlag[53]=AP_Flag[53];
		if(AP_Flag[55] != '\0')TotalFlag[55]=AP_Flag[55];
		if(AP_Flag[56] != '\0')TotalFlag[56]=AP_Flag[56];
		if(AP_Flag[57] != '\0')TotalFlag[57]=AP_Flag[57];
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::Check_Flag():The AP Flag is: %s",AP_Flag);
	}
	else
	{
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::Check_Flag() REQ_ReadModem_NVRAM_Start failed !");
		return false;
	}
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::Check_Flag():The ALL Flag is: %s",TotalFlag);
	strcpy(The_Flag,TotalFlag);
	
	return true;
}
char SmartPhoneSN::CheckMDMFlag()
{
	AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
	PRODUCT_INFO* product_info;

    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

	sNVRAM_ReadReq.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    sNVRAM_ReadReq.RID = 1;
    sNVRAM_ReadCnf.len = sizeof(PRODUCT_INFO);
    sNVRAM_ReadCnf.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));


	meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
		MTRACE(g_hEBOOT_DEBUG, "\n 读取Product info失败\n");
        return 'H';
    }

	product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf.buf;
	char flag_MDM = product_info->barcode[52];

	if((flag_MDM >= '0'&&  flag_MDM <= '8') || (flag_MDM >= 'A'&&  flag_MDM <= 'G'))
	 {
       return flag_MDM;
	 }
	return 'I';
}

META_RESULT SmartPhoneSN::MDSLA_Connect()
{
	bool enabled = false;
	bool verified = false;
	META_RESULT meta_result = META_SUCCESS;
	if(g_sMetaComm.eTargetType != THIN_MODEM_FLASHLESS)
	{
		meta_result = EnableModemMeta();
		if(meta_result!= META_SUCCESS)
		{
			MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::MDSLA_Connect(): EnableModemMeta failed");
			return meta_result;
		}
	}

	if (GetMdSlaEnabled(&enabled,&verified) == true)
	{
		//meta_result = RunVerifyMdSla();
		RunVerifyMdSla();
	}
	return meta_result;
}
bool SmartPhoneSN::GetMdSlaEnabled(bool* isEnabled, bool* isVerified)
{
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::GetMdSlaEnabled()...");
	META_RESULT meta_result = META_SUCCESS;
	unsigned int uiAPIRetry = 0;
	*isEnabled = false;
	*isVerified = false;

	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		meta_result = META_QueryIfFunctionSupportedByTarget_r(m_hMauiMetaHandle, 3000, "META_GetSlaStatus_r");
		if (meta_result == META_SUCCESS)
		{	
			break;
		}
	}
	if (META_SUCCESS == meta_result)
	{
		SlaStatus_Cnf cnf;
		//meta_result = META_GetSlaStatus_r(m_hMauiMetaHandle, 3000, &cnf);
		for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
		{
			meta_result = META_GetSlaStatus_r(m_hMauiMetaHandle, 3000, &cnf);
			if (meta_result == META_SUCCESS)
			{	
				break;
			}
		}
		if (meta_result == META_SUCCESS)
		{
			*isEnabled = (cnf.sla_config == 1)? true: false;
			*isVerified = (cnf.sla_verified == 1)? true: false;
		}
		else
		{
			MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::CheckTargetSlaStatus(): META_GetSlaStatus_r failed");
		}
	}
	else if (META_FUNC_NOT_IMPLEMENT_YET == meta_result)
	{
		MTRACE_WARN(g_hEBOOT_DEBUG, "SmartPhoneSN::CheckTargetSlaStatus(): META_GetSlaStatus_r is not supported by the target");
		meta_result = META_SUCCESS;
	}
	else if(META_FAILED == meta_result)
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::CheckTargetSlaStatus(): Query META_GetSlaStatus_r supported failed");
	}
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::CheckTargetSlaStatus():result(%s),isEnabled(%d), isVerified(%d)",ResultToString(meta_result),*isEnabled, *isVerified);
	return *isEnabled;
}
META_RESULT SmartPhoneSN::RunVerifyMdSla()
{
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RunVerifyMdSla()...");
	META_RESULT meta_result = META_SUCCESS;
	unsigned int uiAPIRetry = 0;

	meta_result = META_VerifySla_r(m_hMauiMetaHandle, 5000);
	if (meta_result != META_SUCCESS)
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::RunVerifyMdSla():META_VerifySla_r failed(%s)",ResultToString(meta_result));
		return meta_result;
	}
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RunVerifyMdSla():META_VerifySla_r success");
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RunVerifyMdSla() : End");
	return meta_result;
}

/*************************************************************************************************/

void SmartPhoneSN::cbk_MetaConnTrace(const char* logBuf, void* userdata)
{
    SmartPhoneSN *pMetaBase = (SmartPhoneSN *)userdata;
	pMetaBase->UpdateUIMsg(logBuf);
	
	/*TCHAR pLogTrace[1024];
    memset(pLogTrace, 0, sizeof(TCHAR)*1024);

    SmartPhoneSN *pMetaBase = (SmartPhoneSN *)userdata;
	pMetaBase->UpdateUIMsg(logBuf);

#ifdef UNICODE
    ::MultiByteToWideChar(CP_ACP, 0, logBuf, -1, pLogTrace, strlen(logBuf)+1);
#else
    strcpy_s(pLogTrace, 1024, logBuf);
#endif

    pMetaBase->ReportLog(pLogTrace);

    if(strstr(logBuf, "Fail") != NULL)
    {
        pMetaBase->UpdateErrMessage(logBuf);
    }*/

}

META_RESULT SmartPhoneSN::SLA_Check()
{
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SLA_Check() : Start");
	META_RESULT meta_result = META_SUCCESS;
	bool enabled = false;
	bool verified = false;
	if (GetMdSlaEnabled(&enabled,&verified) == true)
	{
		RunVerifyMdSla();
	}
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::SLA_Check() : End");
	return meta_result;
}

META_RESULT SmartPhoneSN::RunClearSlaStatus()
{
    MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::RunClearSlaStatus() : Start");
	META_RESULT meta_result = META_SUCCESS;
	bool enabled = false;
	bool verified = false;
    if (GetMdSlaEnabled(&enabled,&verified) == true)
    {
        meta_result = META_QueryIfFunctionSupportedByTarget_r(m_hMauiMetaHandle, 5000, "META_ClearSlaStatus_r");
        if (meta_result == META_SUCCESS)
        {
            ClearSlaStatus_cnf cnf = {0};
            meta_result = META_ClearSlaStatus_r(m_hMauiMetaHandle, 5000,  &cnf);
            if (meta_result != META_SUCCESS)
            {
				MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::RunClearSlaStatus() META_VerifySla_r failed(%s)",ResultToString(meta_result));
            }
            else if ((meta_result == META_SUCCESS) && (cnf.status != 0))
            {
                MTRACE_WARN(g_hEBOOT_DEBUG, "SmartPhoneSN::RunClearSlaStatus()  Clear SLA status result is: %d", cnf.status);
            }
            else
            {
				MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::RunClearSlaStatus() Clear SLA status result is: %d", cnf.status);
            }
        }
    }
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::RunClearSlaStatus() : End...");
    return meta_result;
}

META_RESULT SmartPhoneSN::Encrypt_Check()
{
	META_RESULT meta_result = META_SUCCESS;
	unsigned int uiAPIRetry = 0;
	for (uiAPIRetry = 0; uiAPIRetry < 3; uiAPIRetry++)
	{
		meta_result = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 500, "SP_META_ENCRYPTED_Support_r");
		if (meta_result == META_SUCCESS)
		{	
			break;
		}
	}
	uiAPIRetry = 0;
	if (meta_result == META_SUCCESS)
	{
		if (QueryEncryptionSupport(&m_hSPMetaHandle))
		{
			Encryption Edlg;
			//Edlg.DoModal();
			while ((IDOK == Edlg.DoModal() ) && uiAPIRetry < 3)
			{
				if (!VeritifiEncryption(&m_hSPMetaHandle))
				{
					MTRACE(g_hEBOOT_DEBUG, "VeritifiEncryption() retry : %d", uiAPIRetry + 1);
					uiAPIRetry = uiAPIRetry + 1;
				}
				else
				{
					meta_result= META_SUCCESS;
					break;
				}
			}
			if (3 == uiAPIRetry)
			{
				meta_result= META_SUCCESS;
				MTRACE(g_hEBOOT_DEBUG, "VeritifiEncryption() retry times more than 3 times!");
			}
		}
	}
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::Encrypt_Check() : End");
	return meta_result;
}

META_RESULT SmartPhoneSN::EnterAPMetaModeEx()
{
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx() : Start...");
	META_RESULT MetaResult = META_SUCCESS;
	int iRet = 0;

    iRet = METAAPP_RegisterCallBackFor_Conn_Log_Display(m_iThreadID, &cbk_MetaConnTrace, this);
    if(iRet != META_SUCCESS)
    {
        MTRACE_ERR(g_hEBOOT_DEBUG, "CMtkSpMetaBase::RegistMetaConnTraceCallback(): Regist fail, %s.", ResultToString(iRet));
    }

	if (g_sMetaComm.eTargetType != THIN_MODEM_FLASHLESS)
	{
		UpdateUIMsg("Disable MIPC Port.");
		UpdateProgress(0.07);
		Sleep(500);
	}
	
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx() : Begin to connect AP META ...");
	
	if (g_sMetaComm.eTargetType == SMART_PHONE
		|| (g_sMetaComm.eTargetType == TABLET_WIFI_ONLY) )
	{	
		METAAPP_CONNCT_CNF_T pCnf;
		memset(&pCnf,0,sizeof(pCnf));

		//Sleep(500);
		UpdateProgress(0.07);
		ArgBootMetaMode();

		if(g_sMetaComm.iCOMPort == 0)
		{
			//Auto Scan comport
			m_MetaAppConnSet.autoScanPort = true;
			
		}
		else
		{
			//Assign comport
			m_MetaAppConnSet.autoScanPort = false;
			m_MetaAppConnSet.connectSetting.kernelComPort = g_sMetaComm.iCOMPort;
		}
		if (g_sMetaComm.eTargetType == SMART_PHONE)
		{
			MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx() : METAAPP_ConnectTargetAllInOne_r()");		
			iRet = METAAPP_ConnectTargetAllInOne_r(m_iThreadID,&m_MetaAppConnSet,&pCnf,sizeof(m_MetaAppConnSet));
		}
		
		if (g_sMetaComm.eTargetType == TABLET_WIFI_ONLY)
		{
			MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx() : METAAPP_ConnectAPOnlyAllInOne_r()");	
			m_MetaAppConnSet.logSetting.iMDLoggEnable = false;
			iRet = METAAPP_ConnectAPOnlyAllInOne_r(m_iThreadID,&m_MetaAppConnSet,&pCnf,sizeof(m_MetaAppConnSet));
		}

		if (iRet == 0)
		{
			UpdateUIMsg("Connect AP META success!");
			MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx(): Connect AP META success!");
		}
		else if(iRet == METAAPP_HIGH_LEVEL_AP_DATABASE_NOT_MATCH)
		{
			UpdateUIMsg("META DB DISMATCH!");
			MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx(): DB DISMATCH!"); 
			if(!g_sMetaComm.IgnoreDBInconsistent)
			{	
				m_bTargetInMetaMode = true;
				m_eMetaMode = SP_AP_META;
				MetaResult = META_MAUI_DB_INCONSISTENT;
			}
			else
			{
				UpdateUIMsg("METAAPP_ConnectAPOnlyAllInOne_r success!");
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx():  success!");
			}
		}
		else
		{
			UpdateUIMsg("Connect AP META Fail!");
			MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx(): , Err = %d", iRet);
			MetaResult = META_FAILED;
		}
		m_hMauiMetaHandle = pCnf.hMDHandle;
		m_hSPMetaHandle = pCnf.hMDHandle;

	}		
	else if (g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS
				|| (g_sMetaComm.eTargetType == DATA_CARD))
	{   
		METAAPP_DATACARD_CONNCT_CNF_T pCnf;
		memset(&pCnf,0,sizeof(pCnf));
		ArgBootDCMetaMode();
		
		UpdateProgress(0.07);
		
		if (g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
		{
			UpdateUIMsg("Enable MIPC Port.");
			Sleep(500);
			if(g_sMetaComm.iMDKernelComport == 0)
			{
				//Auto Scan comport
				m_DCMetaAppConnSet.autoScanPort = true;
				if (m_DCMetaAppConnSet.extralSetting.enableMIPCComPort)
				{		
					m_DCMetaAppConnSet.connectSetting[MD_META].bConnection = true;
					m_DCMetaAppConnSet.connectSetting[AP_META].bConnection = false; 
				}				
			}
			else
			{
				//Assign comport
				m_DCMetaAppConnSet.autoScanPort = false;
				if (m_DCMetaAppConnSet.extralSetting.enableMIPCComPort)
				{		
					m_DCMetaAppConnSet.connectSetting[MD_META].bConnection = true;
					m_DCMetaAppConnSet.connectSetting[AP_META].bConnection = false; 
				}
				/*AP META PORT*/
				m_DCMetaAppConnSet.connectSetting[AP_META].kernelComPort = g_sMetaComm.iMDKernelComport;
				/*MD META PORT*/
				m_DCMetaAppConnSet.connectSetting[MD_META].kernelComPort = g_sMetaComm.iMDKernelComport;
			}
		}
		
		if (g_sMetaComm.eTargetType == DATA_CARD)
		{
			if ((g_sMetaComm.iCOMPort == 0) &&(g_sMetaComm.iMDKernelComport == 0))
			{
				//Auto Scan comport
				m_DCMetaAppConnSet.autoScanPort = true;				
			}
			else
			{
				//Assign comport
				m_DCMetaAppConnSet.autoScanPort = false;
				/*AP META PORT*/
				m_DCMetaAppConnSet.connectSetting[AP_META].kernelComPort = g_sMetaComm.iCOMPort;
				/*MD META PORT*/
				m_DCMetaAppConnSet.connectSetting[MD_META].kernelComPort = g_sMetaComm.iMDKernelComport;
			}
			if (g_sMetaComm.bModemOnly)
			{
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx() : ModemOnly");
				m_DCMetaAppConnSet.connectSetting[MD_META].bConnection = true;
				m_DCMetaAppConnSet.connectSetting[AP_META].bConnection = false; 
			}
			else
			{
				m_DCMetaAppConnSet.connectSetting[MD_META].bConnection = true;
				m_DCMetaAppConnSet.connectSetting[AP_META].bConnection = true; 
			}
		}
		
		MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx() : METAAPP_ConnectDCToAPMeta_r()");

		iRet = METAAPP_ConnectDCToAPMeta_r(m_iThreadID,&m_DCMetaAppConnSet,&pCnf,sizeof(m_DCMetaAppConnSet));

		if (iRet == 0)
		{
			UpdateUIMsg("Connect AP META success!");
			MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx(): Connect AP META success!");
		}
		else if(iRet == METAAPP_HIGH_LEVEL_AP_DATABASE_NOT_MATCH)
		{
			UpdateUIMsg("META DB DISMATCH!");
			MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx(): DB DISMATCH!"); 
			if(!g_sMetaComm.IgnoreDBInconsistent)
			{	
				m_bTargetInMetaMode = true;
				m_eMetaMode = SP_AP_META;
				MetaResult = META_MAUI_DB_INCONSISTENT;
			}
			else
			{
				UpdateUIMsg("METAAPP_ConnectAPOnlyAllInOne_r success!");
				MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx():  success!");
			}
		}
		else
		{
			UpdateUIMsg("Connect AP META Fail!");
			MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx(): , Err = %d", iRet);
			MetaResult = META_FAILED;
		}
		m_hMauiMetaHandle = pCnf.hHandle;
		m_hSPMetaHandle = pCnf.hHandle;

	}
	else
	{
	}
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx():m_iThreadID =%d, m_hMauiMetaHandle = %d, m_hSPMetaHandle = %d", m_iThreadID,m_hMauiMetaHandle,m_hSPMetaHandle);
	MTRACE (g_hEBOOT_DEBUG, "SmartPhoneSN::EnterAPMetaModeEx() : End...");
	return MetaResult;
}

void SmartPhoneSN::ArgBootMetaMode()
{
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ArgBootMetaMode() : Start ...");

	memset (&m_MetaAppConnSet, 0x0, sizeof(METAAPP_DATACARD_CONN_STTING_T));
	memset(&m_ExtendConnSetting,0x0,sizeof(METAAPP_EXTEND_CONN_SETTING_T));

	m_MetaAppConnSet.stopFlag = m_pMetaStopFlag;
	m_MetaAppConnSet.uTimeOutMs = m_stModeArg.m_uTimeout;
	m_MetaAppConnSet.connectMode = (METAAPP_CONN_MODE_E)g_sMetaComm.bAlreadyInMeata;
	
	//m_MetaAppConnSet.connectType = (METAAPP_CONN_TYPE_E)g_sMetaComm.bUsbEnable;
	//0: uart,1: usb,2: wifi
	m_MetaAppConnSet.connectType = (METAAPP_CONN_TYPE_E)g_sMetaComm.nConMethod;

	METAAPP_CONN_BOOT_STTING_T& m_BootSet = m_MetaAppConnSet.bootSetting;
	if(g_sMetaComm.bSecurityUSB)
	{
		int iRet=0;
		m_ExtendConnSetting.extraConnSettingSLA.authHandle= SPATE_Get_AuthHandle();
		m_ExtendConnSetting.extraConnSettingSLA.scertHandle = SPATE_Get_ScertHandle();
		m_ExtendConnSetting.extraConnSettingSLA.cbSlaChallenge=SLA_Challenge;
		m_ExtendConnSetting.extraConnSettingSLA.callbackSLAChallengeArg=NULL;
		m_ExtendConnSetting.extraConnSettingSLA.cbSlaChallengeEnd=SLA_Challenge_END;
		m_ExtendConnSetting.extraConnSettingSLA.callbackSLAChallengeArgEnd=NULL;
		iRet=METAAPP_ExtendParaSetting_r(m_iThreadID,&m_ExtendConnSetting);
	}

	m_BootSet.bEnableAdbDevice = g_sMetaComm.bCompositeDeviceEnable;

	m_MetaAppConnSet.logSetting.enableDllLog = g_sMetaComm.bMetaDllLog;
	//Modem Log
	if (g_sMetaComm.eTargetType == TABLET_WIFI_ONLY)
	{
		m_MetaAppConnSet.logSetting.iMDLoggEnable = false;
	}
	else
	{
		m_MetaAppConnSet.logSetting.iMDLoggEnable = g_sMetaComm.bMDLog;
	}
	m_MetaAppConnSet.logSetting.iMobileLogEnable = g_sMetaComm.bMobileLog;
	m_MetaAppConnSet.logSetting.iConnsysLogEnable = g_sMetaComm.bConnsysLog;
	m_MetaAppConnSet.logSetting.enableUartLog = false;

	//USB trace log
	m_MetaAppConnSet.logSetting.enablePcUsbDriverLog = g_sMetaComm.bPcUSBDumpTraceLog;

	m_MetaAppConnSet.filterSetting.brom = g_sMetaComm.sPortFilter.strBromFilter;
	m_MetaAppConnSet.filterSetting.preloader=g_sMetaComm.sPortFilter.strPreloaderFilter;
	m_MetaAppConnSet.filterSetting.kernel=g_sMetaComm.sPortFilter.strKernelFilter;

	sprintf_s(m_MetaAppConnSet.logSetting.IP, 64, "**************");
	
	//Set LogName
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ArgBootMetaMode() : m_strLogName = %s", m_strLogName);
	sprintf_s(m_MetaAppConnSet.logSetting.pcbSn, 96, m_strLogName);
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ArgBootMetaMode() : sn = %s", m_MetaAppConnSet.logSetting.pcbSn);

	//Open META DLL Log
	sprintf_s(m_MetaAppConnSet.logSetting.logSavePath, 260, g_sMetaComm.strLogDir);

	m_BootSet.uMDMode = 0xc000; //m_sMetaArg.uMDMode;

	m_MetaAppConnSet.connectSetting.bDbFileFromDUT = g_sMetaComm.sDBFileOption.bMDDBFromDUT;
    if (!g_sMetaComm.sDBFileOption.bAPDBFromDUT)
    {
	    m_MetaAppConnSet.connectSetting.pApDbFilePath = g_sMetaComm.sDBFileOption.strAPDbpath;
    }
    if(!g_sMetaComm.sDBFileOption.bMDDBFromDUT)
    {
	    m_MetaAppConnSet.connectSetting.pMdDbFilePath = g_sMetaComm.sDBFileOption.strMD1Dbpath;
	    m_MetaAppConnSet.connectSetting.pMdDbFilePath1= g_sMetaComm.sDBFileOption.strMD2Dbpath;
    }
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ArgBootMetaMode() : End...");
}


void SmartPhoneSN::ArgBootDCMetaMode()
{
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ArgBootDCMetaMode() : Start...");
	memset (&m_DCMetaAppConnSet, 0x0, sizeof(METAAPP_DATACARD_CONN_STTING_T));
	memset(&m_ExtendConnSetting,0x0,sizeof(METAAPP_EXTEND_CONN_SETTING_T));

	m_DCMetaAppConnSet.stopFlag = m_pMetaStopFlag;
	m_DCMetaAppConnSet.uTimeOutMs = m_stModeArg.m_uTimeout;
	m_DCMetaAppConnSet.connectMode = (METAAPP_CONN_MODE_E)g_sMetaComm.bAlreadyInMeata;

	//m_DCMetaAppConnSet.connectType = (METAAPP_CONN_TYPE_E)g_sMetaComm.bUsbEnable;
	//0: uart,1: usb,2: wifi
	m_DCMetaAppConnSet.connectType = (METAAPP_CONN_TYPE_E)g_sMetaComm.nConMethod;
	if(g_sMetaComm.bEnableMIPCPort == true)
	{
		m_DCMetaAppConnSet.extralSetting.enableMIPCComPort = true;
	}
	else
	{
		m_DCMetaAppConnSet.extralSetting.enableMIPCComPort = false;
	}


	METAAPP_CONN_BOOT_STTING_T& m_BootSet = m_DCMetaAppConnSet.bootSetting;
	if(g_sMetaComm.bSecurityUSB)
	{
		int iRet=0;
		//m_BootSet.authHandle = m_phSecurity->GetSPAuthHandle();
		//m_BootSet.scertHandle = m_phSecurity->GetSPScertHandle();
		m_ExtendConnSetting.extraConnSettingSLA.authHandle= SPATE_Get_AuthHandle();
		m_ExtendConnSetting.extraConnSettingSLA.scertHandle = SPATE_Get_ScertHandle();
		m_ExtendConnSetting.extraConnSettingSLA.cbSlaChallenge=SLA_Challenge;
		m_ExtendConnSetting.extraConnSettingSLA.callbackSLAChallengeArg=NULL;
		m_ExtendConnSetting.extraConnSettingSLA.cbSlaChallengeEnd=SLA_Challenge_END;
		m_ExtendConnSetting.extraConnSettingSLA.callbackSLAChallengeArgEnd=NULL;
		iRet=METAAPP_ExtendParaSetting_r(m_iThreadID,&m_ExtendConnSetting);
	}

	m_BootSet.bEnableAdbDevice = g_sMetaComm.bCompositeDeviceEnable;

	m_DCMetaAppConnSet.logSetting.enableDllLog = true;
	m_DCMetaAppConnSet.logSetting.iMDLoggEnable = false;
	m_DCMetaAppConnSet.logSetting.iMobileLogEnable =0; //disbale
	m_DCMetaAppConnSet.logSetting.iConnsysLogEnable =0; //disbale
	m_DCMetaAppConnSet.logSetting.enableUartLog = false;

	m_DCMetaAppConnSet.logSetting.enableDllLog = g_sMetaComm.bMetaDllLog;
	m_DCMetaAppConnSet.logSetting.iMDLoggEnable = g_sMetaComm.bMDLog;
	m_DCMetaAppConnSet.logSetting.iMobileLogEnable = g_sMetaComm.bMobileLog;
	m_DCMetaAppConnSet.logSetting.iConnsysLogEnable = g_sMetaComm.bConnsysLog;
	m_DCMetaAppConnSet.logSetting.enableUartLog = false;

	//USB trace log
	m_DCMetaAppConnSet.logSetting.enablePcUsbDriverLog = g_sMetaComm.bPcUSBDumpTraceLog;

	m_DCMetaAppConnSet.portFilterSetting.brom = g_sMetaComm.sPortFilter.strBromFilter;
	m_DCMetaAppConnSet.portFilterSetting.preloader=g_sMetaComm.sPortFilter.strPreloaderFilter;
	m_DCMetaAppConnSet.portFilterSetting.kernel=g_sMetaComm.sPortFilter.strKernelFilter;
	m_DCMetaAppConnSet.portFilterSetting.mdKernel = g_sMetaComm.sPortFilter.strMDKernelFilter;
	m_DCMetaAppConnSet.portFilterSetting.apLogPort = g_sMetaComm.sPortFilter.strKernelLogPortFilter;
	m_DCMetaAppConnSet.portFilterSetting.mdLogPort = g_sMetaComm.sPortFilter.strMDKernelPortFilter;

	//sprintf_s(m_MetaAppConnSet.logSetting.IP, 64, "**************");
	//sprintf_s(m_DCMetaAppConnSet.logSetting.logSavePath,g_sMetaComm.strLogDir,260);
	
	//Set LogName
	sprintf_s(m_DCMetaAppConnSet.logSetting.pcbSn, 96, m_strLogName);

	//Open META DLL Log
	sprintf_s(m_DCMetaAppConnSet.logSetting.logSavePath, 260, g_sMetaComm.strLogDir);

	m_BootSet.uMDMode = 0; //m_sMetaArg.uMDMode;

	//to do optimization (Modem only case, or AP only case)
	// m_DCMetaAppConnSet.connectSetting[AP_META].pDbFilePath = m_sMetaArg.pDbInfo.pAPdb.pFilePath;
	//strcpy_s(m_DCMetaAppConnSet.connectSetting[AP_META].pDbFilePath, , APDB_PATH_BYPASS);
	m_DCMetaAppConnSet.connectSetting[AP_META].pDbFilePath = APDB_PATH_BYPASS;    
	m_DCMetaAppConnSet.connectSetting[AP_META].bConnection = false;

	m_DCMetaAppConnSet.connectSetting[MD_META].bConnection = true;
	m_DCMetaAppConnSet.connectSetting[MD_META].pDbFilePath = g_sMetaComm.sDBFileOption.strMD1Dbpath;
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ArgBootDCMetaMode() : End...");
}

int SmartPhoneSN::APSwitchToModemMeta()
{
	UpdateUIMsg("Start switch to modem meta from ap meta...");
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::APSwithToModemMeta() : Start connectiong MD Meta...");
	int iRet = 0;

	iRet = METAAPP_ApToModemAllInOne_r(m_iThreadID);
	if (iRet == 0)
	{
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToModemMeta():METAAPP_ApToModemAllInOne_r() success!");
	}
	else if(iRet == METAAPP_HIGH_LEVEL_AP_DATABASE_NOT_MATCH || iRet == METAAPP_HIGH_LEVEL_MD_DATABASE_NOT_MATCH)
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToModemMeta(): METAAPP_ApToModemAllInOne_r DB DISMATCH!"); 
		if(!g_sMetaComm.IgnoreDBInconsistent)
			return iRet;
		else
			iRet = 0;
	}
	else
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToModemMeta(): METAAPP_ApToModemAllInOne_r, Err = %d", iRet);
		return iRet;
	}

	if(iRet != 0)
	{
		m_eMetaMode = SP_NOTIN_META;
		//MessageBox(NULL, _T("Connect modem meta fail"), _T("Error"), MB_OK);
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::METAAPP_ApToModemAllInOne_r() : AP To Meta fail, MetaResult = %s", ResultToString(iRet));
	}
	m_eMetaMode = SP_MODEM_META;
	m_bTargetInMetaMode = true;
	m_bStopBeforeUSBInsert = false;

	return iRet;
}

int SmartPhoneSN::APSwitchToDCModemMeta()
{
	UpdateUIMsg("Start switch to modem meta from ap meta...");
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToDCModemMeta() : Start connectiong MD Meta...");
	int iRet = 0;

	iRet = METAAPP_ConnectDCToMDMeta_r(m_iThreadID);
	if (iRet == 0)
	{
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToDCModemMeta():METAAPP_ConnectDCToMDMeta_r() success!");
	}
	else if(iRet == METAAPP_HIGH_LEVEL_AP_DATABASE_NOT_MATCH || iRet == METAAPP_HIGH_LEVEL_MD_DATABASE_NOT_MATCH)
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToDCModemMeta(): METAAPP_ConnectDCToMDMeta_r DB DISMATCH!"); 
		if(!g_sMetaComm.IgnoreDBInconsistent)
			return iRet;
		else
			iRet = 0;
	}
	else
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::APSwitchToDCModemMeta(): METAAPP_ApToModemAllInOne_r, Err = %d", iRet);
		return iRet;
	}

	if(iRet != 0)
	{
		m_eMetaMode = SP_NOTIN_META;
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::METAAPP_ConnectDCToMDMeta_r() : AP To DC Modem Meta fail, MetaResult = %s", ResultToString(iRet));
	}
	m_eMetaMode = SP_MODEM_META;
	m_bTargetInMetaMode = true;
	m_bStopBeforeUSBInsert = false;

	m_sMdInfo.number_of_md = 1;
	m_sMdInfo.active_md_idx = 0;
	m_bWithoutMD = false;
	m_bDualModem = false;

	return iRet;
}

int SmartPhoneSN::ExitMetaMode(bool bpass)
{
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ExitMetaMode(): Exit Meta mode...");
	int iRet = META_SUCCESS;
	bool bBackupNv = true;
	METAAPP_DISCONNECT_META_T disconPara;
	
	if(bpass)
	{
		disconPara.bDoBackupNv = true;
	}
	else
	{
		disconPara.bDoBackupNv = false;
	}

	if (m_bTargetInMetaMode)
	{
		if(g_sMetaComm.bKeepInMeta)
		{			
			disconPara.eDisconPara = METAAPP_Disconnect_DoNothing;
		}
		else
		{
			disconPara.eDisconPara = METAAPP_Disconnect_Poweroff;
		}
		iRet = METAAPP_DisConnectMeta_Ex_r(m_iThreadID,disconPara,sizeof(disconPara));	
		return bBackupNv? iRet:META_FAILED;		
	}
	else
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::ExitMETAMode() : Target not in meta mode!");
		return META_INVALID_ARGUMENTS;
	}
}

int SmartPhoneSN::ExitDCMetaMode(bool bpass)
{
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::ExitMetaMode(): Exit Meta mode...");
	int iRet = META_SUCCESS;
	bool bBackupNv = true;
	METAAPP_DISCONNECT_META_T disconPara;
	if(bpass)
	{
		disconPara.bDoBackupNv = true;
	}
	else
	{
		disconPara.bDoBackupNv = false;
	}
	if (g_sMetaComm.bModemOnly)
	{
		disconPara.bDoBackupNv = false;
	}
	if (m_bTargetInMetaMode)
	{
		if(g_sMetaComm.bAlreadyInMeata != 0)
		{			
			disconPara.eDisconPara = METAAPP_Disconnect_DoNothing;
		}
		else
		{
			disconPara.eDisconPara = METAAPP_Disconnect_Poweroff;

		}
		
		if(g_sMetaComm.eTargetType == THIN_MODEM_FLASHLESS)
		{
			disconPara.eDisconPara = METAAPP_Disconnect_DoNothing;
			disconPara.bDoBackupNv = false;
		}
		iRet = METAAPP_DisConnectDCMeta_r(m_iThreadID,disconPara,sizeof(disconPara));
		return bBackupNv? iRet:META_FAILED;		
	}
	else
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::ExitMETAMode() : Target not in meta mode!");
		return META_INVALID_ARGUMENTS;
	}
}

META_RESULT SmartPhoneSN::REQ_GetCsrFromDut_Start()
{
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_GetCsrFromDut_Start()...");

	int data_in_len = 1024;
	const unsigned char data_in[1024] = {0} ;
	int data_out_len = 5 * 1024;
	unsigned char data_out[5 * 1024] = {0};
	unsigned char dummy_out = 0u;

	META_RESULT meta_result = SP_META_Customer_Func_r(m_hSPMetaHandle,20000,data_in,data_in_len,1,GET_CSR,&dummy_out,data_out,&data_out_len);
	if (meta_result != META_SUCCESS || strcmp((char *)data_out, "") == 0 || dummy_out != 't')
	{
		MTRACE_ERR(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_GetCsrFromDut_Start():SP_META_Customer_Func_r failed(%s)",ResultToString(meta_result));
		UpdateUIMsg("SmartPhoneSN::GetCsrFromDut failed!");
		return META_FAILED;
	}
	MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_GetCsrFromDut_Start():SP_META_Customer_Func_r success");
	
	//Save Csr in PC
	char dir[1024] = {0};
	bool ret = initDirectory(dir);
	if(!ret)
	{
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_GetCsrFromDut_Start():initDirectory fail");
		UpdateUIMsg("REQ_GetCsrFromDut_Start():initDirectory fail!");
		return META_FAILED;
	}
	
	meta_result = SaveCsr(dir, (char *)data_out);
	if(meta_result != META_SUCCESS)
	{
		return META_FAILED;
	}

	return meta_result;
}

bool SmartPhoneSN::initDirectory(char* dir)
{
	/*** get order ***/
	char FolderPath[MAX_PATH];
	char g_pMesSetupFilepath[MAX_PATH];
    memset(&g_pMesSetupFilepath, 0, MAX_PATH);

    ::GetCurrentDirectory(MAX_PATH, FolderPath);
	sprintf_s(g_pMesSetupFilepath, "%s\\%s", FolderPath, "WorkStat.ini");

	char order[64] = {0};
	GetPrivateProfileString("Common", "Order", "unknown", order, 64, g_pMesSetupFilepath);

	/*** Create or Init dir ***/
	time_t nowtime;
	time(&nowtime);
	tm* p = localtime(&nowtime);

    if(strcmp(g_sMetaComm.sWriteOption.strCsrSavePath, "") == 0)
    {
        sprintf(dir, "%s\\Script", FolderPath);
        DWORD attr = GetFileAttributesA(dir);
        if (attr == INVALID_FILE_ATTRIBUTES || !(attr & FILE_ATTRIBUTE_DIRECTORY)) {
            if (!CreateDirectoryA(dir, NULL)) {
                MessageBoxA(NULL, "创建Script目录失败！", "错误", MB_ICONERROR | MB_OK);
                return false;
            }
        }

        sprintf(dir, "%s\\DutCsr", dir);
        attr = GetFileAttributesA(dir);
        if (attr == INVALID_FILE_ATTRIBUTES || !(attr & FILE_ATTRIBUTE_DIRECTORY)) {
            if (!CreateDirectoryA(dir, NULL)) {
                MessageBoxA(NULL, "创建DutCsr目录失败！", "错误", MB_ICONERROR | MB_OK);
                return false;
            }
        }
	}
	else
	{
        DWORD attr = GetFileAttributesA(g_sMetaComm.sWriteOption.strCsrSavePath);
        if (attr == INVALID_FILE_ATTRIBUTES || !(attr & FILE_ATTRIBUTE_DIRECTORY)) {
            if (!CreateDirectoryA(g_sMetaComm.sWriteOption.strCsrSavePath, NULL)) {
                MessageBoxA(NULL, "创建自定义保存目录失败！", "错误", MB_ICONERROR | MB_OK);
                return false;
            }
        }
        sprintf(dir, "%s\\Script", g_sMetaComm.sWriteOption.strCsrSavePath);
        attr = GetFileAttributesA(dir);
        if (attr == INVALID_FILE_ATTRIBUTES || !(attr & FILE_ATTRIBUTE_DIRECTORY)) {
            if (!CreateDirectoryA(dir, NULL)) {
                MessageBoxA(NULL, "创建Script目录失败！", "错误", MB_ICONERROR | MB_OK);
                return false;
            }
        }
    }

    sprintf(dir, "%s\\%s_%04d_%02d_%02d", dir, g_sMetaComm.strOrder,
        p->tm_year + 1900, p->tm_mon + 1, p->tm_mday);
    DWORD attr = GetFileAttributesA(dir);
    if (attr == INVALID_FILE_ATTRIBUTES || !(attr & FILE_ATTRIBUTE_DIRECTORY)) {
        if (!CreateDirectoryA(dir, NULL)) {
            MessageBoxA(NULL, "创建最终目录失败！", "错误", MB_ICONERROR | MB_OK);
            return false;
        }
    }

    return true;
}

META_RESULT SmartPhoneSN::SaveCsr(char* dir, char* Csr)
{
	FILE* fp;
	char filename[2048] = {0};

	META_RESULT MetaResult;
	char szReadBackSN[BARCODE_ARRAY_LEN];
	int iReadBackSNLen = sizeof(szReadBackSN);
	memset((void*)szReadBackSN, 0, iReadBackSNLen);

	bool resultflag = true;

	AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
	AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
	PRODUCT_INFO* product_info;

	memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
	memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

	sNVRAM_ReadReq.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
	sNVRAM_ReadReq.RID = 1;
	sNVRAM_ReadCnf.len = sizeof(PRODUCT_INFO);
	sNVRAM_ReadCnf.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));

	MetaResult =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
	if (MetaResult != META_SUCCESS )
	{
		MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SaveCsr() REQ_ReadFromAPNVRAM failed !");
		UpdateUIMsg("SmartPhoneSN::SaveCsr() REQ_ReadFromAPNVRAM failed !");
		return META_FAILED;
	}

	product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf.buf;
		
	memset((void*)szReadBackSN, 0, iReadBackSNLen);
	strcpy(szReadBackSN, (char *)product_info->target_info.ADBSeriaNo);

	char * p = strchr(szReadBackSN,' ');
	if (p)
	{
		*p = 0;
	}

	if(strcmp(szReadBackSN, "") == 0)
	{
		sprintf(szReadBackSN, "unknown");
	}

	sprintf(filename, "%s\\%s.json", dir, szReadBackSN);
    fp = fopen (filename, "w");
    if (NULL == fp)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::REQ_GetCsrFromDut_Start() fopen %s failed !", filename);
		UpdateUIMsg("REQ_GetCsrFromDut_Start() fopen %s failed !", filename);
        return META_FAILED;
    }
    size_t csrLen = strlen(Csr);
    size_t writtenBytes = fwrite(Csr, 1, csrLen, fp);
    fclose(fp);

    if (writtenBytes != csrLen)
    {
        MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::SaveCsr() fwrite failed! Expected: %zu, Written: %zu", csrLen, writtenBytes);
        UpdateUIMsg("SaveCsr() fwrite failed! Expected: %zu bytes, Written: %zu bytes", csrLen, writtenBytes);
        return META_FAILED;
    }

	if(g_sMetaComm.sWriteOption.bUploadCsrToYN)
	{
		MetaResult = UploadCsrContent(Csr, szReadBackSN);
		if (MetaResult != META_SUCCESS )
		{
			MTRACE(g_hEBOOT_DEBUG, "SmartPhoneSN::UploadCsrContent() failed !");
			return META_FAILED;
		}
	}

	return META_SUCCESS;
}

void stringToHex(const char* str, char* hexStr) {
    while (*str) {
        sprintf(hexStr, "%s%02X", hexStr, (unsigned char)*str);
        str++;
        hexStr += 2; // 移动到下一个位置，留出空格的位置
    }
}

META_RESULT SmartPhoneSN::UploadCsrContent(char* Csr, char* sn)
{
	META_RESULT MetaResult;
	//add get uuid
	unsigned char chipid[512] = {0};
	const unsigned int chipid_len = 16;
	MetaResult = META_MISC_GetRID_r(m_hSPMetaHandle, 5000, chipid, chipid_len);
	if (MetaResult == META_SUCCESS) 
	{
		MTRACE(g_hEBOOT_DEBUG, "META_MISC_GetRID_r(): success! ChipId = %s", chipid);
	}
	else
	{
		MTRACE(g_hEBOOT_DEBUG, "META_MISC_GetRID_r(): fail, MetaResult = %s", ResultToString_SP(MetaResult));
		UpdateUIMsg("META_MISC_GetRID_r(): fail, MetaResult = %s", ResultToString_SP(MetaResult));

		//
		unsigned char temp[512] = {0};
		memset(chipid, 0x00, sizeof(chipid));
		MetaResult = SP_META_GetChipID_r(m_hSPMetaHandle, 5000, temp);
		if (MetaResult == META_SUCCESS) 
		{
			stringToHex((char *)temp, (char *)chipid);
			MTRACE(g_hEBOOT_DEBUG, "SP_META_GetChipID_r(): success! ChipId = %s", chipid);
		}
		else
		{
			MTRACE(g_hEBOOT_DEBUG, "SP_META_GetChipID_r(): fail, MetaResult = %s", ResultToString_SP(MetaResult));
			//wifionly或未加权限的项目无法获取chipid,故不进行报错返回
			/*UpdateUIMsg("SP_META_GetChipID_r(): fail, MetaResult = %s", ResultToString_SP(MetaResult));
			return MetaResult;*/
		}
		//
	}
	//add get uuid end

	//get model
	char model[64] = {0};
	BUILD_PROP_REQ_S build_prop_key;
	BUILD_PROP_CNF_S build_prop_value;

	memset(&build_prop_key, 0, sizeof(BUILD_PROP_REQ_S));
	memset(&build_prop_value, 0, sizeof(BUILD_PROP_CNF_S));
	strcpy((char*)(build_prop_key.tag), "ro.product.model");
	MetaResult = SP_META_GetTargetBuildProp_r(m_hSPMetaHandle, &build_prop_key, &build_prop_value);
	if(MetaResult != META_SUCCESS)
	{
		UpdateUIMsg("SP_META_GetTargetBuildProp_r(): fail, MetaResult = %s", ResultToString_SP(MetaResult));
		return MetaResult;
	}
	sprintf_s(model, sizeof(model), "%s", build_prop_value.content);
	//

	//get fingerprint
	char fingerprint[256] = {0};
	string str_fingerprint = Csr;
	str_fingerprint = str_fingerprint.substr(0, str_fingerprint.find("\",\"csr\":"));
	str_fingerprint = str_fingerprint.substr(str_fingerprint.find(":\"")+2);
	sprintf_s(fingerprint, sizeof(fingerprint), "%s", str_fingerprint.c_str());

	/*Base64 URL 需要特殊处理密文:
	1)去除尾部的"="
    2)把"+"替换成"-"
    3)把"/"替换成"_"
	*/
	string content, tempcontent;
	content = tempcontent = Csr;
	int start_pos = tempcontent.find("csr\":")+6;
	tempcontent = tempcontent.substr(start_pos);
	int end_pos = tempcontent.find("\"") + start_pos;
	tempcontent = tempcontent.substr(0, tempcontent.find("\""));

	char csrcontent[ 5 * 1024] = {0};
	sprintf_s(csrcontent, sizeof(csrcontent), "%s", tempcontent.c_str());
	for(int i=0;i<strlen(csrcontent);i++)
	{
		if(csrcontent[i] == '+')
			csrcontent[i] = '-';
		else if (csrcontent[i] == '/')
			csrcontent[i] = '_';
	}

	if ('=' == csrcontent[strlen(csrcontent) - 2])
	{
		csrcontent[strlen(csrcontent) - 2] = '\0';
	}
	if ('=' == csrcontent[strlen(csrcontent) - 1])
	{
		csrcontent[strlen(csrcontent) - 1] = '\0';
	}

	char pInput[ 5 * 1024] = {0};
	sprintf_s(pInput, sizeof(pInput), "%s%s%s", content.substr(0, start_pos).c_str(), csrcontent, content.substr(end_pos).c_str());
	//

	HttpApi apiproxy;
	char error[1024] = {0};
	bool ret = apiproxy.UploadCsrToZentao(sn, model, fingerprint, (char *)chipid, pInput, error);
	if(!ret)
	{
		UpdateUIMsg("UploadCsrToZentao(): fail, errorInfo = %s", error);
		return META_FAILED;
	}

	return META_SUCCESS;
}
