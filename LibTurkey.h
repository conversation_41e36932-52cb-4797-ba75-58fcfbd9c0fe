#pragma once
#ifndef TURKEY_H_
#define TURKEY_H_

#include <Windows.h>

#include <stdint.h>
#include <exception>
#include <iostream>
#include <functional>
#include <string>
#include <map>

#include <LibKpaUtil.h>

#ifdef LIBTURKEY_EXPORTS
#define LIBTURKEY_API __declspec(dllexport)
#else
#define LIBTURKEY_API __declspec(dllimport)
#endif

namespace trustkernel {

    class Socket;

    class LIBTURKEY_API GenericDevice {
    protected:
        Socket *socket;
    public:
        GenericDevice(const std::wstring &port, const std::string &name = "");
        GenericDevice(uint32_t vid, uint32_t pid, const std::string &name = "");
        ~GenericDevice();
        class IllegalResponse : public std::exception {
            std::string command;
            std::string stage;
            std::string resp;
            std::string _what;

        public:
            IllegalResponse(const std::string &command, const std::string &stage,
                std::string &resp);
            IllegalResponse(const IllegalResponse &other);
#if __cplusplus >= 201103L
            IllegalResponse(IllegalResponse &&other);
#endif
            const char *what() const override { return _what.c_str(); }
        };
        void Reboot();
        std::map<std::string, std::string> Status();
        std::string GetLog();
        void SendBuffer(const char *cmd, const char *stage, const std::string &buf,
            DWORD blockSize);
        void SendBuffer(const char *cmd, const char *stage,
            std::string::const_iterator p,
            std::string::const_iterator pend, DWORD blockSize);
    };

    class LIBTURKEY_API Bootloader : public GenericDevice {
        static const int DOWNLOAD_BLOCK_SIZE = 4096;

    public:
        typedef std::function<std::string(const std::string &, size_t)>
            ResetSignFunc;
        Bootloader();
        void Boot();
        void Download(const std::string &binary);
        void DownloadVersion(uint32_t version);
        uint32_t GetAppVersion();
        void DownloadSignature(const std::string &sig);
        bool VerifyApp();
        void Reset(ResetSignFunc signFunc);
        static bool Exist();
    };

    class LIBTURKEY_API Turkey : public GenericDevice {
        static const int SIGN_BLOCK_SIZE = 256;

    public:
        typedef std::function<std::string(const std::string &, size_t)>
            ResetSignFunc;
        typedef std::function<std::string(const std::string &)> PullRequestFunc;
        Turkey();
        std::string Export(size_t command);
        size_t GetLicense();
        size_t GetVersion();
        void SetKeyId(uint32_t keyid);
        void ReadBMP(std::string *buf);
        void ReadBMPUnsigned(std::string *buf);
        void ReadSign(std::string *buf);
        void ReadKeybox(std::string *buf);
        void Request();
        void Charge(size_t count,
            std::string (*sig_func)(const std::string &nonce, size_t count,
            size_t siglen));
        std::string Sign(const std::string &msg);
        void Reset(ResetSignFunc signFunc);
        std::string Push(const std::string &pre_push);
        std::string PrePush();
        void Pull(uint32_t license, const std::string &keyboxId,
            PullRequestFunc func);
        std::string GetUUID();
        static bool Exist();
    };

#pragma warning(push)
#pragma warning(disable:4251)
    class LIBTURKEY_API AppImage {
        uint32_t version;
        uint32_t app_version;
        std::string app_sig;
        std::string app;

    public:
        AppImage();
        AppImage(const char *filename);
        void DownloadTo(Bootloader &bootloader);
        inline std::string GetApp() const { return app; }
        inline uint32_t GetAppVersion() const { return app_version; }
        inline std::string GetSig() const { return app_sig; }
    };

    class LIBTURKEY_API COMUnknown: public std::exception {
    public:
        COMUnknown() : std::exception("Unknown COM error"){};
    };

    class LIBTURKEY_API COMNotFound : public std::exception {
    public:
        COMNotFound() : std::exception("Requested device not found"){};
        COMNotFound(const std::wstring &name);
    };

    class LIBTURKEY_API COMReadError: public std::exception {
    public:
        COMReadError() : std::exception("Read COM error") {};
        COMReadError(const std::wstring &name);
    };

    class LIBTURKEY_API COMTimeout : public std::exception {
    public:
        COMTimeout() : std::exception("Timeout"){};
        COMTimeout(char *msg) : std::exception(msg){};
    };

    class LIBTURKEY_API TurkeyLicenser: public trustkernel::Licenser {
    private:
        Turkey *turkey;
        int keyId;
        void setKeyId(uint32_t keyId);
        int turkey_rsasha256_sign(void *msg, size_t msg_len, void *hdr, size_t hdr_size,
            unsigned char *signature);
    public:
        TurkeyLicenser(Turkey *turkey, int keyId);
        ~TurkeyLicenser() { }

        virtual int issueSecondaryLicense(trustkernel::Device *device,
                                          unsigned char *buf,
                                          size_t buf_len);
    private:
        static Turkey *g_turkey;

    public:
        static Turkey *getTurkey();
    };

    std::wstring LIBTURKEY_API GetPortName(unsigned int vid, unsigned int pid);
    std::wstring LIBTURKEY_API GetTurkeyPortName();

#pragma warning(pop)
} // namespace trustkernel

#endif
