#pragma once

#include <Windows.h>
#include <afx.h>
class HTMesProxy
{
public:
	~HTMesProxy(void);

	static HTMesProxy * inst();
	BOOL DBHelper_Library_DynamicLoad();
	BOOL DB_LoadScx(char *error);
	void DB_CloseDBConnect();
	int DB_GetzzCheck(char *Dh,char *Sn,int xh,char *ErrorInfo);
	int DB_GetzzStatus(char *Sn,char *Xh,char *Dh,int Gxxh,char *ErrorInfo);
	int DB_UpdatezzStatus(char *Sn,char *Xh,char *Dh,int Gxxh,char *ErrorInfo,char *Czr,char *Scx);
	bool DB_GetXhbz(char *Dh,char *Bar,char *Xhbz, char* errorMessage);
	bool DB_GetImei3(char *Dh,char *Bar,char *Imei1,char *Imei2,char *Meid1,char *Meid2,char *Msn,char *Psn,char *Bt,char *Wifi, 
						char *rom, char *ram, char *row, char *tacode, char *wallpaper_id, char *skuid, char *hefflag, char *elabel_name, char *sw_version, char* errorMessage);
	bool DB_UpdateImeiXh(char *Imei1, char* errorMessage);
	bool DB_GetChbz(char *Dh,char *Bar,char *Xhbz, char* errorMessage);
	bool DB_UpdateImeiCh(char * Dh,char *Xh,char *Imei1,char *Imei2,char *Meid1,char *Meid2,char *Msn,char *Psn,char *Wifi,char *Bt,char *Scx,char *Czr, char* errorMessage);

private:
	HTMesProxy(void);

public:
	char *m_Scx;
	char *m_Dh;
	char *m_Xh;


};