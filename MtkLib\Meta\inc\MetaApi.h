/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef CMETAAPI_H
#define CMETAAPI_H

#include <sstream>
#include "meta.h"

// Test META API interface
class CMETAApi
{
    static int count;
    static std::string getFilename(std::string filename);
public:
    CMETAApi() {}
    virtual ~CMETAApi() {}
    virtual META_RESULT API_META_MMRf_EN_Dynamic_Query_V9_DQ_cnt_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EN_QueryConfigV9 *req, MMRfTestResult_EN_QueryConfigV9 *cnf);
    virtual META_RESULT API_META_MMRf_QueryCarKitMappingInfoV7_DS_cnt_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestCmdQueryCarKitMappingInfoReq *req, MMRfTestCmdQueryCarKitMappingInfoCnfV7_DS_Cnt_T *cnf);
    virtual META_RESULT API_META_MMRf_QueryCarKitMappingInfoV9_DS_cnt_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestCmdQueryCarKitMappingInfoReq *req, MMRfTestCmdQueryCarKitMappingInfoCnfV9_DS_Cnt_T *cnf);
    virtual META_RESULT API_META_MMRf_EN_Dynamic_Query_V9_DQ_data_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EN_QueryConfigV9 *req, MMRfTestResult_EN_QueryConfigV9 *cnf);
    virtual META_RESULT API_META_MMRf_QueryCarKitMappingInfoV7_DS_DataPtr_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestCmdQueryCarKitMappingInfoReq_DS_T *req, MMRfTestCmdQueryCarKitMappingInfoCnfV7_DS_DataPtr_T *cnf, MMRfTestCmdQueryCarKitMappingInfoCnfV7_DS_Cnt_T *cnt);
    virtual META_RESULT API_META_MMRf_QueryCarKitMappingInfoV9_DS_DataPtr_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestCmdQueryCarKitMappingInfoReqV9_DS_T *req, MMRfTestCmdQueryCarKitMappingInfoCnfV9_DS_DataPtr_T *cnf, MMRfTestCmdQueryCarKitMappingInfoCnfV9_DS_Cnt_T *cnt);
    virtual META_RESULT API_META_MMRf_QueryAntInfo_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_En_Query_Ant_Info_Request *req, MMRfTestCmd_En_Query_Ant_Info_Cnfirm *cnf);
    virtual META_RESULT API_META_QueryIfFunctionSupportedByTarget_r(const int meta_handle, unsigned int ms_timeout, const char *query_func_name);
    virtual META_RESULT API_META_MMRf_GetRfCapability_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdRfCapabilityReq *req, const unsigned int requestLength, MMRfTestCmdRfCapabilityCnf *cnf, const unsigned int responseLength);
    virtual META_RESULT API_META_NRf_GetRfCapability_r(const int meta_handle, const unsigned int ms_timeout, const NRfTestCmdRfCapabilityReq *req, const unsigned int requestLength, NRfTestCmdRfCapabilityCnf *cnf, const unsigned int responseLength);
    virtual META_RESULT API_META_MMRf_EN_Rssi_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnCwRssiV9_ReqParam *req, MMRfTestCmd_EnCwRssiV9_CnfPdu *cnf);
    virtual META_RESULT API_META_ERf_GetRfCapability_r(const int meta_handle, const unsigned int ms_timeout, const ERfTestCmdRfCapabilityReq *req, const unsigned int requestLength, ERfTestCmdRfCapabilityCnf *resp, const unsigned int responseLength);
    virtual META_RESULT API_META_MMRf_QueryCarKitMappingInfo_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestCmdQueryCarKitMappingInfoReq *req, MMRfTestCmdQueryCarKitMappingInfoCnf *cnf);
    virtual META_RESULT API_META_MMRf_AfcAacCal_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestResultAfcAacCal *cnf);
    virtual META_RESULT API_META_MMRf_RxAfcCal_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_EN_RxAfcCal_ReqParam *reqLocal, MMRfTestCmd_EnRxAfcCal_CnfParam *cnf);
    virtual META_RESULT API_META_ERf_FhcAfcTxCal_r(const int meta_handle, const unsigned int ms_timeout, const ERfTestCmd_FhcAfcTxCal_ReqParam *req, const ERfTestCmd_FhcAfcTxCal_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_AfcSingleToneTx_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmdAfcSingleToneTx *reqLocal);
    virtual META_RESULT API_META_ERf_AfcSingleToneTx_r(const int meta_handle, const ERfTestCmdAfcSingleToneTx *req, const unsigned int ms_timeout);
    virtual META_RESULT API_META_MMRf_GetAfcSetting_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestResultGetAfcSetting *cnf);
    virtual META_RESULT API_META_MMRf_GetAfcSettingV2_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestResultGetAfcSettingV2 *cnf);
    virtual META_RESULT API_META_MMRf_GetAfcSettingV3_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestResultGetAfcSettingV3 *cnf);
    virtual META_RESULT API_META_MMRf_SetAfcSetting_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdSetAfcSetting *req);
    virtual META_RESULT API_META_MMRf_SetAfcSettingV2_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdSetAfcSettingV2 *req);
    virtual META_RESULT API_META_MMRf_SetAfcSettingV3_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdSetAfcSettingV3 *req);
    virtual META_RESULT API_META_MMRf_EN_GetTPWO_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnGetTPWO *req, MMRfTestResult_EnGetTPWO *cnf);
    virtual META_RESULT API_META_MMRf_EN_SetTPWO_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnSetTPWO *req);
    virtual META_RESULT API_META_MMRf_EN_GetTRxTempCompensation_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnGetTrxTempComp_V9 *req, MMRfTestResult_EnGetTrxTempComp_V9 *cnf);
    virtual META_RESULT API_META_MMRf_EN_GET_TRX_RF_TUNNING_DATA_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_EnGetTrxRfTunningData *req, MMRfTestResult_EnGetTrxRfTunningData *cnf);
    virtual META_RESULT API_META_MMRf_EN_SET_TRX_RF_TUNNING_DATA_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_EnSetTrxRfTunningData *req, MMRfTestCmd_En_Set_Trx_Rf_Tuning_Data_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_EN_WRITE_CAL_INIT_DATA_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_En_Write_Cal_Init_Data_ReqParam *req, MMRfTestCmd_En_Write_Cal_Init_Data_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_EN_GetPCMaxCustomSetting_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_EnGetPcmaxCustomSetting_ReqParam *reqLocal, MMRfTestCmd_EnGetPcmaxCustomSetting_CnfParam *cnfLocal, void *cnfBuf, const unsigned int cnfBufSize);
    virtual META_RESULT API_META_MMRf_EN_SetPCMaxCustomSetting_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_EnSetPcmaxCustomSetting_ReqParam *reqLocal, const void *reqBuf, const unsigned int reqBufSize, MMRfTestCmd_EnSetPcmaxCustomSetting_CnfParam *cnfLocal);
    virtual META_RESULT API_META_MMRf_EN_GetRxGainInfo_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnGetRxGainInfoV9_ReqParam *req, MMRfTestCmd_EnGetRxGainInfoV9_Cnf *cnf);
    virtual META_RESULT API_META_NRf_TxPusch_V8(const int meta_handle, const unsigned int ms_timeout, const NRfTestCmd_StartPuschMultiClusterTxCa_ReqParam *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_NRf_GetPuschTxDLFreq_V7_r(const int meta_handle, const unsigned int ms_timeout, const  NRfTestCmd_GetDlFreqV7 *req, NRfTestResult_GetDlFreqV7 *dlFrequency);
    virtual META_RESULT API_META_MMRf_EN_StartSingleToneTx_V7_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_En_SartTxCwToneV7 *req);
    virtual META_RESULT API_META_MMRf_EN_FetchTxPdValue_V7_r(const int meta_handle, const unsigned int ms_timeout, const MMTST_RAT_E rat, short *pdValue);
    virtual META_RESULT API_META_MMRf_EnQueryCaUsageByRoute_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_QueryCompRouteCAUsage_ReqParam *reqLocal, MMRfTestCmd_QueryCompRouteCAUsage_CnfParam *cnfLocal, MMRfTestCmd_QueryCompRouteCAUsage_CnfPdu *cnfPeer);
    virtual META_RESULT API_META_MMRf_EN_Set_Pa_Table_Idx_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnSetPaTableIdx_ReqParam *req);
    virtual META_RESULT API_META_NRf_StartMixRx_V7_r(const int meta_handle, const unsigned int ms_timeout, const  NRfTestCmd_StartMixRxCaV7 *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_MMRf_EN_SetMultiTRxCalData_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_En_Set_Get_Multi_CalData_ReqParam *reqLocal, const void *reqBuf, const unsigned int reqBufSize, MMRfTestCmd_En_Set_Get_Multi_CalData_CnfParam *cnfLocal);
    virtual META_RESULT API_META_MMRf_EN_GetMultiTRxCalData_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_En_Set_Get_Multi_CalData_ReqParam *reqLocal, const void *reqBuf, const unsigned int reqBufSize, MMRfTestCmd_En_Set_Get_Multi_CalData_CnfParam *cnfLocal, void *cnfBuf, const unsigned int cnfBufSize);
    virtual META_RESULT API_META_NRf_GetMixRxReport_V7_r(const int meta_handle, const unsigned int ms_timeout, const NRfTestResult_GetMixRxReportV7 *cnf);
    virtual META_RESULT API_META_NRf_ResetCounter_r(const int meta_handle, const unsigned int ms_timeout);
    virtual META_RESULT API_META_MMRf_EN_ContRx_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnStartCwContRxV9_ReqParam *req);
    virtual META_RESULT API_META_NRf_StartSensRxEx_V7_r(const int meta_handle, unsigned int ms_timeout, const NRfTestCmd_SensitivityQueryEx_ReqParam *req, NRfTestCmd_SensitivityQuery_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_EN_Band_Info_Dynamic_Query_cnt_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EN_Band_QueryConfig *req, MMRfTestResult_EN_Band_QueryConfig *cnf);
    virtual META_RESULT API_META_MMRf_EN_Band_Info_Dynamic_Query_data_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EN_Band_QueryConfig *req, MMRfTestResult_EN_Band_QueryConfig *cnf);
    virtual META_RESULT API_META_ERf_StopTestMode_r(const int meta_handle, const unsigned int ms_timeout);
    virtual META_RESULT API_META_NRf_TestStop_V7_r(const int meta_handle, const unsigned int ms_timeout);
    virtual META_RESULT API_META_MMRf_QueryAdjustedTxPowerInfo_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnQueryTxPowerOffsetInfo_ReqParam *req, const void *reqBuf, const int reqBufSize, MMRfTestCmd_EnQueryTxPowerOffsetInfo_CnfParam *cnf, void *cnfBuf, const int cnfBufSize);
    virtual META_RESULT API_META_NRf_TxPusch_V7_r(const int meta_handle, const unsigned int ms_timeout, const  NRfTestCmd_StartPuschTxCaV7 *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_ERf_TxCfgUpdate_r(const int meta_handle, unsigned int ms_timeout, const ERfTestCmdTxCfgUpdt *req);
    virtual META_RESULT API_META_ERf_GetMixRxReport_CaMode_V5_r(const int meta_handle, const unsigned int ms_timeout, ERfTestResultGetMixRxRpt_CaMode_V5 *resp);
    virtual META_RESULT API_META_NRf_Sfft_List_Mode_Report_Query_V9_r(const int meta_handle, const unsigned int ms_timeout, unsigned short *stepTypeArray, unsigned int stepCount, NL1TSTCmd_Sfft_List_CnfParam *cnf, void *cnfBuf, const int cnfBufSize, unsigned char *reqBuf);
    virtual META_RESULT API_META_NRf_Rx_Trigger_Sfft_List_Mode_Start_r(const int meta_handle, const unsigned int ms_timeout, const NL1TSTCmd_Sfft_List_Rx_Trigger_ReqParam *req, const void *reqBuf, const int reqBufSize, NL1TSTCmd_Sfft_List_Rx_Trigger_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_Sfft_List_Mode_Start_V8_r(const int meta_handle, const unsigned int ms_timeout, const NL1TSTCmd_Sfft_List_ReqParam *req, const void *reqBuf, const int reqBufSize, NL1TSTCmd_Sfft_List_CnfParam *cnf, void *cnfBuf, const int cnfBufSize);
    virtual META_RESULT API_META_ERf_SfftListModeStart_V8_r(const int meta_handle, const unsigned int ms_timeout, const EL1TSTCmd_Sfft_List_ReqParam *req, const void *reqBuf, const int reqBufSize, EL1TSTCmd_Sfft_List_CnfParam *cnf, void *cnfBuf, const int cnfBufSize);
    virtual META_RESULT API_META_ERf_SfftListModeReportQuery_V9_r(const int meta_handle, const unsigned int ms_timeout, unsigned short *stepTypeArray, unsigned int stepCount, EL1TSTCmd_Sfft_List_CnfParam *cnf, void *cnfBuf, const int cnfBufSize, unsigned char *reqBuf);
    virtual META_RESULT API_META_ERf_RxTriggerSfftListModeStart_r(const int meta_handle, const unsigned int ms_timeout, const EL1TSTCmd_Sfft_List_Rx_Trigger_ReqParam *req, const void *reqBuf, const int reqBufSize, EL1TSTCmd_Sfft_List_Rx_Trigger_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_QueryCoTmsDegree_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestCmd_QueryCotmsDegreeC_ReqParam *req, MMRfTestCmd_QueryCotmsDegreeC_CnfParam *cnf);
    virtual META_RESULT API_META_3Grf_TestStop_r(const int meta_handle, unsigned int ms_timeout, URfTestResultParam *cnf);
    virtual META_RESULT API_META_MMRf_Query_RxCotms_FoeAndDegreeC_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_QueryRxCotmsFoeAndDegreeC_ReqParam *reqLocal, MMRfTestCmd_QueryRxCotmsFoeAndDegreeC_CnfParam *cnf);
    virtual META_RESULT API_META_3Grf_TxDpchV7_r(const int meta_handle, unsigned int ms_timeout, URfTestCmdTxDPChV7 *req, URfTestResultParam *cnf);
    virtual META_RESULT API_META_3Grf_TxDpch_r(const int meta_handle, unsigned int ms_timeout, URfTestCmdTxDPCh *req, URfTestResultParam *cnf);
    virtual META_RESULT API_META_ERf_TxPusch_V5_r(const int meta_handle, const unsigned int ms_timeout, const ERfTestCmd_StartPuschTxCaV5_ReqParam *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_MMRf_XtalTmsCalibrated_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestResultXtalTmsCalibrated *cnf);
    virtual META_RESULT API_META_MMRf_GetCoTmsDataV3_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestResultGetCoTmsDataV3 *cnf);
    virtual META_RESULT API_META_ERf_TxPusch_V2_r(const int meta_handle, const unsigned int ms_timeout, const ERfTestCmdPuschTxV2 *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_ERf_QueryTempAuxAdc_r(const int meta_handle, const unsigned int ms_timeout, ERfResultTempAuxAdc *cnf);
    virtual META_RESULT API_META_3Grf_UbinModeSetup_r(const int meta_handle, unsigned int ms_timeout, const unsigned char ubin_fdd_mode_init);
    virtual META_RESULT API_META_MMRf_SetCoTmsData_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdSetCoTmsData *req);
    virtual META_RESULT API_META_3Grf_SetCapId_r(const int meta_handle, unsigned int ms_timeout, unsigned int capId);
    virtual META_RESULT API_META_3Grf_LowPowerCalV3_r(const int meta_handle, unsigned int ms_timeout, const URfTestCmdLowPowerCal *req);
    virtual META_RESULT API_META_3Grf_TxDpchFixGain_r(const int meta_handle, unsigned int ms_timeout, const URfTestCmdTxDPChFixGain *req);
    virtual META_RESULT API_META_ERf_SetCoTmsData_r(const int meta_handle, const ERfCoTmsData *req, const unsigned int ms_timeout);
    virtual META_RESULT API_META_Rf_MultiSlot_TX_Ex1_r(const int meta_handle, unsigned int ms_timeout, const RfMultiSlotTX_Req *req);
    virtual META_RESULT API_META_MMRf_HeaterControl_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdHeaterControl *req);
    virtual META_RESULT API_META_MMRf_SetCoTmsDataV3_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdSetCoTmsDataV3 *req);
    virtual META_RESULT API_META_Rf_Stop_Ex_r(const int meta_handle, unsigned int ms_timeout);
    virtual META_RESULT API_META_ERf_TxSfftPusch_V7_r(const int meta_handle, const unsigned int ms_timeout, const ERfTestCmd_StartSfftPuschTxV7_ReqParam *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_ERf_StartSensRxEx_V7_r(const int meta_handle, const unsigned int ms_timeout, const ERfTestCmd_SensitivityQueryEx_ReqParam *req, ERfTestCmd_SensitivityQuery_CnfParam *cnf);
    virtual META_RESULT API_META_ERf_StartMixRx_CaMode_V5_r(const int meta_handle, const unsigned int ms_timeout, const ERFTestCmd_StartMixRxCaV5_ReqParam *req);
    virtual META_RESULT API_META_MMRf_SetDpdPattern_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_SetDpdPatternV9_ReqParam *reqLocal, const MMRfTestCmd_SetDpdPatternV9_ReqPdu *reqPeer, MMRfTestCmd_SetDpdPatternV9_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_SetDpd_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_SetDpdV9_ReqParam *reqLocal, const MMRfTestCmd_DpdDataV9_DynSize_T *reqPeer, MMRfTestCmd_SetDpdV9_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_GetDpd_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_GetDpdV9_ReqParam *reqLocal, const MMRfTestCmd_GetDpdV9_ReqPeer *reqPeer, MMRfTestCmd_GetDpdV9_CnfParam *cnfLocal, MMRfTestCmd_DpdDataV9_DynSize_T *cnfPeer);
    virtual META_RESULT API_META_MMRf_StartDpdPaAndDpdFacCal_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_StartDpdFacCalV9_ReqParam *reqLocal, const MMRfTestCmd_StartDpdFacCalV9_ReqPeer *reqPeer, MMRfTestCmd_StartDpdFacCalV9_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_GetDpdBoundary_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_GetDpdBdyV9_ReqParam *reqLocal, const MMRfTestCmd_GetDpdV9_ReqPeer *reqPeer, MMRfTestCmd_GetDpdBdyV9_CnfParam *cnfLocal, MMRfTestCmd_DpdBoundaryDataV9_DynSize_T *cnfPeer);
    virtual META_RESULT API_META_MMRf_SetEt_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_SetEtV9_ReqParam *reqLocal, const MMRfTestCmd_EtDataV9_DynSize_T *reqPeer, MMRfTestCmd_SetEtV9_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_GetEt_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_GetEtV9_ReqParam *reqLocal, const MMRfTestCmd_GetEtV9_ReqPeer *reqPeer, MMRfTestCmd_GetEtV9_CnfParam *cnfLocal, MMRfTestCmd_EtDataV9_DynSize_T *cnfPeer);
    virtual META_RESULT API_META_MMRf_GetEtBoundary_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_GetEtBdyV9_ReqParam *reqLocal, const MMRfTestCmd_GetEtV9_ReqPeer *reqPeer, MMRfTestCmd_GetEtBdyV9_CnfParam *cnfLocal, MMRfTestCmd_EtBoundaryDataV9_DynSize_T *cnfPeer);
    virtual META_RESULT API_META_MMRf_GetEtExpendLog_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_GetEtCalExpendLogV9_ReqParam *reqLocal, const MMRfTestCmd_GetEtCalExpendLogV9_ReqPeer *reqPeer, MMRfTestCmd_GetEtCalExpendLogV9_CnfParam *cnfLocal, MMRfTestCmd_GetEtCalExpendLogV9_CnfPdu *cnfPeer);
    virtual META_RESULT API_META_MMRf_GetEtCalibrationLog_V9_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestCmd_GetEtCalLogV9_ReqParam *req, MMRfTestCmd_GetEtCalLogV9_CnfParam *cnfLocal, MMRfTestCmd_GetEtCalLogV9_CnfPdu *cnfPeer);
    virtual META_RESULT API_META_MMRf_StartEtFacCal_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_StartEtFacCalV9_ReqParam *reqLocal, const MMRfTestCmd_StartEtFacCalV9_ReqPeer *reqPeer, MMRfTestCmd_StartEtFacCalV9_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_GetRfTemp_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdGetRfTemp *req, MMRfTestResultGetRfTemp *cnf);
    virtual META_RESULT API_META_MMRf_GetTadcSetting_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestResultGetTadcSetting *cnf);
    virtual META_RESULT API_META_MMRf_SetTadcSetting_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdSetTadcSetting *req);
    virtual META_RESULT API_META_MMRf_EN_GetRxGainInfoExtend_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnGetRxGainInfoV9_Extend_ReqParam *req, MMRfTestCmd_EnGetRxGainInfoV9_Cnf *cnf);
    virtual META_RESULT API_META_MMRf_GetTpcInfo_2_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_FetchTpcInfo_ReqParam_2 *req, MMRfTestCmd_FetchTpcInfo_CnfParam_2 *cnf);
    virtual META_RESULT API_META_MMRf_WritePowerLimitInfo_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_WritePowerLimit_ReqParam *reqLocal, MMRfTestCmd_WritePowerLimit_ReqPdu *reqPeer, MMRfTestCmd_WritePowerLimit_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_QueryAtcInfo_DQ_cnt_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_En_Query_ATC_ReqParam *req, MMRfTestCmd_En_Query_ATC_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_QueryAtcInfo_DQ_data_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_En_Query_ATC_ReqParam *req, MMRfTestCmd_QueryAtcInfo_DataPtr_T *cnf);
    virtual META_RESULT API_META_MMRf_ForceTasState_V7_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmdForceTasStateV7 *req, MMRfTestResultForceTasStateV7 *cnf);
    virtual META_RESULT API_META_MMRf_EN_StartFixedGainRssi_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_NsftFixedGainRSSI_ReqParam *reqLocal, const void *reqBuf, const int reqBufSize, MMRfTestCmd_NsftFixedGainRSSI_CnfParam *cnfLocal, unsigned char *cnfBuf, const int cnfBufferLen);
    virtual META_RESULT API_META_MMRf_EN_FixedGainRssiGetReport_r(const int meta_handle, unsigned int ms_timeout, MMRfTestCmd_NsftFixedGainRSSI_GetReport_CnfParam *cnf, unsigned char *cnfPeerBufferData, const int cnfPeerBufferLen, unsigned char *reqBuf);
    virtual META_RESULT API_META_MMRf_EN_SetTRxSetting_MRx10_V9_r(const int meta_handle, const unsigned int ms_timeout, const MMRfTestCmd_EnSetTrxSetting_MRx10_V9 *req, MMRfTestResult_EnSetTrxSetting_MRx10_V9_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_FR2_GetChipTemperature_V8_r(int meta_handle, unsigned int ms_timeout, const NL1TST_FR2_GetChipTemperature_ReqParam *req, NL1TST_FR2_GetChipTemperature_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_FR2_GetRfInfo_V8_r(const int meta_handle, unsigned int ms_timeout, NRfTestCmd_FR2_RfInfo *cnf);
    virtual META_RESULT API_META_NRf_FR2_SFFT_List_mode_r(const int meta_handle, const unsigned int ms_timeout, const NL1TST_FR2_SFFT_ReqParam *req, const void *reqBuf, const unsigned int cnfCount, NL1TST_FR2_SFFT_CnfPdu *cnf);
    virtual META_RESULT API_META_NRf_FR2_SFFT_List_mode_V9_r(const int meta_handle, const unsigned int ms_timeout, const NL1TST_FR2_SFFT_ReqParam *req, const void *reqBuf, const unsigned int cnfCount, NL1TST_FR2_SFFT_CnfPdu *cnf);
    virtual META_RESULT API_META_NRf_FR2_QueryAIMInfo_V8_r(const int meta_handle, const unsigned int ms_timeout, NL1TST_FR2_AIMInfo_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_FR2_QueryAIFPortInfo_V9_r(const int meta_handle, const unsigned int ms_timeout, NL1TST_FR2_AIF_Port_Query_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_FR2_FetchTxPdValue_V8_r(const int meta_handle, const unsigned int ms_timeout, NL1TST_FR2_FetchApmRpt_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_QueryAifFrequency_r(const int meta_handle, unsigned int ms_timeout, const NRfTestCmd_QueryIfFrequency_ReqParam *req, NRfTestCmd_QueryIfFrequency_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_FR2_StartMixRx_V8_r(const int meta_handle, const unsigned int ms_timeout, const NRfTestCmd_FR2_StartMixRxCaV8 *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_NRf_FR2_TxPusch_2_V9_r(const int meta_handle, const unsigned int ms_timeout, const NL1TST_FR2_StartPuschTx_ReqParam_3 *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_NRf_FR2_TxPusch_2_V8_r(const int meta_handle, const unsigned int ms_timeout, const NL1TST_FR2_StartPuschTx_ReqParam_2 *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_NRf_FR2_AwvTraining_V8_r(const int meta_handle, unsigned int ms_timeout, const NL1TST_FR2_AwvTraining_ReqParam *req, NL1TST_FR2_AwvTraining_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_Set_Environment_r(const int meta_handle, const unsigned int ms_timeout, MMRfTestCmd_Fr2SetPhoneEnv_ReqParam *req);
    virtual META_RESULT API_META_NRf_QueryFr2DpdConfig_r(const int meta_handle, const unsigned int ms_timeout, NRfTestCmd_Fr2QueryDPDConfig_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_GetFr2DpdFacCalAll_V8_r(const int meta_handle, const unsigned int ms_timeout, const NRfTestCmd_Fr2GetDpdFacCalV8_ReqParam *req, NRfTestCmd_Fr2GetDpdFacCalV8_CnfParam *cnfLocal, NRfTestCmd_Fr2GetDpdFacCalV8_CnfPdu *cnfPeer);
    virtual META_RESULT API_META_NRf_SetFr2DpdFacCal_V8_r(const int meta_handle, const unsigned int ms_timeout, NRfTestCmd_Fr2SetDpdFacCalV8_ReqParam *reqLocal, NRfTestCmd_Fr2SetDpdFacCalV8_ReqPdu *reqPeer, NRfTestCmd_Fr2SetDpdFacCalV8_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_FR2_StartMixRx_V9_r(const int meta_handle, const unsigned int ms_timeout, const NRfTestCmd_FR2_StartMixRxCaV9 *req, unsigned int *pSyncStatus);
    virtual META_RESULT API_META_NRf_GetFR2SelfCalResult(const int meta_handle, const unsigned int ms_timeout, const NL1TST_FR2_GetRfSelfCalResult_ReqParam *reqLocal, NL1TST_FR2_GetRfSelfCalResult_CnfParam *cnfLocal, char *cnfPeer);
    virtual META_RESULT API_META_MMRf_GetTuningDataNvramLid_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_EN_GetTuningDataNvramLid_ReqParam *reqLocal, MMRfTestCmd_EN_GetTuningDataNvramLid_CnfParam *cnfLocal, unsigned char *cnfBuf, const unsigned int cnfBufferLen);
    virtual META_RESULT API_META_MMRf_EN_GetPCMaxSubbandCustomSetting_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_EnGetPcmaxSubbandCustomSetting_ReqParam *reqLocal, MMRfTestCmd_EnGetPcmaxSubbandCustomSetting_CnfParam *cnfLocal, void *cnfBuf, const unsigned int cnfBufSize);
    virtual META_RESULT API_META_MMRf_EN_SetPCMaxSubbandCustomSetting_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_EnSetPcmaxSubbandCustomSetting_ReqParam *reqLocal, const void *reqBuf, const unsigned int reqBufSize, MMRfTestCmd_EnSetPcmaxSubbandCustomSetting_CnfParam *cnfLocal);
    virtual META_RESULT API_META_MMRf_EN_SET_LM_DATA_V9_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_SetLmV9_ReqParam *reqLocal, const void *reqPeer, const unsigned int reqPeerSize, MMRfTestCmd_SetLmV9_CnfParam *cnf);
    virtual META_RESULT API_META_MMRf_EN_GET_LM_DATA_V9_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_GetLmV9_ReqParam *reqLocal, const void *reqPeer, const unsigned int reqPeerSize, MMRfTestCmd_GetLmV9_CnfParam *cnfLocal, void *cnfPeer, unsigned int cnfPeerSize);
    virtual META_RESULT API_META_MMRf_START_LM_FAC_CAL_V9_r(const int meta_handle, unsigned int ms_timeout, const MMRfTestCmd_StartLmFacCalV9_ReqParam *reqLocal, const void *reqPeer, const unsigned int reqPeerSize, MMRfTestCmd_StartLmFacCalV9_CnfParam *cnf);
    virtual META_RESULT API_META_NRf_QueryListModeTimingInfo_r(const int meta_handle, unsigned int ms_timeout, const NL1TSTCmd_CmdlineTimingQuery_ReqParam *req, NL1TSTCmd_CmdlineTimingQuery_CnfParam *cnf, void *cnfBuf);
    virtual META_RESULT API_META_ERf_QueryListModeTimingInfo_r(const int meta_handle, unsigned int ms_timeout, const ERfTestCmd_CmdlineTimingQuery_ReqParam *req, ERfTestCmd_CmdlineTimingQuery_CnfParam *cnf, void *cnfBuf);
};

#endif // CMETAAPI_H
