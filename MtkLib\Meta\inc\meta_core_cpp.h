/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
/*****************************************************************************/
/*!
 * \file meta_core.h
 * \mainpage Mobile Equipment Testing Architecture Development Kit
 * \author MediaTek Inc.
 ******************************************
 * \defgroup META_Result META_RESULT
 * META_RESULT is an enumeration type, which is defined as following. If an exported function of META-DLL has returned value, the type of this returned value is always META_RESULT.
 ******************************************
 *
 * \defgroup General General
 * This section describe the General functions
 *
 * \defgroup GeneralStruct Structure of General
 * \ingroup General
 * GeneralStruct is a subgroup of General
 *
 ******************************************
 *
 * \defgroup InitConn Initialization-Connection
 * This section describe the initialization and connection functions
 *
 * \defgroup InitConnStruct Structure for initialization-connection function
 * \ingroup InitConn
 * InitConnStruct is a subgroup of InitConn
 *
 ******************************************
 *
 * \defgroup MDLogging Modem Logging
 * This section describe the Modem logging functions
 *
 * \defgroup MDLoggingStruct Structure for modem logging functions
 * \ingroup MDLogging
 * MDLoggingStruct is a subgroup of MDLogging
 *
 ******************************************
 * \defgroup MultiMode Multi-Mode
 * This section describes the functions used in multi-mode calibration
 *
 * \defgroup MultiModeStruct Structure for multi-mode functions
 * \ingroup MultiMode
 * MultiModeStruct is a subgroup of MultiMode
 ******************************************
 * \defgroup GSM GSM
 * This section describes the functions used in GSM calibration
 *
 * \defgroup GSMStruct Structure for GSM functions
 * \ingroup GSM
 * GSMStruct is a subgroup of GSM
 ******************************************
 * \defgroup WCDMA WCDMA
 * This section describes the functions used in WCDMA calibration
 *
 * \defgroup WCDMAStruct Structure for WCDMA functions
 * \ingroup WCDMA
 * WCDMAStruct is a subgroup of WCDMA
 ******************************************
 * \defgroup LTE LTE
 * This section describes the functions used in LTE calibration
 *
 * \defgroup LTEStruct Structure of LTE functions
 * \ingroup LTE
 * LTEStruct is a subgroup of LTE
 ******************************************
 * \defgroup NR NR
 * This section describes the functions used in NR calibration
 *
 * \defgroup NRStruct Structure of NR functions
 * \ingroup NR
 * NRStruct is a subgroup of NR
 */
/*==============================================================================*/

#ifndef META_CORE_CPP_H
#define META_CORE_CPP_H

#include<vector>
#include "meta_core.h"
#include "meta_iot_ntn.h"

#ifdef __linux__
#include <stdint.h>
#endif

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] Multi-RAT CA configuration database of LTE/NR TX/RX, used for storing database(Dynamic size database usage)
 */
struct MMRfTestResult_EN_QueryConfigV7_DynSize_T
{
    std::vector<MMTST_TRX_BAND_INFO_T>                  band_info;  /**< TX RX band information */
    std::vector<MMTST_PARTIAL_FERQ_INFO_DYN_ELEMENT_T>  tx_partial_freq_info;  /**< TX partial frequency information */
    std::vector<MMTST_PARTIAL_FERQ_INFO_DYN_ELEMENT_T>  rx_partial_freq_info;  /**< RX partial frequency information */
    std::vector<MMTST_TX_INFO_T>                        tx_info;               /**< TX route information */
    std::vector<MMTST_RX_T1_INFO_T>                     rx_t1_info;            /**< RX-Type1 route information */
    std::vector<MMTST_RX_T2_INFO_T>                     rx_t2_info;            /**< RX-Type2 route information */
    MMTST_RX_GM_INFO_T                                  rx_hpm_gm_info;        /**< RX HPM gain mode information */
    MMTST_RX_GM_INFO_T                                  rx_lpm_gm_info;        /**< RX LPM gain mode information */
    std::vector<MMTST_TX_TO_RX_ROUTE_PAIRING_INFO_T>    tx_to_rx_route_info;    /**< TX/RX route pair information */
    std::vector<MMTST_TX_INFO_V2_T>                     tx_info_ext;            /**< TX route information */
    MMTST_RX_GM_INFO_EXT_T                              rx_hpm_gm_info_ext;     /**< RX HPM gain mode information */
    MMTST_RX_GM_INFO_EXT_T                              rx_lpm_gm_info_ext;     /**< RX LPM gain mode information */
};

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] The confirmation structure for getting maximum TX power(Dynamic size database usage)
 */
typedef struct
{
    std::vector<MMTST_TX_INFO_EXT_T> tx_info;/**< route information */
} MMTST_TX_ROUTE_INFO_EXT_DQ_T;

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] This structure is the confirm message of querying carkit mapping information(Dynamic size database usage)
 */
typedef struct
{
    unsigned char                                 status;                 /**< Query status. */
    MMRfTestParamCmrInfo                          common_info;            /**< Common carkit information. */
    std::vector<MMRfTestParamCarKitInfo_T>        gsm_cmr_indicator;      /**< GSM band and carkit pair information. */
    std::vector<MMRfTestParamCarKitInfo_T>        tdscdma_cmr_indicator;  /**< TDSCDMA band and carkit pair information. */
    std::vector<MMRfTestParamCarKitInfo_T>        c2k_cmr_indicator;      /**< C2K band and carkit pair information. */
    std::vector<MMRfTestParamCarKitInfo_T>        wcdma_cmr_indicator;    /**< WCDMA band and carkit pair information. */
    std::vector<MMRfTestParamCarKitTxRouteInfo_T> lte_tx_cmr_indicator;   /**< LTE TX route and carkit pair information. */
    std::vector<MMRfTestParamCarKitRxRouteInfo_T> lte_rx_cmr_indicator;   /**< LTE RX route and carkit pair information. */
    std::vector<MMRfTestParamCarKitTxRouteInfo_T> nr_tx_cmr_indicator;    /**< NR TX route and carkit pair information. */
    std::vector<MMRfTestParamCarKitRxRouteInfo_T> nr_rx_cmr_indicator;    /**< NR RX route and carkit pair information. */
} MMRfTestCmdQueryCarKitMappingInfoCnfV7_DS;

/**
 * \ingroup MultiModeStruct
 * \details [M90 series] This structure is the confirm message of querying carkit mapping information(Dynamic size database usage)
 */
typedef struct
{
    unsigned char                                 status;                 /**< Query status. */
    MMRfTestParamCmrInfo                          common_info;            /**< Common carkit information. */
    std::vector<MMRfTestParamCarKitInfo_T>        gsm_cmr_indicator;      /**< GSM band and carkit pair information. */
    std::vector<MMRfTestParamCarKitInfo_T>        tdscdma_cmr_indicator;  /**< TDSCDMA band and carkit pair information. */
    std::vector<MMRfTestParamCarKitInfo_T>        c2k_cmr_indicator;      /**< C2K band and carkit pair information. */
    std::vector<MMRfTestParamCarKitInfo_T>        wcdma_cmr_indicator;    /**< WCDMA band and carkit pair information. */
    std::vector<MMRfTestParamCarKitTxRouteInfo_T> lte_tx_cmr_indicator;   /**< LTE TX route and carkit pair information. */
    std::vector<MMRfTestParamCarKitRxRouteInfo_T> lte_rx_cmr_indicator;   /**< LTE RX route and carkit pair information. */
    std::vector<MMRfTestParamCarKitTxRouteInfo_T> nr_tx_cmr_indicator;    /**< NR TX route and carkit pair information. */
    std::vector<MMRfTestParamCarKitRxRouteInfo_T> nr_rx_cmr_indicator;    /**< NR RX route and carkit pair information. */
    std::vector<MMRfTestParamCarKitInfo_V9_T> iot_ntn_cmr_indicator;    /**< IoT-NTN route and carkit pair information. */
} MMRfTestCmdQueryCarKitMappingInfoCnfV9_DS;

/**
 * \ingroup LTEStruct
 * \details [M70 series] The "TX HPUE routes" query info of LTE(Dynamic size database usage)
 */
typedef struct
{
    std::vector<MMTST_GetHpueRouteInfo_T>    *hpue_band_info;    /**< Hupe band information */
} ERfTestCmd_GetHpueRouteInfo_DynSize_T;

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] The currently supported dynamic size confirm of multi-RAT modem information query(Dynamic size database)
 */
typedef struct
{
    unsigned int              info_cnf_bitmap;                                      /**< indicate cnf bitmap, refer to E_EN_DYNAMIC_QUERY_TYPE */
    std::vector<MMTST_TRX_BAND_INFO_T>                  band_info_1;                /**< LTE/NR band information */
    std::vector<MMTST_PARTIAL_FERQ_INFO_DYN_ELEMENT_T>  tx_partial_freq_info_2;     /**< TX partial frequency information */
    std::vector<MMTST_PARTIAL_FERQ_INFO_DYN_ELEMENT_T>  rx_partial_freq_info_3;     /**< RX partial frequency information */
    std::vector<MMTST_TX_INFO_T>                        tx_info_4;                  /**< TX route information */
    std::vector<MMTST_RX_T1_INFO_T>                     rx_t1_info_5;               /**< RX_T1 route information */
    std::vector<MMTST_RX_T2_INFO_T>                     rx_t2_info_6;               /**< RX_T2 route information */
    MMTST_RX_GM_INFO_T                                  rx_hpm_gm_info_7;           /**< RX high gain mode information */
    MMTST_RX_GM_INFO_T                                  rx_lpm_gm_info_8;           /**< RX low gain mode information */
    std::vector<MMTST_TX_TO_RX_ROUTE_PAIRING_INFO_T>    tx_to_rx_route_info_9;      /**< TX/RX route pair information */
    std::vector<MMTST_ROUTE_BLK_INFO_T>                 rx_t1_blk_num_by_route_10;  /**< Rx_T1 block number of LTE/NR route */
    std::vector<MMTST_ROUTE_BLK_INFO_T>                 rx_t2_blk_num_by_route_11;  /**< Rx_T2 block number of LTE/NR route */
    std::vector<MMTST_ROUTE_BLK_INFO_TX_BW_T>           tx_blk_num_by_route_12;     /**< TX block number of LTE/NR route */
    std::vector<MMTST_TX_INFO_EXT_T>                    tx_max_prf_ext_info_13;     /**< TX max pwr per LTE/NR route */
    std::vector<MMTST_GetTxRouteInfo_Element_T>         lte_rftool_rxtxRoute_14;    /**< LTE TX route information */
    std::vector<MMTST_GetHpueRouteInfo_T>               lte_hpue_route_info_15;     /**< LTE hupe route information */
    std::vector<MMTST_LTE_GetMimoBandInfo_T>            lte_mimo_band_info_16;      /**< LTE mimo band information */
    std::vector<MMTST_GetTxRouteInfo_Element_T>         nr_nsft_rxtxRoute_17;       /**< NR nsft TX route information */
    std::vector<MMTST_NR_GetMimoBandInfo_T>             nr_mimo_band_info_18;       /**< NR mino band information */
    std::vector<MMTST_TX_INFO_V2_T>                     tx_info_19;                 /**< TX route information */
    MMTST_RX_GM_INFO_EXT_T                              rx_hpm_gm_info_20;      /**< RX high gain mode information */
    MMTST_RX_GM_INFO_EXT_T                              rx_lpm_gm_info_21;      /**< RX low gain mode information */
    std::vector<MMTST_ROUTE_BLK_INFO_TX_BW_EXT_T>       tx_blk_num_by_route_22;     /**< TX block number of LTE/NR route */
    std::vector<MMTST_TX_INFO_PRF_EXT_T>                tx_max_prf_ext_info_23;     /**< TX max pwr per LTE/NR route */
} MMRfTestResult_EN_QueryConfigV7_DYN;

/**
 * <pre>
 * \ingroup MultiMode
 * \details [M70 series] Query multi-RAT modem information with dynamic size database supported
 * \sa META_MMRf_EN_QueryConfig_V7_r
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  rat the RAT info (LTE: MMTST_RAT_LTE, NR: MMTST_RAT_NR), queried category
 * \param [out] cnf the LTE/NR CA configuration
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 **/
META_RESULT __stdcall META_MMRf_EN_Dynamic_Query_r(const int meta_handle, const unsigned int ms_timeout,
                                                   const MMRfTestCmd_EN_QueryV7_Dynamic *req,
                                                   MMRfTestResult_EN_QueryConfigV7_DYN *cnf);

/**
 * \ingroup MultiModeStruct
 * \details SRS calibration TPC data
 */
typedef struct
{
    std::vector<MMRfSrsCalTpcForRoute> tpc_setting_bynamic;    /**< TPC setting of all routes */
} MMRfTestCmdSetGetSrsCalTpc_DynSize;

/**
 * \ingroup MultiMode
 * \details [M70 series] Set SRS calibration TPC to target.
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the requeset parameter for set SRS calibration TPC data
 * \param [in]  tpc SRS calibration TPC data(Dynamic size database, no size limitation)
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 *
 */
META_RESULT __stdcall META_MMRf_EnSetSrsTxCal_V7_DQ_cnt_r(const int meta_handle, const unsigned int ms_timeout,
                                                          MMRfTestCmdSetSrsCalReq *req,
                                                          MMRfTestCmdSetGetSrsCalTpc_DynSize *tpc);

/**
 * \ingroup MultiModeStruct
 * \details The element of TAS verify vector, used for passing database cross dll(Dynamic size database usage)
 */
typedef struct
{
    unsigned char    status;
    std::vector<MMRfTasVerifyListElement>  gl1_list_indicator;
    std::vector<MMRfTasVerifyListElement>  tl1_list_indicator;
    std::vector<MMRfTasVerifyListElement>  cl1_list_indicator;
    std::vector<MMRfTasVerifyListElement>  ul1_list_indicator;
    std::vector<MMRfTasVerifyListElement>  el1_list_indicator;
    std::vector<MMRfTasVerifyListElement>  nl1_list_indicator;
} MMRfTestResult_EN_QueryTasVerifyListV7_DYN;

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] The confirmation structure of querying EN NSFT route relation command
 */
typedef struct
{
    unsigned short                                 total_route_num; /**< total route number */
    std::vector<MMTST_NSFT_ROUTE_RELATION_INFO_T>  rel_table ;      /**< route relation table */
} MMRfTestCmd_EN_QueryNsftRouteRelation_Cnf;

/**
 * <pre>
 * \ingroup MultiMode
 * \details [M70 series] Query NSFT route relation
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  The request data of querying EN NSFT route relation commmand
 * \param [out] The confirmation data of querying EN NSFT route relation command
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 */
META_RESULT __stdcall META_MMRf_EN_QueryNsftRouteRelation_r(const int meta_handle, const unsigned int ms_timeout,
                                                            MMRfTestCmd_EN_QueryNsftRouteRelation_Req *req,
                                                            MMRfTestCmd_EN_QueryNsftRouteRelation_Cnf *cnf);


/**
 * \ingroup NRStruct
 * \details [M80 series] Get FR2 self calibration debug information
 */
typedef struct
{
    std::vector<NL1TST_FR2_RfSelfCal_DugTrc_T>        entry;     /**< FR2 self calibration debug information vector */
} NL1TST_FR2_GetRfSelfCalDbgInfo_CnfPdu;

/**
 * \ingroup NRStruct
 * \details [M80 series] Get FR2 self calibration debug information
 */
typedef struct
{
    NL1TST_FR2_RfSelfCal_DugTrc_T  entry[NL1TST_FR2_MAX_RF_SELF_K_LID_COUNT];     /**< FR2 self calibration debug information vector */
} NL1TST_FR2_GetRfSelfCalDbgInfo_c_CnfPdu;


/**
 * <pre>
 * \ingroup NR
 * \details [M80 series] Get FR2 self calibration debug information
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [out] cnfLocal confirmation data of FR2 self calibration debug entry count.
 * \param [out] cnfPeer confirmation data of FR2 self calibration debug information.
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 */
META_RESULT __stdcall META_NRf_GetFR2SelfCalDebugInfo(const int meta_handle, const unsigned int ms_timeout,
                                                      NL1TST_FR2_GetRfSelfCalDbgInfo_CnfParam *cnfLocal,
                                                      NL1TST_FR2_GetRfSelfCalDbgInfo_CnfPdu *cnfPeer);


/**
 * <pre>
 * \ingroup NR
 * \details [M80 series] Get FR2 self calibration debug information
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [out] cnfLocal confirmation data of FR2 self calibration debug entry count.
 * \param [out] cnfPeer confirmation data of FR2 self calibration debug information.
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 */
META_RESULT __stdcall META_NRf_GetFR2SelfCalDebugInfo_data(const int meta_handle, const unsigned int ms_timeout, NL1TST_FR2_GetRfSelfCalDbgInfo_CnfParam *cnfLocal, NL1TST_FR2_GetRfSelfCalDbgInfo_c_CnfPdu *cnfPeer);

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] The currently supported dynamic size confirm of multi-RAT modem information query(Dynamic size database)
 */
struct MMRfTestResult_EN_QueryConfigV8
{
    unsigned int                                        version;                    /**< version */
    std::vector<MMTST_TRX_BAND_INFO_T>                  band_info;                  /**< LTE/NR band information */
    std::vector<MMTST_VERIFY_IDX_SX_INFO_V8_T>          tx_verify_idx_info;         /**< TX verify index information */
    std::vector<MMTST_VERIFY_IDX_SX_INFO_V8_T>          rx_t1_verify_idx_info;      /**< RX type1 verify index information */
    std::vector<MMTST_VERIFY_IDX_SX_PAIR_INFO_V8_T>     rx_t2_verify_idx_info;      /**< RX type2 verify index information */
    std::vector<MMTST_TX_INFO_V8_T>                     tx_info;                    /**< TX route information */
    std::vector<MMTST_RX_T1_INFO_V8_T>                  rx_t1_info;                 /**< RX_T1 route information */
    std::vector<MMTST_RX_T2_INFO_V8_T>                  rx_t2_info;                 /**< RX_T2 route information */
    MMTST_RX_GM_INFO_V8_T                               rx_hpm_gm_info;             /**< RX high gain mode information */
    MMTST_RX_GM_INFO_V8_T                               rx_lpm_gm_info;             /**< RX low gain mode information */
    std::vector<MMTST_TRX_ROUTE_PARING_INFO_V8_T>       tx_to_rx_verify_info;       /**< TX/RX verify index pair information */
    std::vector<MMTST_ROUTE_BLK_INFO_T>                 rx_t1_blk_num_by_route;     /**< Rx_T1 block number of LTE/NR route */
    std::vector<MMTST_ROUTE_BLK_INFO_T>                 rx_t2_blk_num_by_route;     /**< Rx_T2 block number of LTE/NR route */
    std::vector<MMTST_ROUTE_BLK_INFO_TX_BW_V8_T>        tx_blk_num_by_route;        /**< TX block number of LTE/NR route */
    std::vector<MMTST_TX_INFO_EXT_V8_T>                 tx_max_prf_ext_info;        /**< TX max pwr per LTE/NR verify index */
    std::vector<MMTST_GetTxVerifyInfo_Element_V8_T>     lte_rftool_rxtxVerify;      /**< LTE TX/RX verify index mapping */
    std::vector<MMTST_GetHpueVerifyInfo_V8_T>           lte_hpue_verify_info;       /**< LTE HPUE verify index information */
    std::vector<MMTST_LTE_GetMimoBandInfo_T>            lte_mimo_band_info;         /**< LTE MIMO band information */
    std::vector<MMTST_GetTxVerifyInfo_Element_V8_T>     nr_nsft_rxtxVerify;         /**< NR TX/RX verify index mapping */
    std::vector<MMTST_NR_GetMimoBandInfo_T>             nr_mimo_band_info;          /**< NR MIMO band information */
    std::vector<MMTST_DPD_ROUTE_INFO_V8_T>              dpd_route_info;             /**< DPD route information */
    std::vector<MMTST_DPD_CAL_DATA_INFO_V8_T>           dpd_cal_data_info;          /**< DPD calibration data information */
    std::vector<MMTST_ET_ROUTE_INFO_V8_T>               et_route_info;              /**< ET route information */
    std::vector<MMTST_ET_CAL_DATA_INFO_V8_T>            et_cal_data_info;           /**< ET calibration data information */
    std::vector<MMTST_MRX_CAL_PRF_USED_BOUNDARY_INFO_V8_T>  mrx_cal_prf_boundary_info;  /**< MRX calibration Prf used boundary information */
    std::vector<MMTST_SPLIT_BAND_INFO_V8_T>             tx_split_band_info;         /**< TX split band information */
    std::vector<MMTST_SPLIT_BAND_INFO_V8_T>             rx_t1_split_band_info;      /**< RX_T1 split band information */
    std::vector<MMTST_SPLIT_BAND_INFO_V8_T>             rx_t2_split_band_info;      /**< RX_T2 split band information */
};

/**
 * <pre>
 * \ingroup MultiMode
 * \details [M80 series] Query multi-RAT modem information with dynamic size database supported
 * \sa META_MMRf_EN_Dynamic_Query_V2_r
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  rat the RAT info (LTE: MMTST_RAT_LTE, NR: MMTST_RAT_NR), queried category
 * \param [out] cnf the LTE/NR CA configuration
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 **/

META_RESULT __stdcall META_MMRf_EN_Dynamic_Query_V8_r(const int meta_handle, const unsigned int ms_timeout,
                                                      const MMRfTestCmd_EN_QueryConfigV8 *req,
                                                      MMRfTestResult_EN_QueryConfigV8 *cnf);

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] The currently supported dynamic size confirm of multi-RAT modem golden calibration data information query(Dynamic size database)
 */
struct MMRfTestResult_EN_Golden_QueryConfigV8
{
    unsigned int                                        version;                    /**< version */
    std::vector<MMTST_ROUTE_BLK_INFO_T>                 rx_t1_blk_num_by_route;     /**< Rx_T1 block number of LTE/NR route */
    std::vector<MMTST_ROUTE_BLK_INFO_T>                 rx_t2_blk_num_by_route;     /**< Rx_T2 block number of LTE/NR route */
    std::vector<MMTST_ROUTE_BLK_INFO_TX_BW_V8_T>        tx_blk_num_by_route;        /**< TX block number of LTE/NR route */
};

/**
 * <pre>
 * \ingroup MultiMode
 * \details [M80 series] Query multi-RAT modem golden calibration data information with dynamic size database supported
 * \sa META_MMRf_EN_Golden_Dynamic_Query_V8_r
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  rat the RAT info (LTE: MMTST_RAT_LTE, NR: MMTST_RAT_NR), queried category
 * \param [out] cnf the LTE/NR CA configuration
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 **/

META_RESULT __stdcall META_MMRf_EN_Golden_Dynamic_Query_V8_r(const int meta_handle, const unsigned int ms_timeout,
                                                             const MMRfTestCmd_EN_Golden_QueryConfigV8 *req,
                                                             MMRfTestResult_EN_Golden_QueryConfigV8 *cnf);

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] Dynamic peer buffer of setting LTE/NR DPD command
 */
typedef struct
{
    std::vector<MMDPD_BW_INFO_V8_T>               bw_info_pdu_1;  /**< bandwidth information PDU */
    std::vector<MMTST_DPD_CAL_DATA_CONTROL_V8_T>  dpd_control_pdu_2;  /**< control PDU */
    std::vector<MMTST_DPD_CAL_DATA_PA_GAIN_V8_T>  dpd_pa_gain_pdu_3;  /**< PA gain PDU */
    std::vector<MMTST_DPD_CAL_DATA_DPD_LUT_V8_T>  dpd_lut_pdu_4;  /**< LUT PDU */
    std::vector<MMTST_DPD_CAL_DATA_CHEQ_V8_T>     dpd_cheq_pdu_5;  /**< channel equalization PDU */
} MMRfTestCmd_DpdDataV8_DYN;

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] Dynamic peer buffer of setting LTE/NR ET command
 */
typedef struct
{
    std::vector<MMET_BW_INFO_V8_T>             bw_info_1;           /**< bandwidth information */
    std::vector<MMET_FAC_CAL_INFO_V8_T>        et_fac_cal_info_2;   /**< ET factory calibration information */
    std::vector<MMET_CAL_CONTROL_DATA_V8_T>    et_control_3;        /**< control data */
    std::vector<MMET_ETM_VPA_CONFIG_V8_T>      et_etm_vpa_config_4; /**< ETM VPA configure */
    std::vector<MMET_TPC_CAL_DATA_V8_T>        et_tpc_cal_data_5;   /**< TPC calibration data */
    std::vector<MMET_CAL_DATA_V8_T>            et_cal_data_6;       /**< calibration data */
} MMRfTestCmd_EtDataV8_DYN;


/**
 * \ingroup MultiModeStruct
 * \details [M80 series] calibration data download check result,separate according to different rats, used for storing database(Dynamic size database usage)
 */
typedef struct
{
    std::vector<MMTST_CddcResultEntry_T>  gsm_enty_result;   /**< gsm cddc result  */
    std::vector<MMTST_CddcResultEntry_T>  wcdma_enty_result; /**< wcdma cddc result  */
    std::vector<MMTST_CddcResultEntry_T>  lte_enty_result;   /**< lte cddc result  */
    std::vector<MMTST_CddcResultEntry_T>  multi_rat_enty_result;  /**< multi rat cddc result  */
    std::vector<MMTST_CddcResultEntry_T>  tdscdma_enty_result;  /**< tdscdma rat cddc result  */
    std::vector<MMTST_CddcResultEntry_T>  c2k_enty_result;    /**< c2k cddc result  */
    std::vector<MMTST_CddcResultEntry_T>  nr_enty_result;     /**< nr cddc result  */
    std::vector<MMTST_CddcResultEntry_T>  iot_ntn_enty_result;     /**< iot-ntn cddc result  */
} MMRfTestCmd_QueryCddcInfo_DYN;

/**
 * <pre>
 * \ingroup MultiMode
 * \details [M80 series] Query cddc information with dynamic size database supported
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req queried category(missing data or valid data)
 * \param [out] cnf cddc information
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 **/
META_RESULT __stdcall META_MMRf_QueryCddcInfo_r(const int meta_handle, const unsigned int ms_timeout,
                                                const MMRfTestCmd_QueryCddcInfo_ReqParam *req,
                                                MMRfTestCmd_QueryCddcInfo_DYN *cnf);

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] The "block number per route" of LTE/NR TX/RX, used for storing database(Dynamic size database usage)
 */
struct MMRfTestResult_EN_TrxBlkInfoByRouteV7_DynSize_V2_T
{
    std::vector<MMTST_ROUTE_BLK_INFO_T>            rx_t1_blk_num_by_route;
    std::vector<MMTST_ROUTE_BLK_INFO_T>            rx_t2_blk_num_by_route;
    std::vector<MMTST_ROUTE_BLK_INFO_TX_BW_EXT_T>  tx_blk_num_by_route;
};

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] Multi-RAT TX and RX routes information(used for dynamic size query database)
 */
struct MMTST_TRX_ROUTE_INFO_DynSize_V2_T
{
    std::vector<MMTST_TX_INFO_V2_T> tx_info;               /**< TX route information */
    std::vector<MMTST_RX_T1_INFO_T> rx_t1_info;            /**< RX-Type1 route information */
    std::vector<MMTST_RX_T2_INFO_T> rx_t2_info;            /**< RX-Type2 route information */
};

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] The LTE/NR RX route mapping to TX route list result.
 */
typedef struct
{
    unsigned short rxRoute; /**< the RX route */
    unsigned int txRouteCount;  /**< support TX route count */
    std::vector<unsigned short> txRouteList;   /**< the TX route list */
} MMRfTestCmd_GetTxRouteListV7Vec;

/**
 * \ingroup MultiModeStruct
 * \details [M70 series] Multi-RAT TX routes lists mapping to RX routes(used for dynamic size query database)
 */
struct MMRfTestCmd_GetTxRouteByRxCnfV7_DynSize_T
{
    std::vector<MMRfTestCmd_GetTxRouteListV7Vec> tx_route_list_by_rx_route; /**< the TX route list by each RX route, only type1 has these mapping info */
};

/**
 * \ingroup LTEStruct
 * \details [M70 series] The LTE RX MIMO band info(Dynamic size database usage)
 */
struct ERfTestCmd_GetMIMOBandInfoV7_DynSize_T
{
    std::vector<MMTST_LTE_GetMimoBandInfo_T> band_mapping_rx_band_type;    /**< RX MIMO band and type information */
};

/**
 * \ingroup NRStruct
 * \details [M70 series] The "MIMO band and TX/RX type" query info(Dynamic size database usage)
 */
struct NRfTestCmd_GetMIMOBandInfoV7_DynSize_T
{
    std::vector<MMTST_NR_GetMimoBandInfo_T> bandMappingTrxBandType;    /**< Mino band trx type mapping */
};

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] Multi-RAT TX and RX routes information
 */
struct MMTST_TRX_ROUTE_INFO_DynSize_V8_T
{
    std::vector<MMTST_TX_INFO_V8_T>     tx_info;               /**< TX route information */
    std::vector<MMTST_RX_T1_INFO_V8_T>  rx_t1_info;            /**< RX-Type1 route information */
    std::vector<MMTST_RX_T2_INFO_V8_T>  rx_t2_info;            /**< RX-Type2 route information */
};

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] The block number of LTE/NR TX/RX routes for calibration data operating
 */
typedef struct
{
    std::vector<MMTST_ROUTE_BLK_INFO_T>           rx_t1_blk_num_by_route;    /**< the RX-Type1 route block number information  */
    std::vector<MMTST_ROUTE_BLK_INFO_T>           rx_t2_blk_num_by_route;    /**< the RX-Type2 route block number information  */
    std::vector<MMTST_ROUTE_BLK_INFO_TX_BW_V8_T>  tx_blk_num_by_route;   /**< the TX route block number information  */
} MMRfTestResult_EN_TrxBlkInfoByRouteV8_DynSize_T;

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] The vector of TX verify idx result.
 */
typedef struct
{
    unsigned short rxVerifyIdx; /**< the RX verify idx */
    unsigned int txVerifyIdxCount; /**< support TX verify idx count */
    std::vector<unsigned short> txVerifyIdxList; /**< the TX verify idx list */
} MMRfTestCmd_GetTxVerifyIdxV8Vec;

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] Multi-RAT TX verify idx lists mapping to RX verify idx(used for dynamic size query database)
 */
struct MMRfTestCmd_GetTxVerifyIdxByRxCnfV8_DynSize_T
{
    std::vector<MMRfTestCmd_GetTxVerifyIdxV8Vec> tx_verify_idx_list_by_rx_verify_idx; /**< the TX verify idx list by each RX verify idx, only type1 has these mapping info */
};

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] The vector of RX verify idx result.
 */
typedef struct
{
    unsigned short txVerifyIdx;       /**< the TX verify idx */
    unsigned int rxVerifyIdxCount;  /**< support RX verify idx count */
    std::vector<unsigned short> rxVerifyIdxList;   /**< the RX verify idx list */
} MMRfTestCmd_GetRxVerifyIdxV8Vec;

/**
 * \ingroup MultiModeStruct
 * \details [M80 series] Multi-RAT RX verify idx lists mapping to TX verify idx(used for dynamic size query database)
 */
struct MMRfTestCmd_GetRxVerifyIdxByTxCnfV8_DynSize_T
{
    std::vector<MMRfTestCmd_GetRxVerifyIdxV8Vec> rx_verify_idx_list_by_tx_verify_idx; /**< the RX verify idx list by each TX verify idx*/
};

/**
 * <pre>
 * \ingroup MultiMode
 * \details [M70 series] Read Power Limitation Information
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnfLocal the confirmation data
 * \param [out] cnfPeer the confirmation peer buffer data
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 **/
META_RESULT __stdcall META_MMRf_ReadPowerLimitInfo_r(const int meta_handle, const unsigned int ms_timeout,
                                                     const MMRfTestCmd_ReadPowerLimit_ReqParam *req,
                                                     MMRfTestCmd_ReadPowerLimit_CnfParam *cnfLocal,
                                                     std::vector<MMTST_PowerLimit_Data_Unit> &cnfPeer);

typedef struct
{
    unsigned int              info_cnf_bitmap;
    std::vector<IL1TST_TRX_BAND_INFO_T> band_info;  /**< TX RX band information */
    std::vector<IL1TST_RX_CAL_INFO_T>   rx_cal_info;  /**< rx_cal_info */
    std::vector<IL1TST_TX_INFO_MPRF_T>  tx_max_prf_info; /**< maximum TX power */
} IotNtnRfTestResult_QueryConfig_DynSize_T;

META_RESULT __stdcall META_Iotntn_DynamicQuery_r(const int meta_handle, const unsigned int ms_timeout, const IRfTestCmd_IotNtnDynamicQueryReq *req, IotNtnRfTestResult_QueryConfig_DynSize_T *cnf);

/**
 * <pre>
 * \ingroup MultiMode
 * \details [M90 series] Query cddc information with dynamic size database supported
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req queried category(missing data or valid data)
 * \param [out] cnf cddc information
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 **/
META_RESULT __stdcall META_MMRf_QueryCddcInfo_V9_r(const int meta_handle, const unsigned int ms_timeout,
                                                   const MMRfTestCmd_QueryCddcInfo_ReqParam *req,
                                                   MMRfTestCmd_QueryCddcInfo_DYN *cnf);

#endif // #ifndef META_CORE_CPP_H
