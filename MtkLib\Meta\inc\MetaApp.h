/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __METAAPP_COMMON_H__
#define __METAAPP_COMMON_H__

#define MAX_BARCODE_SIZE      64
#define MAX_TIME_SIZE         32
#define MOBILE_LOG_SOCKET_PORT   10119
#define MODEM_LOG_SOCKET_PORT    10001
#define META_MAX_THREADS 32
#define MAX_PORT_FILTER       16

#include <string>
#include "meta.h"
#include "export.h"

#ifdef _WIN32
#include "sp_brom.h"
#include "message_box.h"

typedef struct
{
    SP_AUTH_HANDLE_T  authHandle;
    SP_SCERT_HANDLE_T scertHandle;
    SP_CALLBACK_SLA_CHALLENGE cbSlaChallenge;
    void *callbackSLAChallengeArg;
    SP_CALLBACK_SLA_CHALLENGE_END  cbSlaChallengeEnd;
    void *callbackSLAChallengeArgEnd;
    SP_CALLBACK_PL_SLA_CHALLENGE cbPlSlaChallenge;
    void *callbackPlSlaChallengeArg;
    SP_CALLBACK_PL_SLA_CHALLENGE_END cbPlSlaChallengeEnd;
    void *callbackPlSlaChallengeEndArg;
} METAAPP_EXTEND_CONN_SETTING_SLA_T;

typedef struct
{
    bool bEnableAdbDevice;
    unsigned int uPortNumber;  //preloader com port
    unsigned short uMDMode;
} METAAPP_CONN_BOOT_STTING_T;

typedef enum
{
    METAAPP_CONN_CONNCET_BY_UART = 0,
    METAAPP_CONN_CONNCET_BY_USB  = 1,
    METAAPP_CONN_CONNCET_BY_WIFI = 2,
    METAAPP_CONN_CONNCET_NUM = METAAPP_CONN_CONNCET_BY_WIFI + 1
} METAAPP_CONN_TYPE_E;

typedef enum
{
    METAAPP_HIGH_LEVEL_SUCCESS = 0,
    METAAPP_HIGH_LEVEL_FAIL = 1,
    METAAPP_HIGH_LEVEL_HANDLE_ALLOC_FAIL = 2,
    METAAPP_HIGH_LEVEL_OPEN_DLL_LOG_FAIL = 3,
    METAAPP_HIGH_LEVEL_GET_PRELOAD_PORT_FAIL = 4,
    METAAPP_HIGH_LEVEL_BOOT_PRELOAD_FAIL = 5,
    METAAPP_HIGH_LEVEL_GET_KERNEL_PORT = 6,
    METAAPP_HIGH_LEVEL_CONNECT_AP_META_FAIL = 7,
    METAAPP_HIGH_LEVEL_GET_AP_VERSION_FAIL = 8,
    METAAPP_HIGH_LEVEL_LOAD_AP_DATABASE_FROM_DUT_FAIL = 9,
    METAAPP_HIGH_LEVEL_LOAD_MD_DATABASE_FROM_DUT_FAIL = 10,
    METAAPP_HIGH_LEVEL_INIT_AP_DATABASE_FAIL = 11,
    METAAPP_HIGH_LEVEL_INIT_MINICOMLOGGER_FAIL = 12,
    METAAPP_HIGH_LEVEL_OPEN_MODEM_LOG_FAIL = 13,
    METAAPP_HIGH_LEVEL_AP_DATABASE_NOT_MATCH = 14,
    METAAPP_HIGH_LEVEL_MD_DATABASE_NOT_MATCH = 15,
    METAAPP_HIGH_LEVEL_PRELOAD_SLA_FAIL = 16,
    METAAPP_HIGH_LEVEL_OPEN_AP_LOG_FAIL = 17,

    METAAPP_HIGH_LEVEL_GET_MODEM_PORT_FAIL = 50,
    METAAPP_HIGH_LEVEL_REBOOT_MODEM_FAIL = 51,
    METAAPP_HIGH_LEVEL_GET_SP_MODEM_INFO_FAIL = 52,
    METAAPP_HIGH_LEVEL_DISCON_AP_TO_MODEM_FAIL = 53,
    METAAPP_HIGH_LEVEL_GET_MODEM_VERSION_FAIL = 54,
    METAAPP_HIGH_LEVEL_INIT_MODEM_DATABASE_FAIL = 55,
    METAAPP_HIGH_LEVEL_SET_MMC2K_INFO_FAIL = 56,

    METAAPP_HIGH_LEVEL_CONNECT_AP_FAIL = 101,
    METAAPP_HIGH_LEVEL_C2K_INIT_FAIL = 102,
    METAAPP_HIGH_LEVEL_SWTICH_TO_C2K_FAIL = 103,
    METAAPP_HIGH_LEVEL_EXIT_C2K_FAIL = 104,

    METAAPP_HIGH_LEVEL_MD_STATUS_NOT_CORRENT_FAIL = 120,

    METAAPP_HIGH_LEVEL_CLEAN_BOOT_FAIL = 150,
    METAAPP_HIGH_LEVEL_EXIT_META_FAIL = 151,

    METAAPP_HIGH_LEVEL_ARG_SIZE_NOT_MATCH = 1000,

    METAAPP_HIGH_LEVEL_END = 65536
} METAAPP_HIGH_LEVEL_RESULT;

typedef enum
{
    METAAPP_CONN_BOOT_META_MODE       = 0,   // boot process
    METAAPP_CONN_ALREADY_IN_META_MODE = 1,   // already in meta mode(skip boot preloader)
    METAAPP_CONN_ATM_MODE         = 2,   // ATM mode
    METAAPP_CONN_MODE_NUM = METAAPP_CONN_ATM_MODE + 1
} METAAPP_CONN_MODE_E;

#define APDB_PATH_BYPASS "bypass"
typedef struct
{
    int   kernelComPort;    //kernel com port
    bool  bDbFileFromDUT;   //true: database from DUT,false: database transmit by user
    char *pApDbFilePath;    //AP database path, will don't init APDB if content is APDB_PATH_BYPASS.
    char *pMdDbFilePath;    //MD database path for WG\LWG\LWTG\LWCTG...database file
    char *pMdDbFilePath1;   //MD database path for TG\LTG....database file
} METAAPP_CONN_AP_SETTING_T;

typedef struct
{
    const char *brom;
    const char *preloader;
    const char *kernel;
} METAAPP_CONN_FILTER_SETTING_T;

typedef struct
{
    bool enableDllLog;
    bool enableUartLog;     // bUartLogDisable;
    bool enablePcUsbDriverLog;
    bool enableBootprofLog;
    int  iMDLoggEnable;     //0 disable; 1:ELT Port output 2:Modem log in SD card
    int  iMobileLogEnable;  //0 disable; 1:ELT Port output 2:Saving in SD card
    int  iConnsysLogEnable;  //0 disable; 1:ELT Port outpyt 2:Saving in SD card
    char IP[64];
    char pcbSn[MAX_BARCODE_SIZE + MAX_TIME_SIZE];
    char logSavePath[MAX_PATH];
} METAAPP_CONN_LOG_SETTING_T;

typedef void (__stdcall *METAAPP_Conn_Log_Display_CallBackForMultiThread)(const char *logBuf, void *cbUserDate);
typedef struct
{
    bool autoScanPort;
    int *stopFlag;
    METAAPP_CONN_TYPE_E connectType;
    METAAPP_CONN_MODE_E connectMode;
    unsigned int uTimeOutMs;

    METAAPP_CONN_BOOT_STTING_T     bootSetting;
    METAAPP_CONN_AP_SETTING_T connectSetting;
    METAAPP_CONN_LOG_SETTING_T logSetting;
    METAAPP_CONN_FILTER_SETTING_T  filterSetting;

} METAAPP_CONN_STTING_T;

typedef struct
{
    const char *mdLogPortFilter;  //MI_X
} METAAPP_EXTEND_CONN_FILTER_SETTING_T;
typedef struct
{
    METAAPP_EXTEND_CONN_SETTING_SLA_T extraConnSettingSLA;
    METAAPP_EXTEND_CONN_FILTER_SETTING_T extraFilterSetting;
} METAAPP_EXTEND_CONN_SETTING_T;

typedef struct
{
    int hMDHandle;
    unsigned int uPreloaderPortNumber;
    unsigned int uKernelPortNumber;

} METAAPP_CONNCT_CNF_T;

typedef enum
{
    T_GSM = 0,
    T_TDS,
    T_WCDMA,
    T_LTE,
    T_C2K,
    T_EXIT_C2K
} METAAPP_TEST_RAT_E;

#endif //#ifdef _WIN32

typedef struct
{
    int  iMDLoggEnable; //0 disable; 1:ELT Port output 2:Modem log in SD card
    bool bSDMDLoggEnable;
    int  iMobileLogEnable; //0 disable; 1:ELT Port output 2:Saving in SD card
    int  iConnsysLogEnable; //0 disable; 1:ELT Port output 2:Saving in SD card
    char pLogName[MAX_BARCODE_SIZE + MAX_TIME_SIZE]; //SN+TIME
    char pLogPath[MAX_PATH];
    char mdLogPortFilter[MAX_PORT_FILTER];
} LogConfigure_s;

typedef struct
{
    char IP[64];
    int port;
} LogWifiCon_s;

typedef struct
{
    LogConfigure_s sLogConfigure;
    unsigned int nConnectType; //0:Uart, 1:USB, 2:WIFI
    LogWifiCon_s sWifiCon;
    unsigned int m_uKernelCom;
    unsigned int m_uDebugCom;
} MiniComLogInput_s;

typedef struct
{
    unsigned int portNumber;
    char *path;
} MiniComPortInfo;

typedef struct
{
    LogConfigure_s sLogConfigure;
    unsigned int nConnectType; //0:Uart, 1:USB, 2:WIFI
    LogWifiCon_s sWifiCon;
    MiniComPortInfo m_uKernelCom;
    MiniComPortInfo m_uDebugCom;
} MiniComLogInput_Ex_s;

#ifdef _WIN32

typedef enum
{
    METAAPP_Disconnect_DoNothing      = 0,   //just disconnect meta(close com port)
    METAAPP_Disconnect_Poweroff       = 1,   //disconnect meta with shutdown phone
    METAAPP_Disconnect_Reboot         = 2,   //disconenct meta with reboot phone
    METAAPP_Disconnect_UnmountData    = 3,   //disconnect meta with unmount data, this feature not ready
    METAAPP_Disconnect_KillWifiAPK    = 4,   //disconnect meta(close com port) with Exit WIFI APK
    METAAPP_Disconnect_NUM = METAAPP_Disconnect_KillWifiAPK + 1
} METAAPP_DISCONNECT_META_PARA_E;


typedef struct
{

    METAAPP_DISCONNECT_META_PARA_E eDisconPara;

    bool bDoBackupNv;        //true: disconnect meta with backup NV to NVRAM partition, false:  disconnect meta without backup NV


} METAAPP_DISCONNECT_META_T;

#endif //#ifdef _WIN32

typedef enum
{
    AP_META = 0,
    MD_META,
    META_MODE_NUM
} METAAPP_CONN_META_MODE_T;

#ifdef _WIN32

typedef struct
{
    bool  bConnection;      //Enable AP or MD Meta Connection
    int   kernelComPort;    //kernel com port
    char *pDbFilePath;  //database path, will don't init DB if content is APDB_PATH_BYPASS.
} METAAPP_CONN_META_SETTING_T;


typedef struct
{
    const char *brom;
    const char *preloader;
    const char *kernel;
    const char *apLogPort;  //MI_X
    const char *mdKernel;
    const char *mdLogPort;  //MI_X
} METAAPP_CONN_PORT_FILTER_SETTING_T;

typedef struct
{
    bool enableMIPCComPort;        //enable MIPC com port or not:1:enable, 0:disable
} METAAPP_CONN_EXTRA_SETTING_T;
typedef struct
{
    bool autoScanPort;  //auto scan com port
    int *stopFlag;
    METAAPP_CONN_TYPE_E connectType;
    METAAPP_CONN_MODE_E connectMode;  //boot meta\already in meta\ATM
    unsigned int uTimeOutMs;

    METAAPP_CONN_BOOT_STTING_T        bootSetting;
    METAAPP_CONN_META_SETTING_T       connectSetting[META_MODE_NUM];
    METAAPP_CONN_LOG_SETTING_T        logSetting;
    METAAPP_CONN_PORT_FILTER_SETTING_T  portFilterSetting;
    METAAPP_CONN_EXTRA_SETTING_T       extralSetting;

} METAAPP_DATACARD_CONN_STTING_T;

typedef struct
{
    int hHandle;
    unsigned int uPreloaderPortNumber;
    unsigned int uAPPortNumber;
    unsigned int uMDPortNumber;

} METAAPP_DATACARD_CONNCT_CNF_T;

#endif //#ifdef _WIN32

typedef struct
{
    unsigned int nConnectType; //0:Uart, 1:USB, 2:WIFI
    unsigned int m_uKernelCom;
    unsigned int m_uDebugCom;
    char pLogName[MAX_BARCODE_SIZE + MAX_TIME_SIZE]; //SN+TIME
    char pLogPath[MAX_PATH];
    char pLogPortFilter[MAX_PORT_FILTER];
} DataCardLogInput_s;

#define MetaHandleCheckMacro(metaHandle) {if(metaHandle < 0 || metaHandle >= META_MAX_THREADS ) return META_FAILED;}

#ifdef  __cplusplus
extern "C" {
#endif

#ifdef _WIN32
//connect meta function
/*
    METAAPP_ConnectTargetAllInOne_r: connect AP ,and get modem info from AP
*/
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI  METAAPP_ConnectTargetAllInOne_r(const int threadSlot, METAAPP_CONN_STTING_T *pSetting, METAAPP_CONNCT_CNF_T *pCnf, unsigned int arg_size);
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI  METAAPP_ApToModemAllInOne_r(const int threadSlot);
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI  METAAPP_ModemToApAllInOne_r(const int threadSlot);
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI  METAAPP_SwitchTestRat_r(const int threadSlot, METAAPP_TEST_RAT_E tRat);
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI  METAAPP_DisConnectMeta_Ex_r(const int threadSlot, METAAPP_DISCONNECT_META_T disconPara, unsigned int arg_size);
//set meta connect extend parameters
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI  METAAPP_ExtendParaSetting_r(const int threadSlot, METAAPP_EXTEND_CONN_SETTING_T *pExtendConnSet);

/*
    METAAPP_ConnectTargetAllInOne_EX_r: only connect AP ,for AP only platform,or only BT\WIFI\GPS test station
*/
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI  METAAPP_ConnectAPOnlyAllInOne_r(const int threadSlot, METAAPP_CONN_STTING_T *pSetting, METAAPP_CONNCT_CNF_T *pCnf, unsigned int arg_size);
/*
    Connect meta for modem only dongle(No AP)
*/
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI METAAPP_ConnectThinModemToMeta_r(const int threadSlot, METAAPP_CONN_STTING_T *pSetting, METAAPP_CONNCT_CNF_T *pCnf, unsigned int arg_size);
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI METAAPP_DisConnectThinModemMeta_r(const int threadSlot, METAAPP_DISCONNECT_META_T disconPara, unsigned int arg_size);

/*
    Connect meta for data card
*/
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI METAAPP_ConnectDCToAPMeta_r(const int threadSlog, METAAPP_DATACARD_CONN_STTING_T *pSetting, METAAPP_DATACARD_CONNCT_CNF_T *pCnf, unsigned int arg_size);
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI METAAPP_ConnectDCToMDMeta_r(const int threadSlog);
METAAPP_HIGH_LEVEL_RESULT METAAPPAPI METAAPP_DisConnectDCMeta_r(const int threadSlot, METAAPP_DISCONNECT_META_T disconPara, unsigned int arg_size);

//callback register for log trace
META_RESULT METAAPPAPI METAAPP_RegisterCallBackFor_Conn_Log_Display(const int threadSlot, const METAAPP_Conn_Log_Display_CallBackForMultiThread cb, void *cbUserData);
//Write C2KMEID
/*
    USE by below test step
    1. METAAPP_SwitchTestRat_r(T_C2K)
    2. METAAPP_WriteMEID_r
    3. METAAPP_SwitchTestRat_r(T_EXIT_C2K)
*/
META_RESULT METAAPPAPI  METAAPP_WriteMEID_r(const int threadSlot, int nWriteMeid, char *pszMeid, int nWriteEsn, char *pszEsn);
#endif //#ifdef _WIN32

//modem/mobile log function
bool METAAPPAPI MetaApp_IsLoggingInstanceExist(const int meta_handle);
META_RESULT METAAPPAPI MetaApp_InitMetaLog(const int meta_handle, MiniComLogInput_s &sLogInput, bool bConnectError = false);
META_RESULT METAAPPAPI MetaApp_InitMetaLog_Ex(const int meta_handle, MiniComLogInput_Ex_s &sLogInput, bool bConnectError = false);
META_RESULT METAAPPAPI MetaApp_StartMetaLog(const int meta_handle, bool bSendDefaultFilter = true);
META_RESULT METAAPPAPI MetaApp_StopLog(const int meta_handle, bool bAssert);
META_RESULT METAAPPAPI MetaApp_StopErrorLog(const int meta_handle, bool bAssert);
META_RESULT METAAPPAPI MetaApp_WaitMemDumpDone(const int meta_handle);
META_RESULT METAAPPAPI MetaApp_EnableMobileLog(const int meta_handle);


//Datacard modem/mobile log function
META_RESULT METAAPPAPI MetaApp_StartDataCardTargetLog(const int meta_handle, METAAPP_CONN_META_MODE_T mode, DataCardLogInput_s &sLogInput);
META_RESULT METAAPPAPI MetaApp_StopDataCardTargetLog(const int meta_handle, METAAPP_CONN_META_MODE_T mode, bool bAssert);
META_RESULT METAAPPAPI MetaApp_SetDataCardModemFilter(const int meta_handle);

//thinmodem modem log function
META_RESULT METAAPPAPI MetaApp_StartThinModemTargetLog(const int meta_handle,  DataCardLogInput_s &sLogInput);
META_RESULT METAAPPAPI MetaApp_StopThinModemTargetLog(const int meta_handle,  bool bAssert);
META_RESULT METAAPPAPI MetaApp_SetThinModemModemFilter(const int meta_handle);

#ifdef _WIN32
META_RESULT METAAPPAPI MetaApp_RenameLogFiles(char *strPath, const char *strKeyPhase, const char *strReplacePhase);
META_RESULT METAAPPAPI MetaApp_AddTimeForLogName(char *strInputName, char *strOutputName, int iOutputStringSize);

//meta app common function
META_RESULT METAAPPAPI METAAPP_SetPowerOffsetToPhone(const int meta_handle, int iBand, double dPowerOffset, int count);
META_RESULT METAAPPAPI METAAPP_LtePowerOffsetInit(const int meta_handle);
META_RESULT METAAPPAPI METAAPP_LteSetPowerOffsetToPhone(const int meta_handle, int iBand, int iRoute, double dPowerOffset, int count);
META_RESULT METAAPPAPI METAAPP_LtePowerOffsetDeInit(const int meta_handle);

//barcode
META_RESULT METAAPPAPI METAAPP_writeBarcodetoNVRAM(const int meta_handle, char *p_Barcode, int timeout);
META_RESULT METAAPPAPI METAAPP_readBarcodefromNVRAM(const int meta_handle, int timeout, char *p_Barcode);

//IMEI
META_RESULT METAAPPAPI METAAPP_readIMEIfromNVRAM_Ex3(const int meta_handle, const int timeout, unsigned short rid, IMEISV_struct_T *p_IMEISV);
META_RESULT METAAPPAPI METAAPP_writeIMEItoNVRAM_Ex3(const int meta_handle, const int timeout, bool b_check_checksum, bool b_NVRAM_lock, unsigned short rid, IMEISV_struct_T *p_IMEISV);

//AP META Read\Write
META_RESULT METAAPPAPI METAAPP_AP_NVRAM_Read_r(const int meta_handle, unsigned int ms_timeout, const AP_FT_NVRAM_READ_REQ *req, AP_FT_NVRAM_READ_CNF *cnf);
META_RESULT METAAPPAPI METAAPP_AP_NVRAM_Write_r(const int meta_handle, unsigned int ms_timeout, const AP_FT_NVRAM_WRITE_REQ *req);
META_RESULT METAAPPAPI METAAPP_AP_NVRAM_GetRecLen(const int meta_handle, const char *LID, int *len);
META_RESULT METAAPPAPI METAAPP_AP_NVRAM_Reset_r(const int meta_handle, unsigned int ms_timeout, const char *LID);
#endif //#ifdef _WIN32

#ifdef  __cplusplus
}
#endif

#endif

