/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
/*****************************************************************************
*  Copyright Statement:
*  --------------------
*  This software is protected by Copyright and the information contained
*  herein is confidential. The software may not be copied and the information
*  contained herein may not be used or disclosed except with the written
*  permission of MediaTek Inc. (C) 2006
*
*  BY OPENING THIS FILE, BUYER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
*  THAT THE SOFTWARE/FIRMWARE AND ITS DOCUMENTATIONS ("MEDIATEK SOFTWARE")
*  RECEIVED FROM MEDIATEK AND/OR ITS REPRESENTATIVES ARE PROVIDED TO BUYER ON
*  AN "AS-IS" BASIS ONLY. MEDIATEK EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
*  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
*  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
*  NEITHER DOES MEDIATEK PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
*  SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
*  SUPPLIED WITH THE MEDIATEK SOFTWARE, AND BUYER AGREES TO LOOK ONLY TO SUCH
*  THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. MEDIATEK SHALL ALSO
*  NOT BE RESPONSIBLE FOR ANY MEDIATEK SOFTWARE RELEASES MADE TO BUYER'S
*  SPECIFICATION OR TO CONFORM TO A PARTICULAR STANDARD OR OPEN FORUM.
*
*  BUYER'S SOLE AND EXCLUSIVE REMEDY AND MEDIATEK'S ENTIRE AND CUMULATIVE
*  LIABILITY WITH RESPECT TO THE MEDIATEK SOFTWARE RELEASED HEREUNDER WILL BE,
*  AT MEDIATEK'S OPTION, TO REVISE OR REPLACE THE MEDIATEK SOFTWARE AT ISSUE,
*  OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY BUYER TO
*  MEDIATEK FOR SUCH MEDIATEK SOFTWARE AT ISSUE.
*
*  THE TRANSACTION CONTEMPLATED HEREUNDER SHALL BE CONSTRUED IN ACCORDANCE
*  WITH THE LAWS OF THE STATE OF CALIFORNIA, USA, EXCLUDING ITS CONFLICT OF
*  LAWS PRINCIPLES.  ANY DISPUTES, CONTROVERSIES OR CLAIMS ARISING THEREOF AND
*  RELATED THERETO SHALL BE SETTLED BY ARBITRATION IN SAN FRANCISCO, CA, UNDER
*  THE RULES OF THE INTERNATIONAL CHAMBER OF COMMERCE (ICC).
*
*****************************************************************************/


#ifndef _SLA_CHALLENGE_H_
#define _SLA_CHALLENGE_H_

#define SLA_BUF_SIZE									(256)
#define SLA_OK											(0x0)
#define RSA_NEW_FAIL									(0x0001)
#define SET_KEY_FAIL									(0x0002)

#define AP_NEW_RSA_KEY_FAIL								(0x1000)
#define AP_CTX_NEW_FAIL									(0x1001)
#define AP_CTX_INIT_FAIL								(0x1002)
#define AP_SET_RSA_PADDING_FAIL							(0x1003)
#define AP_GET_SIG_LEN_FAIL								(0x1004)
#define AP_SIGN_RANDOM_NUM_FAIL							(0x1005)

#define MD_LEGACY_NEW_RSA_KEY_FAIL						(0x2000)
#define MD_LEGACY_CTX_NEW_FAIL							(0x2001)
#define MD_LEGACY_CTX_INIT_FAIL							(0x2002)
#define MD_LEGACY_SET_RSA_PADDING_FAIL					(0x2003)
#define MD_LEGACY_GET_SIG_LEN_FAIL						(0x2004)
#define MD_LEGACY_SIGN_RANDOM_NUM_FAIL					(0x2005)

#define MD_NORMAL_NEW_RSA_KEY_FAIL						(0x3000)
#define MD_NORMAL_CTX_NEW_FAIL							(0x3001)
#define MD_NORMAL_CTX_INIT_FAIL							(0x3002)
#define MD_NORMAL_SET_RSA_PADDING_FAIL					(0x3003)
#define MD_NORMAL_GET_SIG_LEN_FAIL						(0x3004)
#define MD_NORMAL_SIGN_RANDOM_NUM_FAIL					(0x3005)

#define MD_CERTCHAIN_INPUT_BUF_EXCEEDS_MAX				(0x4000)
#define MD_CERTCHAIN_SIGN_RANDOM_NEW_RSA_KEY_FAIL		(0x4100)
#define MD_CERTCHAIN_SIGN_RANDOM_CTX_INIT_FAIL			(0x4101)
#define MD_CERTCHAIN_SIGN_RANDOM_DIGEST_UPDATE_FAIL		(0x4102)
#define MD_CERTCHAIN_SIGN_RANDOM_GET_SIG_LEN_FAIL		(0x4103)
#define MD_CERTCHAIN_SIGN_RANDOM_DIGEST_SIGN_FAIL		(0x4104)
#define MD_CERTCHAIN_KEY_TRANS_FAIL						(0x4200)
#define MD_CERTCHAIN_SIGN_PUBKEY_NEW_RSA_KEY_FAIL		(0x4300)
#define MD_CERTCHAIN_SIGN_PUBKEY_CTX_INIT_FAIL			(0x4301)
#define MD_CERTCHAIN_SIGN_PUBKEY_DIGEST_UPDATE_FAIL		(0x4302)
#define MD_CERTCHAIN_SIGN_PUBKEY_GET_SIG_LEN_FAIL		(0x4303)
#define MD_CERTCHAIN_SIGN_PUBKEY_DIGEST_SIGN_FAIL		(0x4304)


#ifdef __cplusplus
extern "C" {
#endif

	/* BROM SLA */
	int __stdcall SLA_Challenge(void *usr_arg, const unsigned char  *p_challenge_in, unsigned int  challenge_in_len, unsigned char  **pp_challenge_out, unsigned int  *p_challenge_out_len);
	int __stdcall SLA_Challenge_END(void *usr_arg, unsigned char  *p_challenge_out);
	int __stdcall SLA_Feature_Config(void);
	/* MD META SLA (legacy) */
	int __stdcall MD_SLA_Challenge(void *usr_arg, const unsigned char  *p_challenge_in, unsigned int  challenge_in_len, unsigned char  **pp_challenge_out, unsigned int  *p_challenge_out_len);
	int __stdcall MD_SLA_Challenge_END(void *usr_arg, unsigned char  *p_challenge_out);
	/* MD META SLA (new) */
	int __stdcall MD_SLA_Challenge_Custom(void *usr_arg, const unsigned char  *p_challenge_in, unsigned int  challenge_in_len, unsigned char  **pp_challenge_out, unsigned int  *p_challenge_out_len);
	int __stdcall MD_SLA_Challenge_Custom_END(void *usr_arg, unsigned char  *p_challenge_out);
	int __stdcall MD_SLA_Challenge_Get_Method();
	/* PL SLA */
	int __stdcall PL_SLA_Challenge(void *usr_arg, const unsigned char  *p_challenge_in, unsigned int  challenge_in_len, unsigned char  **pp_challenge_out, unsigned int  *p_challenge_out_len);
	int __stdcall PL_SLA_Challenge_END(void *usr_arg, unsigned char  *p_challenge_out);
	/* Version */
	int __stdcall SLA_Challenge_Get_Version(unsigned int* major_ver, unsigned int* minor_ver, unsigned int* build_num, unsigned int* patch_num);

#ifdef __cplusplus
}
#endif

#endif
