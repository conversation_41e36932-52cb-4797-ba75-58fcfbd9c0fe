/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
/*****************************************************************************/
/*!
 * \file meta_conn.h
 * \mainpage META Connection and Logging Development Kit
 * \author MediaTek Inc.
 ******************************************
 * \defgroup META_Result META_RESULT
 * META RESULT is an enumeration type, please refer to the Mobile Equipment Testing Architecture Development Kit for more information.
 ******************************************
 * \defgroup General General
 * This section describe the general functions
 *
 * \defgroup GeneralStruct Structure of General
 * \ingroup General
 * GeneralStruct is a subgroup of General
 ******************************************
 * \defgroup Connection Connection
 * This section describe the connection functions
 *
 * \defgroup ConnectionStruct Structure of Connection
 * \ingroup Connection
 * ConnectionStruct is a subgroup of Connection
 ******************************************
 * \defgroup Logging Logging
 * This section describe the logging functions
 *
 * \defgroup LoggingStruct Structure for library logging and target logging functions
 * \ingroup Logging
 * LoggingStruct is a subgroup of Logging
 */

#ifndef __META_CONN_H__
#define __META_CONN_H__

#include <string>
#include "meta.h"
#include "export.h"
#include "message_box.h"

typedef struct
{
    unsigned int loggingType;
    unsigned int loggingAction;
} MD_Log_Setting;

typedef struct
{
    unsigned int apLoggingType;
    unsigned int apLoggingAction;
} AP_Log_Setting;

#ifdef  __cplusplus
extern "C" {
#endif

/**
  * <pre>
  * \ingroup General
  * \details Initialize META App handle before using the connection and logging functions
  *
  * \param [out] handle the handle of META App will be initialized for use
  * \param [in] mode given the data library mode for command composition
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_Init(int *handle, DATA_LIBRARY_MODE mode);

/**
  * <pre>
  * \ingroup General
  * \details Deinitialize META App handle before leave target context
  *
  * \param [in] handle the META App handle
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_DeInit(int handle);

/**
  * <pre>
  * \ingroup General
  * \details Query target supports the functionality of NVRAM backup or not
  *
  * \param [in] handle the META App handle
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FUNC_NOT_IMPLEMENT_YET This API was not implemented in the target.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_IsBackupSupported(int handle);

/**
  * <pre>
  * \ingroup General
  * \details Execute the functionality of NVRAM backup
  *
  * \param [in] handle the META App handle
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_Backup(int handle);

/**
  * <pre>
  * \ingroup General
  * \details Setup self defined pop-up window to META App for warning alerting
  *
  * \param [in] handle the META App handle
  * \param [in] cb the callback function for self defined pop-up window
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_HANDLE The handle or the cb is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_SetMsgBoxFunc(int handle, MsgBoxFunc cb);

/**
  * <pre>
  * \ingroup General
  * \details Setup the full path of the application which links META App library for tool logging
  *
  * \param [in] handle the META App handle
  * \param [in] path the full path of the application which links META App library
  *
  * \retval META_SUCCESS The operation succeeded.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_SetAppExeName(int handle, const wchar_t *path);

/**
  * <pre>
  * \ingroup General
  * \details Setup the path of the configuration file
  *
  * \param [in] handle the META App handle
  * \param [in] path the path of the configuration file
  *
  * \retval META_SUCCESS The operation succeeded.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_SetCfgFileName(int handle, const wchar_t *path);

/**
  * <pre>
  * \ingroup General
  * \details Get the modem command protocol
  *
  * \param [in] handle the META App handle
  * \param [in] md_idx the modem index
  * \param [out] srv the modem command protocol
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_GetMdSrv(int handle, unsigned int md_idx, META_MODEM_SRV *srv);

/**
  * <pre>
  * \ingroup General
  * \details Get the modem command channel type
  *
  * \param [in] handle the META App handle
  * \param [in] md_idx the modem index
  * \param [out] ch_type the modem command channel type
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_GetMdChType(int handle, unsigned int md_idx, META_MODEM_CH_TYPE *ch_type);

/**
  * <pre>
  * \ingroup General
  * \details Get the modem SLA status
  *
  * \param [in] handle the META App handle
  * \param [out] isEnabled the modem SLA enabled status
  * \param [out] isVerified the modem SLA verified status
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * \retval META_INVALID_ARGUMENTS The handle is invalid.
  * \retval others Check the definitions in META_GetErrorString
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_GetMdSlaStatus(int handle, bool *isEnabled, bool *isVerified);

/**
  * <pre>
  * \ingroup General
  * \details Enable modem command frame compression
  *
  * \param [in] handle the META App handle
  * \param [in] enabled enable compression
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * \retval META_INVALID_ARGUMENTS The handle is invalid.
  * \retval others Check the definitions in META_GetErrorString
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_EnableFrameCompression(int handle, bool enabled);

/**
  * <pre>
  * \ingroup General
  * \details Enable PASTA relay channel
  *
  * \param [in] handle the META App handle
  * \param [in] enabled the PASTA relay channel enabled status
  * \param [in] channelName the PASTA relay channel name
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The operation failed.
  * \retval META_INVALID_ARGUMENTS The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_EnablePastaRelay(int handle, bool enabled, const char *channelName);

/**
  * <pre>
  * \ingroup General
  * \details Query the target's capability to download the Modem NVRAM database
  *
  * \param [in] handle the META App handle
  * \param [out] isSupported the capability is supported by the target or not
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_ARGUMENTS The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_isSupportedGetTargetNvDatabaseFile(int handle, bool *isSupported);

/**
  * <pre>
  * \ingroup General
  * \details Download the Modem NVRAM database
  *
  * \param [in] handle the META App handle
  * \param [in] file_name the file name of Modem NVRAM database
  * \param [out] isGetFile the result of downloading Modem NVRAM database
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_ARGUMENTS The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_GetTargetNvDatabaseFile(int handle, wchar_t *file_name, bool *isGetFile);

/**
  * <pre>
  * \ingroup General
  * \details Reboot the target into normal mode
  *
  * \param [in] handle the META App handle
  * \param [in] para the disconnect settings including reboot control
  * \param [out] ret the result of reboot
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_ARGUMENTS The handle is invalid.
  * \retval META_NOT_SUPPORT the capability is not supported by the target.
  * \retval others Check the definitions in META_GetErrorString
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_RebootTargetToNormalMode(int handle, void *para, bool *ret);

/**
  * <pre>
  * \ingroup General
  * \details Setup Modem exception flag for stopping META App flow
  *
  * \param [in] handle the META App handle
  * \param [in] flagValue the specific value (0xEEEE) for notifying Modem exception
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_ARGUMENTS The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_SetMdExceptionStopFlag(int handle, int flagValue);

/**
  * <pre>
  * \ingroup General
  * \details Setup boot stop flag for stopping target booting flow
  *
  * \param [in] handle the META App handle
  * \param [in] flagValue the specific value (9876) for stopping target booting flow
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_ARGUMENTS The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_SetBootStopFlag(int handle, int flagValue);

/**
  * <pre>
  * \ingroup General
  * \details Cancel the blocking function calls of the specified META App handle
  *
  * \param [in] handle the META App handle
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_CancelAllBlockingCall(int handle);

/**
  * <pre>
  * \ingroup General
  * \details Cancel the blocking function calls of all META App handle
  *
  * \retval META_SUCCESS The operation succeeded.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_ForceCancelAllBlockingCall(void);

/**
 * <pre>
 * \ingroup General
 * \details Get version information from METACore library
 *
 * \param [in]  in_version_struct_type  specifies the library version structure type, which affects how to cast `out_version_info`
 * \param [out] out_version_info        pointer to the structure where the library version information will be stored
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_FAILED  The given version type and version info are not matched. Failed to get version information.
 *
 * \code
 * #include "METAVersionDefinition.h"
 * void example() {
 *     METALibraryVersionInfo info;
 *     META_RESULT result = MetaConn_GetLibraryVersionInfo(META_LIBRARY_VERSION_V1, &info);
 *     if (result == META_SUCCESS) {
 *         // Read value from info
 *     }
 * }
 * \endcode
 * </pre>
 */
META_RESULT METACONNAPI MetaConn_GetLibraryVersionInfo(int in_version_struct_type, void *out_version_info);

/**
  * <pre>
  * \ingroup Connection
  * \details Connect to calibration channel and logging channel on modem
  *
  * \param [in] handle the META App handle
  * \param [in] key the key string for differenct product connection ("FP Connection Module" or "SP Connection Module")
  * \param [in] para the connection parameters (FP_Conn_Input or SP_Conn_Input)
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * \retval others Check the definitions in META_GetErrorString
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_Connect(int handle, const char *key, void *para);

/**
  * <pre>
  * \ingroup Connection
  * \details Disconnect calibration channel and logging channel on modem
  *
  * \param [in] handle the META App handle
  * \param [in] para the disconnection parameters (FP_Disconn_Input or SP_Disconn_Input)
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * \retval others Check the definitions in META_GetErrorString
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_Disconnect(int handle, void *para);

/**
  * <pre>
  * \ingroup Logging
  * \details Setup the META App log path and enable logging
  *
  * \param [in] handle the META App handle
  * \param [in] path META App log path (*.log)
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_SetLogPath(int handle, const wchar_t *path);

/**
  * <pre>
  * \ingroup Logging
  * \details Setup the META App log path and enable logging with cumulative
  *
  * \param [in] handle the META App handle
  * \param [in] path META App log path (*.log)
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_SetLogPath_Cumulative(int handle, const wchar_t *path);

/**
  * <pre>
  * \ingroup Logging
  * \details Stop META App logging
  *
  * \param [in] handle the META App handle
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_DebugOff(int handle);

/**
  * <pre>
  * \ingroup Logging
  * \details Clear existing META App logs
  *
  * \param [in] handle the META App handle
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_DebugClear(int handle);

/**
  * <pre>
  * \ingroup Logging
  * \details Register callback function to retrieve runtime META App log
  *
  * \param [in] handle the META App handle
  * \param [in] levelBitmap the log levels
  * \param [in] observer the callback function for META App log
  * \param [out] observerId the ID of observer used for un-register log observer
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_RegisterLogObserver(const int handle, unsigned int levelBitmap, LogObserver observer, int *observerId);

/**
  * <pre>
  * \ingroup Logging
  * \details Un-register callback function to retrieve runtime META App log
  *
  * \param [in] handle the META App handle
  * \param [in] observerId the ID of observer
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_UnRegisterLogObserver(const int handle, int observerId);

/**
  * <pre>
  * \ingroup Logging
  * \details Get current modem logging mode
  *
  * \param [in] handle the META App handle
  * \param [out] the modem logging mode
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_GetMDLoggingMode(int handle, unsigned int *MdLogMode);

/**
  * <pre>
  * \ingroup Logging
  * \details Control AP and Modem loggger
  *
  * \param [in] handle the META App handle
  * \param [in] mdLogSetting the Modem settings
  * \param [in] apLogSetting the AP log settings
  * \param [in] filePath the AP and Modem log file path
  * \param [in] fileNamePrefix the self-defined prefix of file name
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * \retval others Check the definitions in META_GetErrorString
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_ControlMDLogger(int handle, MD_Log_Setting *mdLogSetting, AP_Log_Setting *apLogSetting = NULL, wchar_t *filePath = NULL, wchar_t *fileNamePrefix = NULL);

/**
  * <pre>
  * \ingroup Logging
  * \details Download the modem memory dump
  *
  * \param [in] handle the META App handle
  *
  * \retval META_SUCCESS The operation succeeded.
  * \retval META_FAILED  The function was failed.
  * \retval META_INVALID_HANDLE The handle is invalid.
  * \retval others Check the definitions in META_GetErrorString
  * </pre>
  */
META_RESULT METACONNAPI MetaConn_LoadMemDumpViaMiniComLogger(int handle);

#ifdef  __cplusplus
}
#endif



#endif
