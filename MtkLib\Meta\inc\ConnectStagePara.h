/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __CONNECTSTAGE_PARA_H__
#define __CONNECTSTAGE_PARA_H__

typedef void (*StageNameCB)(const char *stage_names[], unsigned int stage_map);
typedef void (*StageProgressCB)(int current_stage_index, int current_stage_progress);

typedef struct
{
    StageNameCB stageNameCB;
    StageProgressCB stageProgressCB;
} ConnectStageArg;

#endif
