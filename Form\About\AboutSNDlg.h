#if !defined(AFX_ABOUTSNDLG_H__EFD10ADB_9F61_4920_A469_E12C3BD1A851__INCLUDED_)
#define AFX_ABOUTSNDLG_H__EFD10ADB_9F61_4920_A469_E12C3BD1A851__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// AboutSNDlg.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CAboutSNDlg dialog

class CAboutSNDlg : public CDialog
{
// Construction
public:
	CAboutSNDlg(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CAboutSNDlg)
	enum { IDD = IDD_ABOUTSN_DIALOG };
		// NOTE: the ClassWizard will add data members here
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CAboutSNDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CAboutSNDlg)
	virtual BOOL OnInitDialog();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_ABOUTSNDLG_H__EFD10ADB_9F61_4920_A469_E12C3BD1A851__INCLUDED_)
