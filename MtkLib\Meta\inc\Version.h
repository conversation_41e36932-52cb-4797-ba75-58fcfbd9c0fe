// Version.h.in
#ifndef META_VERSION_H
#define META_VERSION_H

#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)

#define ST_MAJOR_VERSION        10
#define ST_MINOR_VERSION        2452
#define ST_REVISION             0
#define ST_PRODUCT_VERSION      "10.2452.0"

// vs sln: from CommonProperties.vsprops. cmake: from add_definition
#ifdef BUILD_CONFIG_NAME
#define VERSION_FLAVOR          TOSTRING(BUILD_CONFIG_NAME)
#else
#define VERSION_FLAVOR          "CustomerRelease"
#endif

#define VERSION_TAG             ""
#define BRANCH_INFO             ""
#define VERSION_SCM_ID          "fd6470420f"

// This string concat will cause build fail (error RC2104: undefined keyword or key name: Debug\)
// #define RC_PRODUCT_INFO         TOSTRING(BUILD_CONFIG_NAME) " Version ()"

#define RC_PRODUCT_INFO         "CustomerRelease Version - Official Build"

#endif
