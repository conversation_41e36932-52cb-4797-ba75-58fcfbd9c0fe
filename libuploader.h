#ifndef LIBUPLOADER_H_
#define LIBUPLOADER_H_

#include <string>
#include <unordered_map>

#include <LibKpaUtil.h>

#ifdef __cplusplus
extern "C" {
#endif

struct mbedtls_x509_crt;
typedef struct mbedtls_x509_crt mbedtls_x509_crt;

#ifdef __cplusplus
}
#endif

using trustkernel::Licenser;
using trustkernel::Device;

using std::string;
using std::unordered_map;

#ifdef LIBUPLOADER_EXPORTS
#define LIBUPLOADER_API __declspec(dllexport)
#else
#define LIBUPLOADER_API __declspec(dllimport)
#endif

namespace trustkernel {
    class Network;
    class Uploader;
    class VTurkeyLicenser;
    class KeyboxContainer;
    class DeviceRest;
};

#pragma warning(push)
#pragma warning(disable:4251)

struct PostUserData {
    mbedtls_x509_crt *caCert;
    mbedtls_x509_crt *sslCert;
    string sslKey;

    PostUserData(mbedtls_x509_crt *_caCert, mbedtls_x509_crt *_sslCert, string _sslKey):
        caCert(_caCert), sslCert(_sslCert), sslKey(_sslKey) { }

    PostUserData():
        caCert(NULL), sslCert(NULL), sslKey("") { }
};

class LIBUPLOADER_API trustkernel::Network {
private:
    void *sslPrivData;
    void initSslPrivData();

public:
    Network();
    ~Network();

    static Network *thisPtr;
    static Network *getNetwork();

    static void lock() { }
    static void unlock() { }

    /* functions must be called before using class Uploader */
    static bool globalInitCalled;
    static void globalInit();
    static void globalCleanup();

    int post(const string &url, const string &body, string &resp);

    int postWithClientCert(struct PostUserData *postUserDatta,
                const string &url, const string &body, string &resp);

    int get(const string &url, string &resp);
};

class LIBUPLOADER_API trustkernel::Uploader {
private:
    Network *network;
    Device *device;

    string host;
    struct PostUserData *postUserData;

    int parsePCKS12Cert(const string &clientCertPath, const string &certPassword,
                        string &key, mbedtls_x509_crt *cert);
    string buildUploadRequest();

public:
    Uploader(Network *n, Device *d);
    ~Uploader();

    static const string PLSERVER_HOST;
    int upload(const string &clientCertPath, const string &certPassword);
};

class LIBUPLOADER_API trustkernel::VTurkeyLicenser: public trustkernel::Licenser {
private:
    Network *network;
    string host;

    string buildSecondaryCertRequest(Device *device,
                                     bool requireKeybox,
                                     const string &keyboxUuid);
    string buildRevokeLicenseRequest(Device *device, const string &keyboxUuid);

    int secondaryCertRequest(Device *device, string &secondaryCert);
    int revokeLicenseRequest(Device *device, string &revokeLicense);

public:
    static const string VTURKEY_HOST;

    VTurkeyLicenser(Network *n);

    virtual int getSecondaryLicenseLeftCount();
    virtual int issueSecondaryLicense(Device *device, unsigned char *buf, size_t buf_len);

    virtual int issueRevokeLicense(Device *device, unsigned char *buf, size_t bufLength);
};

class  LIBUPLOADER_API trustkernel::KeyboxContainer {
private:
    KeyboxContainer();
    ~KeyboxContainer();

    static KeyboxContainer *thisPtr;
    std::unordered_map<string, string> keyboxStore;

public:
    static KeyboxContainer *getInstance();

    bool installKeybox(const string& keyboxUuid, const string& keyboxData);
    bool hasKeyboxUuid(const string &keyboxUuid);
    string getKeyboxByUuid(const string &keyboxUuid);
};

class LIBUPLOADER_API trustkernel::DeviceRest {
private:
    Network *network;
    Device *device;

    string host;

public:
    DeviceRest(Network *n, Device *d);
    ~DeviceRest() {}

    static const string DEVICEREST_HOST;

    int getServerNonce(const string &deviceUuid, string &nonce);
    int enrollDevice(const string &data, const string &signature,
                     string &respData, string &respSignature);
};

#pragma warning(pop)

#endif