[hardware]
;;CDROM_FriendlyName=MEDIATEK  FLASH DISK USB Device
1 = "USBSTOR\CdRomLinux___File-Stor_Gadget0355"
2 = "USBSTOR\CdRomLinux___File-Stor_Gadget"
3 = "USBSTOR\CdRomLinux___"
4 = "USBSTOR\CdRomMEDIATEK_FLASH_DISK________"
5 = "USBSTOR\CdRomMEDIATEK_FLASH_DISK____"
6 = "USBSTOR\CdRom&Ven_MEDIATEK&Prod__DRIVER_DISK"
CTL_PATH=USB\Vid_0e8d&Pid_00A2&MI_01
BaudRate=115200
FlowCtrl=0 ; 0:none flowctrl, 1:hardware flowctrl 2:software flowctrl
RNDIS_CTL_PATH=USB\Vid_0e8d&Pid_7111
EnableRescueMode=false
[software]
;; Scan devices periodically in milliseconds (default 1000 ms)
ScanInterval=1000
;; Repeat times of scanning devices (default 10 times)
ScanRepeat=10
;; Do NOT scan and send command to specific CDROM/com port/RNDIS device
DisableCDROMScan=false
DisableComPortScan=false
DisableRNDISScan=false