/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __SP_CONN_PARA__
#define __SP_CONN_PARA__

#include "meta.h"
#include "conn_para.h"

#ifdef _WIN32
#include "sp_brom.h"
#endif //#ifdef _WIN32

typedef  struct
{
    char *connect_send_cmd;
    char *connect_ack_cmd;
    char *disconnect_send_cmd;
    char *disconnect_ack_cmd;
} SP_AT_CMD_SETTING_T;

typedef struct
{
    bool                   BootModeFlag;       /**< [META] Decide target will boot meta mode or normal mode. 0: normal mode, need preloader handshake META 1:already meta mode boot */
    bool                   usb_auto_detec;     /**< [META] USB auto detection selection */
    bool                   secure_boot;        /**< [META] Selection of secure boot */
    bool                   send_at_cmd_to_connect_md_meta_mode;
    bool                   con_sp_detect_booting_completed;
    SP_AT_CMD_SETTING_T    at_cmd_setting;
} SP_Conn_Para;
typedef struct
{
    unsigned int           MDloggingType;        /**< [META] MDlogger logging, 0: disable logging. 0x01: USB. 0x02: SD card. DHL meta trace, 0x10: relay to ELT. 0x20: dump to file. */
    unsigned int           APloggingType;        /**< [META] Mobilelog logging, 0: disable logging. 0x01: USB. 0x02: SD card. */
    wchar_t               *MDlogFilePath;
    wchar_t               *APlogFilePath;
    wchar_t               *logFileNamePrefix;
} SP_Logging_Para;

typedef struct
{
    Conn_Para input_para;
    SP_Conn_Para sp_input_para;
#ifdef _WIN32
    SP_BOOT_ARG_S *sp_boot_arg;
    SP_PL_SLA *sp_pl_sla_arg;
#endif //#ifdef _WIN32
    int *boot_stop;
    bool c2k_log_relay_enable;
} SP_Conn_Input;

typedef struct
{
    bool b_shutdown;
    bool b_backup;
    bool b_switchFlag;
    bool b_bootFlag;
    bool b_sendAtCmdFlag;
    char *apdb_pathname;
    SP_Logging_Para disLog_para;
} SP_Disconn_Input;

#endif
