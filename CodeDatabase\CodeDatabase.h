#ifndef AGENEW_DATABASE_H
#define AGENEW_DATABASE_H
#pragma warning(disable : 4786)
#include <map>
#include <string>
#include <Windows.h>
#include "Common.h"

struct CodesColumn
{
    int imei1;
    int imei2;
    int sn;
    int bt_mac;
    int wifi_mac;
    int barcode;
    int meid;
};

struct Codes
{
    std::string imei1;
    std::string imei2;
    std::string sn;
    std::string bt_mac;
    std::string wifi_mac;
    std::string barcode;
    std::string meid;

    bool isEmpty()
    {
        return imei1.empty() && barcode.empty();
    }

    static bool isSNValid(const std::string &sn)
    {
        return !sn.empty();
    }

    // if mac addr format wrong, return empty string
    static std::string getWritableMacAddr(const std::string &macAddr)
    {
        switch (macAddr.size())
        {
        case 12:
            {
                for (size_t i=0; i<macAddr.size(); i++)
                {
                    char c = macAddr[i];
                    bool valid = c>='0'&&c<='9' || c>='a'&&c<='f' || c>='A'&&c<='F';
                    if (!valid) return std::string();
                }
                return macAddr;
            }
        case 17:
            {
                std::string ret;
                for (size_t i=0; i<macAddr.size(); i++)
                {
                    char c = macAddr[i];
                    switch (i%3)
                    {
                    case 0:
                    case 1:
                        {
                            bool valid = c>='0'&&c<='9' || c>='a'&&c<='f' || c>='A'&&c<='F';
                            if (!valid) return std::string();
                            ret += macAddr[i];
                            break;
                        }
                    case 2:
                        {
                            if (c!=':' && c!='-') return std::string();
                            break;
                        }
                    }
                }
                return ret;
            }
        default:
            return std::string();
        };
    }
};

class CodeDatabase
{
public:
    static CodeDatabase* inst();

    bool reloadDatabase(HWND parent, char * okMsg, size_t okMsgLen);

    // if codeName invalid, return empty string
    Codes getCode(const std::string &scanCode, E_SCAN_CODE_TYPE scanCodeType);
    Codes getCode(const ScanData_struct &scanData, E_SCAN_CODE_TYPE scanCodeType);

    bool checkScanCodeTypeReady(E_SCAN_CODE_TYPE scanCodeType, char * errMsg, size_t msgLen);
    
private:
    CodeDatabase();

    bool readTitleRow(HWND parent, const char * line, char * okMsg, size_t okMsgLen);
    Codes line2Codes(const char * line, int lineIndex, char * msg, size_t errMsgLen);

    std::map<std::string, Codes>  _recordsByIMEI1;
    std::map<std::string, Codes> _recordsByBarcode;
    CodesColumn _column;
    int _codeTypeCount;

    bool _isScanBarcodeReady;
    bool _isScanIMEI1Ready;
};

#endif