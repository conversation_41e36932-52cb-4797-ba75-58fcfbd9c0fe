#pragma once

#include <string>

using namespace std;

class MesServerProxy
{
public:
    MesServerProxy(void);
    ~MesServerProxy(void);

	bool CheckUserAndResourcePassed(char* user, char* resource, char* password, char *oErrMessage);
	bool CheckRoutePassed(char* iSN, char* resource, char *oErrMessage);
	bool SetMobileData(char* iSN, char* resource, char* user, char* status, char* item, char* value, char *oErrMessage);
	bool UploadFile(char* resource, char* model, char* logpath, char* iSN, char* data, char* file, char* status, char *oErrMessage);
	bool GetAddressRangerByMo(char* iSN, char* resourse, char *oErrMessage);
	bool SetAddressInfo(char* iSN, char* sign, char *oErrMessage);
	bool GetMEIOrNetCodeRange(char* iSN, char* resource, char *oErrMessage);
	bool SetImeiInfo(char* iSN, char* sign, char *oErrMessage);
	bool GetIMobileAllinfo(char* iSN, char *oErrMessage);
	bool SetAttachment(char* resource, char* type, char* iSN, char* barcode, char* user, char *oErrMessage);
	bool GetAllCode(char* resource, char* iSN, char* barcode, char *oErrMessage);
	bool SetAllInfo(char* iSN, char* sign, char *oErrMessage);

private:
    void Shell(LPSTR args, char* result, int size);
};