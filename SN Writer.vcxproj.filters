﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{2be0ddb4-d3bd-40a6-8c70-409d85dedcdc}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Source Files\Common_SRC">
      <UniqueIdentifier>{97bc0f8f-294a-4b3b-9583-9629bdc825f2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Exec_SRC">
      <UniqueIdentifier>{b241dbed-915f-4268-863e-cf2bcd584949}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\MtkLib_SRC">
      <UniqueIdentifier>{eeda2250-2833-4613-841d-ec5b98a8e0a4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\MtkLib_SRC\ATCmd_SRC">
      <UniqueIdentifier>{219c1920-ecad-40cb-bb5c-17b60e95b974}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\MtkLib_SRC\DebugTrace_SRC">
      <UniqueIdentifier>{c66c3f3d-0e30-44a8-95e6-1168071f9bef}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\MtkLib_SRC\Security_SRC">
      <UniqueIdentifier>{bd9191b1-acc1-446f-a447-d305796a77b8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Form_SRC">
      <UniqueIdentifier>{3f27e73f-f9da-40cc-a1c6-700273462e47}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Form_SRC\FileSel_SRC">
      <UniqueIdentifier>{c2d57143-1ea1-4730-b933-31c17815abf0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Form_SRC\Identify_SRC">
      <UniqueIdentifier>{695f3f80-3eb1-4d71-a071-04903a75548b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Form_SRC\ScanData_SRC">
      <UniqueIdentifier>{8b92bf4b-2708-4dfe-97c0-ae613f6f79c4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Form_SRC\SystemConfig_SRC">
      <UniqueIdentifier>{f13b653f-0aee-46a1-9266-36cb85056dd1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Form_SRC\About_SRC">
      <UniqueIdentifier>{f93226bc-c074-46e8-acf7-95e759b01075}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Form_SRC\AutoGen_SRC">
      <UniqueIdentifier>{4dc6c53c-2108-45db-a796-3d35689c3a2e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Extra_SRC">
      <UniqueIdentifier>{0efc9ef9-4139-472b-af11-e59980fae61a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Extra_SRC\BtnST_SRC">
      <UniqueIdentifier>{efcf9a72-12a3-42b0-9525-2ebbb0d5d108}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Agenew_SRC">
      <UniqueIdentifier>{80424326-4201-40d8-b83d-7dc00f84643b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{89f4961e-3883-4434-ae22-56195d831a25}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files\Common_HDR">
      <UniqueIdentifier>{3632e6e3-a9be-46d3-a5ed-217cc7c8ed8c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Exec_HDR">
      <UniqueIdentifier>{91f929a3-d17c-409a-a7c3-ee51f58aa798}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\MtkLib_HDR">
      <UniqueIdentifier>{143aad05-f6a9-4262-bcec-8cdfe2ad4b27}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\MtkLib_HDR\Meta_HDR">
      <UniqueIdentifier>{bd8617de-5331-4a5d-ba1a-7a45bce60d23}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\MtkLib_HDR\ATCmd_HDR">
      <UniqueIdentifier>{020a322d-6d6b-495f-85e0-47ff60b2ccc9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\MtkLib_HDR\DebugTrace_HDR">
      <UniqueIdentifier>{3c25cf70-536f-40c4-94e4-e1194b6917b9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\MtkLib_HDR\Security_HDR">
      <UniqueIdentifier>{ad54a9fa-2db5-43a3-a2c8-d995bd9a7aca}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\MtkLib_HDR\SNFstream_HDR">
      <UniqueIdentifier>{49799f64-ee92-4ef6-91eb-6657dfa094cb}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\MtkLib_HDR\USBSwitchTool_HDR">
      <UniqueIdentifier>{d5a889d6-b443-481c-b251-ae6c69564505}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Form_HDR">
      <UniqueIdentifier>{c361f503-0f1d-4c8a-896a-9dc2019c5e3c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Form_HDR\FileSel_HDR">
      <UniqueIdentifier>{b8d0f224-3999-4aa1-8848-9406ac9c8c43}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Form_HDR\Identify_HDR">
      <UniqueIdentifier>{0922bb99-6af5-4e5a-aaf1-96f369ac4a22}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Form_HDR\ScanData_HDR">
      <UniqueIdentifier>{ec09d95f-1834-476a-b255-406e4ab26b54}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Form_HDR\SystemConfig_HDR">
      <UniqueIdentifier>{e272ed41-7d12-41fd-b71e-07bbe500bfbc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Form_HDR\About_HDR">
      <UniqueIdentifier>{ebd7872c-225d-45cc-911e-a0ab59c06327}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Form_HDR\AutoGen_HDR">
      <UniqueIdentifier>{494fc905-3234-4c69-9d44-6fef4edf5269}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Extra_HDR">
      <UniqueIdentifier>{3bee5b3d-2c6b-447f-80a8-6c066e269ed2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Extra_HDR\BtnST_HDR">
      <UniqueIdentifier>{df8edd01-c742-467e-acab-dea93ddc1d04}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Agenew_HDR">
      <UniqueIdentifier>{8daded59-b8d8-4127-a107-640cf703a1cb}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{c79aeee0-7629-457b-9073-629b64917c71}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Encryption.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MesConnectDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MesServerProxy.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SN Writer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SN WriterDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Common\Common.cpp">
      <Filter>Source Files\Common_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Execute\SNbase.cpp">
      <Filter>Source Files\Exec_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Execute\SPexc.cpp">
      <Filter>Source Files\Exec_SRC</Filter>
    </ClCompile>
    <ClCompile Include="MtkLib\AT_cmd\src\AtProcess.cpp">
      <Filter>Source Files\MtkLib_SRC\ATCmd_SRC</Filter>
    </ClCompile>
    <ClCompile Include="MtkLib\AT_cmd\src\rs232Module.cpp">
      <Filter>Source Files\MtkLib_SRC\ATCmd_SRC</Filter>
    </ClCompile>
    <ClCompile Include="MtkLib\DebugTrace\src\xboot_debug.cpp">
      <Filter>Source Files\MtkLib_SRC\DebugTrace_SRC</Filter>
    </ClCompile>
    <ClCompile Include="MtkLib\Security\src\AUTH.cpp">
      <Filter>Source Files\MtkLib_SRC\Security_SRC</Filter>
    </ClCompile>
    <ClCompile Include="MtkLib\Security\src\SCERT.cpp">
      <Filter>Source Files\MtkLib_SRC\Security_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Form\FileSel\FileSelDlg.cpp">
      <Filter>Source Files\Form_SRC\FileSel_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Form\Identify\ChgPasswdDlg.cpp">
      <Filter>Source Files\Form_SRC\Identify_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Form\Identify\VerifyPasswdDlg.cpp">
      <Filter>Source Files\Form_SRC\Identify_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Form\ScanData\ScanData.cpp">
      <Filter>Source Files\Form_SRC\ScanData_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Form\SystemConfig\SystemConfig.cpp">
      <Filter>Source Files\Form_SRC\SystemConfig_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Form\About\AboutSNDlg.cpp">
      <Filter>Source Files\Form_SRC\About_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Form\AutoGen\autogendlg.cpp">
      <Filter>Source Files\Form_SRC\AutoGen_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Extra\CBtnST\BtnST.cpp">
      <Filter>Source Files\Extra_SRC\BtnST_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Extra\CBtnST\CeXDib.cpp">
      <Filter>Source Files\Extra_SRC\BtnST_SRC</Filter>
    </ClCompile>
    <ClCompile Include="Extra\CBtnST\ShadeButtonST.cpp">
      <Filter>Source Files\Extra_SRC\BtnST_SRC</Filter>
    </ClCompile>
    <ClCompile Include="CodeDatabase\CodeDatabase.cpp">
      <Filter>Source Files\Agenew_SRC</Filter>
    </ClCompile>
    <ClCompile Include="MesConnectDlg2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HTMesProxy.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CheckFlagDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HttpApi.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WininetHttp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="inttypes.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="LibKpaUtil.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="LibKPHA.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="libuploader.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="MesConnectDlg.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="MesServerProxy.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="Encryption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SN Writer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SN WriterDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Common\Common.h">
      <Filter>Header Files\Common_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Execute\SNbase.h">
      <Filter>Header Files\Exec_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Execute\SPexc.h">
      <Filter>Header Files\Exec_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\C2kAgent_api.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\C2kAgent_api_datatype.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\FtaAgent_api.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\HdcpEncryption.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\meta.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\meta_boot_def.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\meta_dll_audio.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\meta_dll_connectivity.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\meta_dll_gps.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\meta_dll_mm.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\meta_dll_nfc.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\mtk_mcu.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\SLA_Challenge.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\sp_brom.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\sp_conn_para.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\sp_md_info.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Meta\inc\xboot.h">
      <Filter>Header Files\MtkLib_HDR\Meta_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\AT_cmd\inc\AtProcess.h">
      <Filter>Header Files\MtkLib_HDR\ATCmd_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\AT_cmd\inc\rs232Module.h">
      <Filter>Header Files\MtkLib_HDR\ATCmd_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\DebugTrace\inc\Mdebug.h">
      <Filter>Header Files\MtkLib_HDR\DebugTrace_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\DebugTrace\inc\xboot_debug.h">
      <Filter>Header Files\MtkLib_HDR\DebugTrace_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Security\inc\AUTH.h">
      <Filter>Header Files\MtkLib_HDR\Security_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Security\inc\des.h">
      <Filter>Header Files\MtkLib_HDR\Security_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\Security\inc\SCERT.h">
      <Filter>Header Files\MtkLib_HDR\Security_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\SNFstream\inc\AutoGen.h">
      <Filter>Header Files\MtkLib_HDR\SNFstream_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\SNFstream\inc\SNFstream.h">
      <Filter>Header Files\MtkLib_HDR\SNFstream_HDR</Filter>
    </ClInclude>
    <ClInclude Include="MtkLib\USBSwitchTool\inc\USBSwitchDLLibrary.h">
      <Filter>Header Files\MtkLib_HDR\USBSwitchTool_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Form\FileSel\FileSelDlg.h">
      <Filter>Header Files\Form_HDR\FileSel_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Form\Identify\ChgPasswdDlg.h">
      <Filter>Header Files\Form_HDR\Identify_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Form\Identify\VerifyPasswdDlg.h">
      <Filter>Header Files\Form_HDR\Identify_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Form\ScanData\ScanData.h">
      <Filter>Header Files\Form_HDR\ScanData_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Form\SystemConfig\SystemConfig.h">
      <Filter>Header Files\Form_HDR\SystemConfig_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Form\About\AboutSNDlg.h">
      <Filter>Header Files\Form_HDR\About_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Form\AutoGen\autogendlg.h">
      <Filter>Header Files\Form_HDR\AutoGen_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Extra\CBtnST\BtnST.h">
      <Filter>Header Files\Extra_HDR\BtnST_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Extra\CBtnST\CeXDib.h">
      <Filter>Header Files\Extra_HDR\BtnST_HDR</Filter>
    </ClInclude>
    <ClInclude Include="Extra\CBtnST\ShadeButtonST.h">
      <Filter>Header Files\Extra_HDR\BtnST_HDR</Filter>
    </ClInclude>
    <ClInclude Include="CodeDatabase\CodeDatabase.h">
      <Filter>Header Files\Agenew_HDR</Filter>
    </ClInclude>
    <ClInclude Include="LibTurkey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MesConnectDlg2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HTMesProxy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CheckFlagDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HttpApi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WininetHttp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\bitmap1.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\close_btn.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\FAIL.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\logo.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\logoNuu.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\menu_btn.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\min_btn.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\PASS.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\SN Writer.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="SN Writer.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Library Include="LibKpaUtil.lib" />
    <Library Include="LibKPHA.lib" />
    <Library Include="libturkey.lib" />
    <Library Include="LibUploader.lib" />
  </ItemGroup>
</Project>