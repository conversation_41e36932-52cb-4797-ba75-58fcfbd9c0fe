// SN WriterDlg.cpp : implementation file
//

#include "StdAfx.h"
#include "SN Writer.h"
#include "SN WriterDlg.h"
#include "SystemConfig.h"
#include "ScanData.h"
#include "BtnST.h"
#include "AboutSNDlg.h"
#include "ChgPasswdDlg.h"
#include "VerifyPasswdDlg.h"
#include "FileSelDlg.h"
#include "AutoGenDlg.h"
#include "CheckFlagDialog.h"
#include "Common.h"
#include "SNBase.h"
#include "HdcpEncryption.h"
/*TrustKernel add */
#include "LibKPHA.h"
#include "CodeDatabase/CodeDatabase.h" // jiali add
#include "MesServerProxy.h"
#include "HTMesProxy.h"
#include <shobjidl.h>  // 用于现代文件对话框
#include <shlobj.h>    // 用于Shell相关函数

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CAboutDlg dialog used for App About
#define  MAX_COMPORT_COUNT 255
unsigned short g_iTotalComPort = MAX_COMPORT_COUNT;
unsigned short g_iComportArray[MAX_COMPORT_COUNT] = {0};

const char *g_strToolVersion = "NUU_SN_Writer_v2152_20250908";

extern CSNWriterDlg *g_pMainDlg;

/////////////////////////////////////////////////////////////////////////////
// CSNWriterDlg dialog

CSNWriterDlg::CSNWriterDlg(CWnd* pParent /*=NULL*/)
: CDialog(CSNWriterDlg::IDD, pParent)
, WINDOW_TITLEBAR_HEIGHT(36) // jiali add for Agenew logo
{
    //{{AFX_DATA_INIT(CSNWriterDlg)
	m_strOrder = _T("");
    m_strTotal = _T("");
    m_strPass = _T("");
    m_strFail = _T("");
    //}}AFX_DATA_INIT
    // Note that LoadIcon does not require a subsequent DestroyIcon in Win32
    m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
    m_isDialogMaxminzed = false; // jiali add for Agenew logo
}

void CSNWriterDlg::DoDataExchange(CDataExchange* pDX)
{
    CDialog::DoDataExchange(pDX);
    //{{AFX_DATA_MAP(CSNWriterDlg)

    DDX_Control(pDX, IDC_COMBO_TARGET_TYPE, m_cTargetTypeBox);
    DDX_Control(pDX, IDC_COMBO_COMPORT, m_cComPortBox);
    DDX_Control(pDX, IDC_PROGRESS_SN, m_ProgressSN);
	DDX_Text(pDX, IDC_ORDER_EDIT, m_strOrder);
    DDX_Text(pDX, IDC_COUNT_TOTAL, m_strTotal);
    DDX_Text(pDX, IDC_COUNT_PASS, m_strPass);
    DDX_Text(pDX, IDC_COUNT_FAIL, m_strFail);
    //}}AFX_DATA_MAP

    DDX_Control(pDX, IDC_BTN_INDITE, m_InditeBTN);

    DDX_Control(pDX, IDC_BTN_START, m_StartBTN);
    DDX_Control(pDX, IDC_BTN_QUIT, m_QuitBTN);
    DDX_Control(pDX, IDC_BTN_SYSCONFIG, m_SystemCfgBTN);
}

BEGIN_MESSAGE_MAP(CSNWriterDlg, CDialog)
    //{{AFX_MSG_MAP(CSNWriterDlg)
    ON_WM_PAINT()
    ON_WM_QUERYDRAGICON()
    ON_BN_CLICKED(IDC_BTN_START, OnBtnStart)
    ON_BN_CLICKED(IDC_BTN_QUIT, OnBtnQuit)
    ON_BN_CLICKED(IDC_BTN_SYSCONFIG, OnBtnSysconfig)
    ON_CBN_SELCHANGE(IDC_COMBO_COMPORT, OnSelchangeComboComport)
    ON_CBN_SELCHANGE(IDC_COMBO_TARGET_TYPE, OnSelchangeComboTargetType)
	ON_WM_CTLCOLOR()
    ON_COMMAND(IDR_ABOUT_SN, OnAboutSn)
    ON_COMMAND(IDR_ATCMD_MODE, OnAtcmdMode)
    ON_COMMAND(IDR_CHANGE_PASSWD, OnChangePasswd)
    ON_COMMAND(IDR_ENG_TO_OPERATER, OnEngToOperater)
    ON_COMMAND(IDR_OPTION_ALREADYINMETA, OnOptionAlreadyInMeta)
    ON_COMMAND(IDR_META_MODE, OnMetaMode)
    ON_COMMAND(IDR_OPERATOR_TO_ENG, OnOperatorToEng)
    ON_COMMAND(IDR_OPTION_DRMKEY, OnOptionDrmkey)
    ON_COMMAND(IDR_OPTION_EXTMODEM, OnOptionExtmodem)
    ON_COMMAND(IDR_OPTION_HDCP, OnOptionHdcp)
    ON_COMMAND(IDR_OPTION_SECURITY_USB, OnOptionSecurityUsb)
    ON_COMMAND(IDR_OPTION_SWITCHTOOL, OnOptionSwitchtool)
    ON_COMMAND(IDR_SP_AUTH_FILE, OnSpAuthFile)
    ON_COMMAND(IDR_OPTION_HDCPDATA, OnOptionHdcpdata)
    ON_COMMAND(IDR_OPTION_ATTESTATION, OnOptionAttestationKey)
    ON_COMMAND(IDR_ENABLE_AUTOGEN, OnEnableAutogen)
    ON_COMMAND(IDR_OPTION_CHECK_CAL_FLAG, OnOptionCheckCalFlag)
    ON_COMMAND(IDR_OPTION_CHECK_FT_FLAG, OnOptionCheckFtFlag)
	ON_COMMAND(IDR_OPTION_CHECKFLAG, OnOptionCheckFlag)
    ON_COMMAND(IDR_OPTION_SKIP_WRITE_PRODINFO, OnOptionSkipWriteProdinfo)
    ON_COMMAND(IDR_CHECK_BTWIFI, OnCheckBtwifi)
    ON_COMMAND(IDR_OPTION_COMPOSITE_DEVICE, OnOptionCompositeDevice)
    ON_COMMAND(IDR_BACKNV_TOPC, OnBacknvTopc)
    ON_COMMAND(IDR_OEM_LOCK, OnFastbootOemLock)
    ON_WM_SYSCOMMAND()
    // jiali add for Agenew logo, begin
	ON_BN_CLICKED(IDC_MENU_BTN, OnMenuBtn)
	ON_BN_CLICKED(IDC_MIN_BTN, OnMinBtn)
	ON_BN_CLICKED(IDC_CLOSE_BTN, OnCloseBtn)
    ON_WM_LBUTTONDOWN()
	// jiali add for Agenew logo, end
	ON_COMMAND(IDR_SUPPORT_TEE, OnSupportTEE)
	//}}AFX_MSG_MAP
	ON_COMMAND(ID_OPTION_GETCSRFROMDUT, &CSNWriterDlg::OnOptionGetcsrfromdut)
	ON_STN_CLICKED(IDC_OPERATE_INFO, &CSNWriterDlg::OnStnClickedOperateInfo)
	ON_NOTIFY(NM_CUSTOMDRAW, IDC_PROGRESS_SN, &CSNWriterDlg::OnNMCustomdrawProgressSn)
	ON_COMMAND(ID_RKP_UPLOADCSRTOYN, &CSNWriterDlg::OnRkpUploadcsrtoyn)
	ON_EN_CHANGE(IDC_ORDER_EDIT, &CSNWriterDlg::OnEnChangeOrderEdit)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSNWriterDlg message handlers

BOOL CSNWriterDlg::OnInitDialog()
{
    CDialog::OnInitDialog();

    // Add "About..." menu item to system menu.

    // IDM_ABOUTBOX must be in the system command range.
    //ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
    // ASSERT(IDM_ABOUTBOX < 0xF000);

    CMenu* pSysMenu = GetSystemMenu(FALSE);
    if (pSysMenu != NULL)
    {
        CString strAboutMenu;
        strAboutMenu.LoadString(IDS_ABOUTBOX);
        if (!strAboutMenu.IsEmpty())
        {
            pSysMenu->AppendMenu(MF_SEPARATOR);
            //pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
        }
    }

    // Set the icon for this dialog.  The framework does this automatically
    //  when the application's main window is not a dialog
    SetIcon(m_hIcon, TRUE);			// Set big icon
    SetIcon(m_hIcon, FALSE);		// Set small icon
     char str_kpha_Version[50];
	sprintf(str_kpha_Version, "%s-TEE-v%u",
	g_strToolVersion, Get_KPHA_Version());
    SetWindowText(g_strToolVersion);
	SetWindowText(str_kpha_Version);

    // TODO: Add extra initialization here
    //g_MainDlgHandle = (DWORD)this;

    m_SNMenu.LoadMenu(IDR_SN_MENU);
    m_hSNMenu = m_SNMenu.GetSafeHmenu();

#ifdef _LOAD_SKIN_
    skinppSetDrawMenu(m_hSNMenu, TRUE);
#endif

    //m_hSNMenu = LoadMenu(AfxGetInstanceHandle(),MAKEINTRESOURCE(IDR_SN_MENU));
    //::SetMenu(this->GetSafeHwnd(),m_hSNMenu); // jiali modify for Agenew logo

    m_hBitmapFail = ::LoadBitmap(AfxGetInstanceHandle(), MAKEINTRESOURCE(IDB_BITMAP_FAIL));
    m_hBitmapPass = ::LoadBitmap(AfxGetInstanceHandle(), MAKEINTRESOURCE(IDB_BITMAP_PASS));
    ((CWnd*)GetDlgItem(IDC_BTN_INDITE))->ShowWindow(SW_HIDE);

#ifdef _BITMAP_BTN_
    m_InditeBTN.SetBitmap(m_hBitmapPass);
#else
    m_InditeBTN.SetShade(CShadeButtonST::SHS_METAL);
#endif

    m_StartBTN.SetShade(CShadeButtonST::SHS_SOFTBUMP);
    m_QuitBTN.SetShade(CShadeButtonST::SHS_SOFTBUMP);
    m_SystemCfgBTN.SetShade(CShadeButtonST::SHS_SOFTBUMP);

    // Get main dialog handle
    g_pMainDlg = (CSNWriterDlg*)this;
    ParameterInit();

	char title[128];
	if(g_sMetaComm.smesconnect.strMes[0] == 'L')
	{
		sprintf(title, "%s (LA MES)", g_strToolVersion);
	}
	else
	{
		sprintf(title, "%s (HT MES)", g_strToolVersion);
	}
	g_strToolVersion = title;
	
	SetDlgItemText(IDC_ORDER_EDIT, g_sMetaComm.strOrder);

    // jiali add for Agenew logo, begin
    {
        GetDlgItem(IDC_DIALOG_TITLE)->SetWindowText(g_strToolVersion);

        CBitmap Bitmap;
        Bitmap.LoadBitmap(IDB_EXIT_NORMAL);
        HBITMAP hBitmap=(HBITMAP)Bitmap.Detach();
        ((CButton*)GetDlgItem(IDC_CLOSE_BTN))->SetBitmap(hBitmap);

        Bitmap.LoadBitmap(IDB_MIN_NORMAL);
        hBitmap=(HBITMAP)Bitmap.Detach();
        ((CButton*)GetDlgItem(IDC_MIN_BTN))->SetBitmap(hBitmap);

        Bitmap.LoadBitmap(IDB_MENU_NORMAL);
        hBitmap=(HBITMAP)Bitmap.Detach();
        ((CButton*)GetDlgItem(IDC_MENU_BTN))->SetBitmap(hBitmap);

        // resize title bar white bg
        CRect wr;
        GetWindowRect(&wr);
        wr.bottom = WINDOW_TITLEBAR_HEIGHT;
        wr.top = 0;
        wr.right = wr.right - wr.left;
        wr.left = 0;
        GetDlgItem(IDC_TITLEBAR_BG)->MoveWindow(wr, TRUE);
    }
    // jiali add for Agenew logo, end

    // jiali add for database.csv, begin
    char okMsg[1024] = {0};
    if (CodeDatabase::inst()->reloadDatabase(this->m_hWnd, okMsg, sizeof(okMsg)))
    {
        GetDlgItem(IDC_OPERATE_INFO)->SetWindowTextA(okMsg);

        strcat_s(okMsg, "\r\n实际写的码，与配置有关。请在System Config中勾上要写的码！");
    }
    {
        char msg[1024] = {0};
        CodeDatabase::inst()->checkScanCodeTypeReady(g_sMetaComm.eScanCodeType, msg, sizeof(msg));
        strcat_s(okMsg, "\r\n");
        strcat_s(okMsg, msg);
        GetDlgItem(IDC_OPERATE_INFO)->SetWindowTextA(okMsg);
    }
    // jiali add for database.csv, end

	BOOL ret =HTMesProxy::inst()->DBHelper_Library_DynamicLoad();
	if (!ret)
	{
		return FALSE;
	}

    return TRUE;  // return TRUE  unless you set the focus to a control
}

void CSNWriterDlg::MenuItemInit()
{
    CMenu * pSubMenu = NULL;

    //sub menu - option
    pSubMenu = m_SNMenu.GetSubMenu(1);

    if (g_sMetaComm.bAlreadyInMeata)
    {
        pSubMenu->CheckMenuItem(IDR_OPTION_ALREADYINMETA, MF_CHECKED | MF_BYCOMMAND);
    }

    if (g_sMetaComm.eOperatorMode == META_MODE)
    {
        pSubMenu->GetSubMenu(1)->CheckMenuItem(IDR_META_MODE, MF_CHECKED | MF_BYCOMMAND);
        pSubMenu->GetSubMenu(1)->CheckMenuItem(IDR_ATCMD_MODE, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else if (g_sMetaComm.eOperatorMode == AT_MODE)
    {
        pSubMenu->GetSubMenu(1)->CheckMenuItem(IDR_ATCMD_MODE, MF_CHECKED | MF_BYCOMMAND);
        pSubMenu->GetSubMenu(1)->CheckMenuItem(IDR_META_MODE, MF_UNCHECKED | MF_BYCOMMAND);
    }

    if (g_sMetaComm.bSwithTool)
    {
        g_sMetaComm.bDualTalk = false;
        pSubMenu->CheckMenuItem(IDR_OPTION_SWITCHTOOL, MF_CHECKED | MF_BYCOMMAND);
        pSubMenu->CheckMenuItem(IDR_OPTION_EXTMODEM, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else if (g_sMetaComm.bDualTalk)
    {
        g_sMetaComm.bSwithTool = false;
        pSubMenu->CheckMenuItem(IDR_OPTION_EXTMODEM, MF_CHECKED | MF_BYCOMMAND);
        pSubMenu->CheckMenuItem(IDR_OPTION_SWITCHTOOL, MF_UNCHECKED | MF_BYCOMMAND);
    }

    if (g_sMetaComm.bSecurityUSB)
    {
        pSubMenu->CheckMenuItem(IDR_OPTION_SECURITY_USB, MF_CHECKED | MF_BYCOMMAND);
    }

    if (g_sMetaComm.bCheckCalFlag)
    {
        pSubMenu->GetSubMenu(6)->CheckMenuItem(IDR_OPTION_CHECK_CAL_FLAG, MF_CHECKED | MF_BYCOMMAND);
    }

    if (g_sMetaComm.bCheckFtFlag)
    {
        pSubMenu->GetSubMenu(6)->CheckMenuItem(IDR_OPTION_CHECK_FT_FLAG, MF_CHECKED | MF_BYCOMMAND);
    }

	if (g_sMetaComm.bCheckFlag)
    {
        pSubMenu->CheckMenuItem(IDR_OPTION_CHECKFLAG, MF_CHECKED | MF_BYCOMMAND);
    }
    if (g_sMetaComm.bSkipWriteProdInfo)
    {
        pSubMenu->CheckMenuItem(IDR_OPTION_SKIP_WRITE_PRODINFO , MF_CHECKED | MF_BYCOMMAND);
    }

    if (g_sMetaComm.bCompositeDeviceEnable)
    {
        pSubMenu->CheckMenuItem(IDR_OPTION_COMPOSITE_DEVICE, MF_CHECKED | MF_BYCOMMAND);
    }
    if (g_sMetaComm.bCheckBackNVtoPC)
    {
        pSubMenu->CheckMenuItem(IDR_BACKNV_TOPC, MF_CHECKED | MF_BYCOMMAND);
    }
    if (g_sMetaComm.bCheckFastbootLock)
    {
        pSubMenu->CheckMenuItem(IDR_OEM_LOCK, MF_CHECKED | MF_BYCOMMAND);
    }

    // sub menu - Hdcp_DrmKey
    pSubMenu = m_SNMenu.GetSubMenu(2);
    if (g_sMetaComm.eTargetType == TABLET_WIFI_ONLY)
    {
        pSubMenu->EnableMenuItem(IDR_OPTION_HDCP, MF_GRAYED | MF_DISABLED | MF_BYCOMMAND);
        pSubMenu->EnableMenuItem(IDR_OPTION_HDCPDATA, MF_GRAYED | MF_DISABLED | MF_BYCOMMAND);
    }
    else
    {
        if (g_sMetaComm.sWriteOption.bWriteHdcp)
            pSubMenu->CheckMenuItem(IDR_OPTION_HDCP, MF_CHECKED | MF_BYCOMMAND);
        if (g_sMetaComm.sWriteOption.bInstallHdcpData)
            pSubMenu->CheckMenuItem(IDR_OPTION_HDCPDATA, MF_CHECKED | MF_BYCOMMAND);
    }
    if (g_sMetaComm.sWriteOption.bWriteDrm)
        pSubMenu->CheckMenuItem(IDR_OPTION_DRMKEY, MF_CHECKED | MF_BYCOMMAND);
    if (g_sMetaComm.sWriteOption.bWriteAttestationKey)
        pSubMenu->CheckMenuItem(IDR_OPTION_ATTESTATION, MF_CHECKED | MF_BYCOMMAND);
	if (g_sMetaComm.sWriteOption.bGetCsrFromDut)
        pSubMenu->CheckMenuItem(ID_OPTION_GETCSRFROMDUT, MF_CHECKED | MF_BYCOMMAND);
	if (g_sMetaComm.sWriteOption.bUploadCsrToYN)
        pSubMenu->CheckMenuItem(ID_RKP_UPLOADCSRTOYN, MF_CHECKED | MF_BYCOMMAND);

    //sub menu - identify
    pSubMenu = m_SNMenu.GetSubMenu(3);
    if (g_LoginIdentify == ENGINEER)
    {
        pSubMenu->CheckMenuItem(0, MF_CHECKED | MF_BYPOSITION);
        pSubMenu->CheckMenuItem(1, MF_UNCHECKED | MF_BYPOSITION);
        if (g_AutoGenData.bEnableAutoGen)
        {
            pSubMenu->GetSubMenu(0)->CheckMenuItem(IDR_ENABLE_AUTOGEN, MF_CHECKED | MF_BYCOMMAND);
        }
        else
        {
            pSubMenu->GetSubMenu(0)->CheckMenuItem(IDR_ENABLE_AUTOGEN, MF_UNCHECKED | MF_BYCOMMAND);
        }

        if (g_sMetaComm.bCheckBTWifi)
        {
            pSubMenu->GetSubMenu(0)->CheckMenuItem(IDR_CHECK_BTWIFI, MF_CHECKED | MF_BYCOMMAND);
        }
        else
        {
            pSubMenu->GetSubMenu(0)->CheckMenuItem(IDR_CHECK_BTWIFI, MF_UNCHECKED | MF_BYCOMMAND);
        }
    }
    else if (g_LoginIdentify == OPERATOR)
    {
        pSubMenu->CheckMenuItem(1, MF_CHECKED | MF_BYPOSITION);
        pSubMenu->CheckMenuItem(0, MF_UNCHECKED | MF_BYPOSITION);
    }

	    g_sMetaComm.bCheckSupportTEE = true;

    if (g_sMetaComm.bCheckSupportTEE) {
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_SUPPORT_TEE, MF_CHECKED | MF_BYCOMMAND);
    } else {
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_SUPPORT_TEE, MF_UNCHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::ParameterInit()
{
    InitSNIni();

    MenuItemInit();

    EnableUIItem();

    SetUIItem();

}
void CSNWriterDlg::EnableStartBTN(bool bEnable)
{
    (CWnd*)GetDlgItem(IDC_BTN_START)->EnableWindow(bEnable);
}

void CSNWriterDlg::EnableUIItem()
{
    GetDlgItem(IDC_COMBO_COMPORT)->EnableWindow(TRUE);

    if (g_LoginIdentify == ENGINEER)
    {
        GetDlgItem(IDC_BTN_SYSCONFIG)->EnableWindow(TRUE);
        GetDlgItem(IDC_COMBO_TARGET_TYPE)->EnableWindow(TRUE);

        m_SNMenu.EnableMenuItem(0, MF_ENABLED | MF_BYPOSITION);
        m_SNMenu.EnableMenuItem(1, MF_ENABLED | MF_BYPOSITION);
        m_SNMenu.EnableMenuItem(2, MF_ENABLED | MF_BYPOSITION);
        m_SNMenu.EnableMenuItem(3, MF_ENABLED | MF_BYPOSITION);
        m_SNMenu.GetSubMenu(3)->EnableMenuItem(0, MF_ENABLED | MF_BYPOSITION);
        m_SNMenu.GetSubMenu(3)->EnableMenuItem(1, MF_GRAYED | MF_DISABLED | MF_BYPOSITION);
    }
    else
    {
        GetDlgItem(IDC_BTN_SYSCONFIG)->EnableWindow(FALSE);
        GetDlgItem(IDC_COMBO_TARGET_TYPE)->EnableWindow(FALSE);
        m_SNMenu.EnableMenuItem(0, MF_GRAYED | MF_DISABLED | MF_BYPOSITION);
        m_SNMenu.EnableMenuItem(1, MF_GRAYED | MF_DISABLED | MF_BYPOSITION);
        m_SNMenu.EnableMenuItem(2, MF_GRAYED | MF_DISABLED | MF_BYPOSITION);
        m_SNMenu.EnableMenuItem(3, MF_ENABLED | MF_BYPOSITION);
        m_SNMenu.GetSubMenu(3)->EnableMenuItem(0, MF_GRAYED | MF_DISABLED | MF_BYPOSITION);
        m_SNMenu.GetSubMenu(3)->EnableMenuItem(1, MF_ENABLED | MF_BYPOSITION);
    }

    SetDlgItemText(IDC_BTN_START, "Start");
}

void CSNWriterDlg::DisableUIItem()
{
    GetDlgItem(IDC_BTN_SYSCONFIG)->EnableWindow(FALSE);
    GetDlgItem(IDC_COMBO_TARGET_TYPE)->EnableWindow(FALSE);
    GetDlgItem(IDC_COMBO_COMPORT)->EnableWindow(FALSE);
    m_SNMenu.EnableMenuItem( 0, MF_GRAYED | MF_DISABLED | MF_BYPOSITION );
    m_SNMenu.EnableMenuItem( 1, MF_GRAYED | MF_DISABLED | MF_BYPOSITION );
    m_SNMenu.EnableMenuItem( 2, MF_GRAYED | MF_DISABLED | MF_BYPOSITION );
    m_SNMenu.EnableMenuItem(3, MF_GRAYED | MF_DISABLED | MF_BYPOSITION);
    m_SNMenu.GetSubMenu(3)->EnableMenuItem(0, MF_GRAYED | MF_DISABLED | MF_BYPOSITION);
    m_SNMenu.GetSubMenu(3)->EnableMenuItem(1, MF_GRAYED | MF_DISABLED | MF_BYPOSITION);

    SetDlgItemText(IDC_BTN_START, "Stop");
}

void CSNWriterDlg::PopupMsgBox(const char* strTilte, UINT uType, const char* strMsg, ...)
{
    char szBuf[256] = "";

    va_list varg;
    va_start(varg, strMsg);
    vsprintf_s(szBuf, strMsg, varg);
    va_end(varg);
    MessageBox(strMsg, strTilte, uType);
}

void CSNWriterDlg::GetIndicatorPos(CRect &rect)
{
    ((CWnd*)GetDlgItem(IDC_BTN_INDITE))->GetWindowRect(&rect);
}

void CSNWriterDlg::UpdateOperateResult(WriteNvram_Status_e eWriteStatus)
{
	char * error = (char *)malloc(sizeof(char)*1024);
	char * Imei1 = "";
	bool ret;
#ifdef _BITMAP_BTN_
    switch (eWriteStatus)
    {
    case FAIL:
        ((CWnd*)GetDlgItem(IDC_BTN_INDITE))->ShowWindow(SW_SHOW);
        m_InditeBTN.SetBitmap(m_hBitmapFail);
		
		if (g_sMetaComm.smesconnect.mesconnectcheck == TRUE)
		{
			bool ret;
			char *buf = (char *)malloc(sizeof(char)*1024);

			if(g_sMetaComm.smesconnect.strMes[0] == 'L')
			{
				MesServerProxy proxy;
				ret = proxy.SetMobileData(g_sMetaComm.smesconnect.Sn, g_sMetaComm.smesconnect.Resource, g_sMetaComm.smesconnect.Username, "NG", "SN_Writer", "", buf);

				if (!ret)
				{
					//MessageBox(buf);
					UpdateUIMsg("%s", buf);
				}
			}
		}
        break;

    case PASS:
        ((CWnd*)GetDlgItem(IDC_BTN_INDITE))->ShowWindow(SW_SHOW);
        m_InditeBTN.SetBitmap(m_hBitmapPass);
		((CWnd*)GetDlgItem(IDC_ORDER_EDIT))->EnableWindow(FALSE);

		if (g_sMetaComm.smesconnect.mesconnectcheck == TRUE)
		{
			bool ret;
			char *buf = (char *)malloc(sizeof(char)*1024);

			if(g_sMetaComm.smesconnect.strMes[0] == 'L')
			{
				MesServerProxy proxy;
				ret = proxy.SetMobileData(g_sMetaComm.smesconnect.Sn, g_sMetaComm.smesconnect.Resource, g_sMetaComm.smesconnect.Username, "OK", "SN_Writer", "", buf);

				if (!ret)
				{
					//MessageBox(buf);
					UpdateUIMsg("%s", buf);
				}
			}
			else
			{
				/*ret = HTMesProxy::inst()->DB_UpdateImeiXh(
					g_sMetaComm.smesconnect.Gdh, 
					g_sMetaComm.smesconnect.Xh, 
					g_sMetaComm.smesconnect.Imei,
					"",
					"",
					"",
					g_sMetaComm.smesconnect.Msn,
					g_sMetaComm.smesconnect.Sn,
					g_sMetaComm.smesconnect.Wifi,
					g_sMetaComm.smesconnect.Bt,
					g_sMetaComm.smesconnect.Scx,
					g_sMetaComm.smesconnect.User, 
					buf);*/
				ret = HTMesProxy::inst()->DB_UpdateImeiXh(g_sMetaComm.smesconnect.Imei, buf);
				if (!ret)
				{
					//MessageBox(buf);
					UpdateUIMsg("%s", buf);
				}
				HTMesProxy::inst()->DB_CloseDBConnect();
			}
		}
        break;

    case NORMAL:
        ((CWnd*)GetDlgItem(IDC_BTN_INDITE))->ShowWindow(SW_HIDE);
        break;

    case CANCEL:
    default:
        //Do nothing
        break;
    }
#endif
}

void CSNWriterDlg::GetUIItem()
{
    OnSelchangeComboComport();
    OnSelchangeComboTargetType();
}

void CSNWriterDlg::SwitchToEngineer()
{
    g_LoginIdentify = ENGINEER;
    SaveLoginIdentify(ENGINEER);
    EnableUIItem();
}

void CSNWriterDlg::SwitchToOperator()
{
    g_LoginIdentify = OPERATOR;
    SaveLoginIdentify(OPERATOR);
    EnableUIItem();
}

void CSNWriterDlg::GetComPortDynamiclly()
{
    char DestString[10];
    int i = 0, result = 0;

    result = SP_ComPortEnumerate(g_iComportArray, &g_iTotalComPort);
    if(result != SP_S_DONE)
    {
        g_iTotalComPort = 0;
    }

    if(g_iTotalComPort < 1 || g_iTotalComPort >= MAX_COMPORT_COUNT)
    {
        m_cComPortBox.InsertString(0, "USB VCOM");
    }
    else
    {
        for(i = 0; i < g_iTotalComPort; i++)	// show all com port got dynamiclly
        {
            sprintf_s(DestString, "COM%d", g_iComportArray[g_iTotalComPort - i - 1]);
            m_cComPortBox.InsertString(0, DestString);
        }

        m_cComPortBox.InsertString(0, "USB VCOM");
    }

    m_cComPortBox.SetCurSel(0);

}

void CSNWriterDlg::SetUIItem()
{
    GetComPortDynamiclly();

    m_cTargetTypeBox.InsertString(SMART_PHONE, "Smart Phone");
    //m_cTargetTypeBox.InsertString(SMART_PHONE_DUALMODEM, "SP DualModem");
    //m_cTargetTypeBox.InsertString(TABLET_WIFI_ONLY, "Tablet Wifi Only");
    //m_cTargetTypeBox.InsertString(RNDIG_DONGLE, "Rndis Dongle");
    m_cTargetTypeBox.SetCurSel(g_sMetaComm.eTargetType);
}

// If you add a minimize button to your dialog, you will need the code below
//  to draw the icon.  For MFC applications using the document/view model,
//  this is automatically done for you by the framework.

void CSNWriterDlg::OnPaint()
{
    if (IsIconic())
    {
        CPaintDC dc(this); // device context for painting

        SendMessage(WM_ICONERASEBKGND, (WPARAM) dc.GetSafeHdc(), 0);

        // Center icon in client rectangle
        int cxIcon = GetSystemMetrics(SM_CXICON);
        int cyIcon = GetSystemMetrics(SM_CYICON);
        CRect rect;
        GetClientRect(&rect);
        int x = (rect.Width() - cxIcon + 1) / 2;
        int y = (rect.Height() - cyIcon + 1) / 2;

        // Draw the icon
        dc.DrawIcon(x, y, m_hIcon);
    }
    else
    {
        CDialog::OnPaint();
    }
}

// The system calls this to obtain the cursor to display while the user drags
//  the minimized window.
HCURSOR CSNWriterDlg::OnQueryDragIcon()
{
    return (HCURSOR) m_hIcon;
}

void CSNWriterDlg::OnBtnStart()
{
    // TODO: Add your control notification handler code here
    EnableStartBTN(false);

    OnSelchangeComboTargetType();
    OnSelchangeComboComport();
    E_TARGET_TYPE eTargetType = GetTargetType();

    CString strText;
    ((CWnd*)GetDlgItem(IDC_BTN_START))->GetWindowText(strText);

    if (strText == "Start")
    {
        m_spSN.SNThread_Start();
    }
    else if(strText == "Stop")
    {
        m_spSN.SNThread_Stop();
        EnableUIItem();
    }
}

void CSNWriterDlg::OnBtnQuit()
{
    // TODO: Add your control notification handler code here
    CDialog::OnCancel();
}

BOOL CSNWriterDlg::PreTranslateMessage(MSG* pMsg)
{
    // TODO: Add your specialized code here and/or call the base class
    if ( pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE )
    {
        return TRUE;
    }
    return CDialog::PreTranslateMessage(pMsg);
}

BOOL CSNWriterDlg::DestroyWindow()
{
    // TODO: Add your specialized code here and/or call the base class

    SaveParaToSetupFile();
    AuthHandle_DeInit();

    return CDialog::DestroyWindow();
}

void CSNWriterDlg::OnBtnSysconfig()
{
    // TODO: Add your control notification handler code here
    E_SCAN_CODE_TYPE oldSCT = g_sMetaComm.eScanCodeType; // jiali add

    CSystemConfig dlg;
	if (IDOK == dlg.DoModal())
    {
        ResetUIItem();
    }
    // jiali add begin, for recheck scan code type
    E_SCAN_CODE_TYPE newSCT = g_sMetaComm.eScanCodeType;
    if (newSCT != oldSCT)
    {
        char msg[1024] = {0};
        CodeDatabase::inst()->checkScanCodeTypeReady(g_sMetaComm.eScanCodeType, msg, sizeof(msg));
        GetDlgItem(IDC_OPERATE_INFO)->SetWindowTextA(msg);
    }
    // jiali add end
	SetDlgItemText(IDC_ORDER_EDIT, g_sMetaComm.strOrder);
}

void CSNWriterDlg::OnSelchangeComboComport()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);

    CString strComport;
    m_cComPortBox.GetWindowText(strComport);
    if(strComport.Find("USB", 0) != -1)
    {
        g_sMetaComm.bUsbEnable = true;
        g_sMetaComm.iCOMPort = 0;
    }
    else
    {
        char *pStr = (LPSTR)(LPCTSTR)strComport;
        g_sMetaComm.bUsbEnable = false;
        pStr += 3;
        g_sMetaComm.iCOMPort = atoi(pStr);
    }
}

void CSNWriterDlg::OnSelchangeComboTargetType()
{
    // TODO: Add your control notification handler code here
    UpdateData(TRUE);
    g_sMetaComm.eTargetType =(E_TARGET_TYPE)m_cTargetTypeBox.GetCurSel();

    if (g_sMetaComm.eTargetType == RNDIG_DONGLE)
    {
        g_sMetaComm.bSwithTool = true;
        g_sMetaComm.bDualTalk = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SWITCHTOOL, MF_CHECKED | MF_BYCOMMAND);    // USB SwithTool option
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_EXTMODEM, MF_UNCHECKED | MF_BYCOMMAND);    // Extern modem option
    }
    else
    {
        g_sMetaComm.bSwithTool = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SWITCHTOOL, MF_UNCHECKED | MF_BYCOMMAND);  // USB SwithTool
    }

    if(g_sMetaComm.eTargetType == TABLET_WIFI_ONLY)
    {
        g_sMetaComm.sWriteOption.bWriteHdcp = false;
        g_sMetaComm.sWriteOption.bInstallHdcpData = false;
        m_SNMenu.GetSubMenu(2)->EnableMenuItem(IDR_OPTION_HDCP, MF_GRAYED | MF_DISABLED | MF_BYCOMMAND);
        m_SNMenu.GetSubMenu(2)->EnableMenuItem(IDR_OPTION_HDCPDATA, MF_GRAYED | MF_DISABLED | MF_BYCOMMAND);
        m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_HDCP, MF_UNCHECKED);
        m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_HDCPDATA, MF_UNCHECKED);
    }
    else
    {
        m_SNMenu.GetSubMenu(2)->EnableMenuItem(IDR_OPTION_HDCP, MF_ENABLED | MF_BYCOMMAND);
        m_SNMenu.GetSubMenu(2)->EnableMenuItem(IDR_OPTION_HDCPDATA, MF_ENABLED | MF_BYCOMMAND);
    }
}

HBRUSH CSNWriterDlg::OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor)
{
    HBRUSH hbr = CDialog::OnCtlColor(pDC, pWnd, nCtlColor);

    // TODO: Change any attributes of the DC here
    if((IDC_MAINUI_TEMP1 == pWnd->GetDlgCtrlID())||(IDC_MAINUI_TEMP2 == pWnd->GetDlgCtrlID()))
    {
        pDC->SetTextColor(RGB(255,0,0));
    }

    // TODO: Return a different brush if the default is not desired

    // jiali add for Agenew logo begin
    if (pWnd->GetDlgCtrlID() == IDC_DIALOG_TITLE)
    {
        pDC->SetBkColor(RGB(255, 255, 255)); // bg color just below char rect
        HBRUSH b=CreateSolidBrush(RGB(255,255,255)); // bg color for static text rect
        return b;
    }
    // jiali add for Agenew logo end
    return hbr;
}

void CSNWriterDlg::OnAboutSn()
{
    // TODO: Add your command handler code here
    CAboutSNDlg dlg;
    dlg.DoModal();
}

void CSNWriterDlg::OnChangePasswd()
{
    // TODO: Add your command handler code here
    CChgPasswdDlg dlg;
    dlg.DoModal();
}

void CSNWriterDlg::OnEngToOperater()
{
    // TODO: Add your command handler code here
    SwitchToOperator();
}

void CSNWriterDlg::OnOperatorToEng()
{
    // TODO: Add your command handler code here
    CVerifyPasswdDlg dlg;
    if(IDOK == dlg.DoModal())
    {
        SwitchToEngineer();
    }
}

void CSNWriterDlg::OnOptionAlreadyInMeta()
{
    if (g_sMetaComm.bAlreadyInMeata)
    {
        g_sMetaComm.bAlreadyInMeata = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_ALREADYINMETA, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bAlreadyInMeata = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_ALREADYINMETA, MF_CHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnMetaMode()
{
    // TODO: Add your command handler code here
    g_sMetaComm.eOperatorMode = META_MODE;
    m_SNMenu.GetSubMenu(1)->GetSubMenu(1)->CheckMenuItem(IDR_META_MODE, MF_CHECKED | MF_BYCOMMAND);
    m_SNMenu.GetSubMenu(1)->GetSubMenu(1)->CheckMenuItem(IDR_ATCMD_MODE, MF_UNCHECKED | MF_BYCOMMAND);
}

void CSNWriterDlg::OnAtcmdMode()
{
    // TODO: Add your command handler code here

    //Current only support meta mode
    g_sMetaComm.eOperatorMode = META_MODE;
    m_SNMenu.GetSubMenu(1)->GetSubMenu(1)->CheckMenuItem(IDR_META_MODE, MF_CHECKED | MF_BYCOMMAND);
    m_SNMenu.GetSubMenu(1)->GetSubMenu(1)->CheckMenuItem(IDR_ATCMD_MODE, MF_UNCHECKED | MF_BYCOMMAND);

    MessageBox("Current only support meta mode!", "Warning", MB_ICONWARNING);

    /*
    g_sMetaComm.eOperatorMode = AT_MODE;
    m_SNMenu.GetSubMenu(1)->GetSubMenu(1)->CheckMenuItem(0, MF_UNCHECKED | MF_BYPOSITION);
    m_SNMenu.GetSubMenu(1)->GetSubMenu(1)->CheckMenuItem(1, MF_CHECKED | MF_BYPOSITION);
    */
}

void CSNWriterDlg::OnOptionExtmodem()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.bDualTalk)
    {
        g_sMetaComm.bDualTalk = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_EXTMODEM, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bSwithTool = false;
        g_sMetaComm.bDualTalk = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_EXTMODEM, MF_CHECKED | MF_BYCOMMAND);
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SWITCHTOOL, MF_UNCHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnOptionSwitchtool()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.bSwithTool)
    {
        g_sMetaComm.bSwithTool = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SWITCHTOOL, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bSwithTool = true;
        g_sMetaComm.bDualTalk = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SWITCHTOOL, MF_CHECKED | MF_BYCOMMAND);
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_EXTMODEM, MF_UNCHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnOptionSecurityUsb()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.bSecurityUSB)
    {
        g_sMetaComm.bSecurityUSB = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SECURITY_USB, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bSecurityUSB = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SECURITY_USB, MF_CHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnOptionCheckCalFlag()
{
    // TODO: Add your command handler code here
    if(g_sMetaComm.bCheckCalFlag)
    {
        g_sMetaComm.bCheckCalFlag = false;
        m_SNMenu.GetSubMenu(1)->GetSubMenu(6)->CheckMenuItem(IDR_OPTION_CHECK_CAL_FLAG, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bCheckCalFlag = true;
        m_SNMenu.GetSubMenu(1)->GetSubMenu(6)->CheckMenuItem(IDR_OPTION_CHECK_CAL_FLAG, MF_CHECKED | MF_BYCOMMAND);
    }

}

void CSNWriterDlg::OnOptionCheckFtFlag()
{
    // TODO: Add your command handler code here
    if(g_sMetaComm.bCheckFtFlag)
    {
        g_sMetaComm.bCheckFtFlag = false;
        m_SNMenu.GetSubMenu(1)->GetSubMenu(6)->CheckMenuItem(IDR_OPTION_CHECK_FT_FLAG, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bCheckFtFlag = true;
        m_SNMenu.GetSubMenu(1)->GetSubMenu(6)->CheckMenuItem(IDR_OPTION_CHECK_FT_FLAG, MF_CHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnOptionCheckFlag()
{
	if (g_sMetaComm.bCheckFlag)
    {
        g_sMetaComm.bCheckFlag = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_CHECKFLAG, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bCheckFlag = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_CHECKFLAG, MF_CHECKED | MF_BYCOMMAND);
		CCheckFlagDialog dlg;
        dlg.DoModal();
    }
}
void CSNWriterDlg::OnOptionSkipWriteProdinfo()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.bSkipWriteProdInfo)
    {
        g_sMetaComm.bSkipWriteProdInfo = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SKIP_WRITE_PRODINFO, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bSkipWriteProdInfo = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SKIP_WRITE_PRODINFO, MF_CHECKED | MF_BYCOMMAND);
    }
}


void CSNWriterDlg::OnSpAuthFile()
{
    // TODO: Add your command handler code here
    CFileSelDlg dlg;
    dlg.SetLoadFileType(LOAD_SP_AUTH_FILE);
    if (IDOK == dlg.DoModal())
    {
        g_sMetaComm.bSecurityUSB = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_SECURITY_USB, MF_CHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnOptionHdcp()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.sWriteOption.bWriteHdcp)
    {
        g_sMetaComm.sWriteOption.bWriteHdcp = false;
        m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_HDCP, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        CFileSelDlg dlg;
        dlg.SetLoadFileType(LOAD_HDCP_BIN_FILE);
        if (IDOK == dlg.DoModal())
        {
            g_sMetaComm.sWriteOption.bWriteHdcp = true;
            m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_HDCP, MF_CHECKED | MF_BYCOMMAND);
        }
    }
}

void CSNWriterDlg::OnOptionDrmkey()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.sWriteOption.bWriteDrm)
    {
        g_sMetaComm.sWriteOption.bWriteDrm = false;
        m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_DRMKEY, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        CFileSelDlg dlg;
        dlg.SetLoadFileType(LOAD_DRMKEY_FILE);
        if (IDOK == dlg.DoModal())
        {
            g_sMetaComm.sWriteOption.bWriteDrm = true;
            m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_DRMKEY, MF_CHECKED | MF_BYCOMMAND);
        }
    }
}

void CSNWriterDlg::OnOptionHdcpdata()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.sWriteOption.bInstallHdcpData)
    {
        g_sMetaComm.sWriteOption.bInstallHdcpData = false;
        m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_HDCPDATA, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        CFileSelDlg dlg;
        dlg.SetLoadFileType(LOAD_HDCP_DATA_FILE);
        if (IDOK == dlg.DoModal())
        {
            g_sMetaComm.sWriteOption.bInstallHdcpData = true;
            m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_HDCPDATA, MF_CHECKED | MF_BYCOMMAND);
        }
    }
}

void CSNWriterDlg::OnEnableAutogen()
{
    // TODO: Add your command handler code here
    CAutoGenDlg dlg;
    dlg.DoModal();
    if (g_AutoGenData.bEnableAutoGen)
    {
        m_SNMenu.GetSubMenu(3)->GetSubMenu(0)->CheckMenuItem(IDR_ENABLE_AUTOGEN, MF_CHECKED | MF_BYCOMMAND);
    }
    else
    {
        m_SNMenu.GetSubMenu(3)->GetSubMenu(0)->CheckMenuItem(IDR_ENABLE_AUTOGEN, MF_UNCHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnCheckBtwifi()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.bCheckBTWifi)
    {
        g_sMetaComm.bCheckBTWifi = false;
        m_SNMenu.GetSubMenu(3)->GetSubMenu(0)->CheckMenuItem(IDR_CHECK_BTWIFI, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bCheckBTWifi = true;
        m_SNMenu.GetSubMenu(3)->GetSubMenu(0)->CheckMenuItem(IDR_CHECK_BTWIFI, MF_CHECKED | MF_BYCOMMAND);
    }

}

void CSNWriterDlg::OnOptionCompositeDevice()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.bCompositeDeviceEnable)
    {
        g_sMetaComm.bCompositeDeviceEnable = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_COMPOSITE_DEVICE, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bCompositeDeviceEnable = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OPTION_COMPOSITE_DEVICE, MF_CHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnBacknvTopc()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.bCheckBackNVtoPC)
    {
        g_sMetaComm.bCheckBackNVtoPC = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_BACKNV_TOPC, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bCheckBackNVtoPC = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_BACKNV_TOPC, MF_CHECKED | MF_BYCOMMAND);
    }

}

void CSNWriterDlg::OnFastbootOemLock()
{
    // TODO: Add your command handler code here
    if (g_sMetaComm.bCheckFastbootLock)
    {
        g_sMetaComm.bCheckFastbootLock = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OEM_LOCK, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bCheckFastbootLock = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_OEM_LOCK, MF_CHECKED | MF_BYCOMMAND);
    }

}

// jiali add for Agenew logo begin
void CSNWriterDlg::OnCloseBtn()
{
	this->OnCancel();
}

void CSNWriterDlg::OnMinBtn()
{
    PostMessage(WM_SYSCOMMAND, SC_MINIMIZE, 0);
}

void CSNWriterDlg::OnMenuBtn()
{
    CMenu m;
    m.CreatePopupMenu();
    m.AppendMenu(MF_POPUP, (UINT)m_hSNMenu, "asdf");
    RECT rect;
    GetDlgItem(IDC_MENU_BTN)->GetWindowRect(&rect);
    m.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTBUTTON/*|TPM_VERTICAL*/, rect.left, rect.bottom, this);
    m.Detach();
}

void CSNWriterDlg::OnLButtonDown(UINT nFlags, CPoint point) 
{
    if (point.y <= WINDOW_TITLEBAR_HEIGHT)
    {
        // trigger OnNcHitTest, make dialog dragable and movable
        PostMessage(WM_NCLBUTTONDOWN,HTCAPTION,MAKELPARAM(point.x,point.y));
    }
	
	CDialog::OnLButtonDown(nFlags, point);
}

// jiali add for Agenew logo end

void CSNWriterDlg::OnSupportTEE()
{
    if (g_sMetaComm.bCheckSupportTEE)
    {
        g_sMetaComm.bCheckSupportTEE = false;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_SUPPORT_TEE, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.bCheckSupportTEE = true;
        m_SNMenu.GetSubMenu(1)->CheckMenuItem(IDR_SUPPORT_TEE, MF_CHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnOptionAttestationKey()
{
    if (g_sMetaComm.sWriteOption.bWriteAttestationKey)
    {
        g_sMetaComm.sWriteOption.bWriteAttestationKey = false;
        m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_ATTESTATION, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        CFileSelDlg dlg;
        dlg.SetLoadFileType(LOAD_ATTESTATION_KEY_FILE);
        if (dlg.DoModal() == IDOK)
        {
            g_sMetaComm.sWriteOption.bWriteAttestationKey = true;
            m_SNMenu.GetSubMenu(2)->CheckMenuItem(IDR_OPTION_ATTESTATION, MF_CHECKED | MF_BYCOMMAND);
        }
    }
}

void CSNWriterDlg::OnOptionGetcsrfromdut()
{
	// TODO: 在此添加命令处理程序代码
	if (g_sMetaComm.sWriteOption.bGetCsrFromDut)
    {
        g_sMetaComm.sWriteOption.bGetCsrFromDut = false;
		m_SNMenu.GetSubMenu(2)->CheckMenuItem(ID_OPTION_GETCSRFROMDUT, MF_UNCHECKED | MF_BYCOMMAND);
		g_sMetaComm.sWriteOption.bUploadCsrToYN = false;
		m_SNMenu.GetSubMenu(2)->CheckMenuItem(ID_RKP_UPLOADCSRTOYN, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.sWriteOption.bGetCsrFromDut = true;
        m_SNMenu.GetSubMenu(2)->CheckMenuItem(ID_OPTION_GETCSRFROMDUT, MF_CHECKED | MF_BYCOMMAND);

        // 使用现代的文件夹选择对话框，支持显示当前路径
        IFileOpenDialog *pFileOpen = NULL;
        HRESULT hr = CoCreateInstance(CLSID_FileOpenDialog, NULL, CLSCTX_ALL,
                                      IID_IFileOpenDialog, reinterpret_cast<void**>(&pFileOpen));

        if (SUCCEEDED(hr))
        {
            // 设置对话框选项为选择文件夹
            DWORD dwOptions;
            if (SUCCEEDED(pFileOpen->GetOptions(&dwOptions)))
            {
                pFileOpen->SetOptions(dwOptions | FOS_PICKFOLDERS);
            }

            // 设置对话框标题
            pFileOpen->SetTitle(L"请选择CSR保存文件夹");

            // 如果已经有保存路径，设置为默认路径
            if (strlen(g_sMetaComm.sWriteOption.strCsrSavePath) > 0)
            {
                WCHAR wszPath[MAX_PATH] = {0};
                MultiByteToWideChar(CP_ACP, 0, g_sMetaComm.sWriteOption.strCsrSavePath, -1, wszPath, MAX_PATH);

                IShellItem *psi = NULL;
                hr = SHCreateItemFromParsingName(wszPath, NULL, IID_PPV_ARGS(&psi));
                if (SUCCEEDED(hr))
                {
                    pFileOpen->SetDefaultFolder(psi);
                    psi->Release();
                }
            }

            // 显示对话框
            hr = pFileOpen->Show(NULL);
            if (SUCCEEDED(hr))
            {
                IShellItem *pItem = NULL;
                hr = pFileOpen->GetResult(&pItem);
                if (SUCCEEDED(hr))
                {
                    PWSTR pszFilePath = NULL;
                    hr = pItem->GetDisplayName(SIGDN_FILESYSPATH, &pszFilePath);
                    if (SUCCEEDED(hr))
                    {
                        // 转换为多字节字符串并保存
                        char szPathA[MAX_PATH] = {0};
                        WideCharToMultiByte(CP_ACP, 0, pszFilePath, -1, szPathA, MAX_PATH, NULL, NULL);
                        strcpy_s(g_sMetaComm.sWriteOption.strCsrSavePath, szPathA);

                        CoTaskMemFree(pszFilePath);
                    }
                    pItem->Release();
                }
            }
            pFileOpen->Release();
        }
        else
        {
            // 如果现代对话框创建失败，回退到传统方法
            BROWSEINFO bi = { 0 };
            bi.lpszTitle = _T("请选择CSR保存文件夹");

            // 如果已经有保存路径，设置为默认路径
            TCHAR szInitialPath[MAX_PATH] = {0};
            if (strlen(g_sMetaComm.sWriteOption.strCsrSavePath) > 0)
            {
                #ifdef UNICODE
                MultiByteToWideChar(CP_ACP, 0, g_sMetaComm.sWriteOption.strCsrSavePath, -1, szInitialPath, MAX_PATH);
                #else
                strcpy_s(szInitialPath, g_sMetaComm.sWriteOption.strCsrSavePath);
                #endif
                bi.pszDisplayName = szInitialPath;
            }

            LPITEMIDLIST pidl = SHBrowseForFolder(&bi);
            if (pidl != NULL)
            {
                TCHAR szPath[MAX_PATH] = {0};
                if (SHGetPathFromIDList(pidl, szPath))
                {
                    #ifdef UNICODE
                    char szPathA[MAX_PATH] = {0};
                    WideCharToMultiByte(CP_ACP, 0, szPath, -1, szPathA, MAX_PATH, NULL, NULL);
                    strcpy_s(g_sMetaComm.sWriteOption.strCsrSavePath, szPathA);
                    #else
                    strcpy_s(g_sMetaComm.sWriteOption.strCsrSavePath, szPath);
                    #endif
                }
                CoTaskMemFree(pidl);
            }
        }
    }
}

void CSNWriterDlg::OnRkpUploadcsrtoyn()
{
	// TODO: 在此添加命令处理程序代码
	if (g_sMetaComm.sWriteOption.bUploadCsrToYN)
    {
        g_sMetaComm.sWriteOption.bUploadCsrToYN = false;
		m_SNMenu.GetSubMenu(2)->CheckMenuItem(ID_RKP_UPLOADCSRTOYN, MF_UNCHECKED | MF_BYCOMMAND);
    }
    else
    {
        g_sMetaComm.sWriteOption.bUploadCsrToYN = true;
		m_SNMenu.GetSubMenu(2)->CheckMenuItem(ID_RKP_UPLOADCSRTOYN, MF_CHECKED | MF_BYCOMMAND);
		g_sMetaComm.sWriteOption.bGetCsrFromDut = true;
		m_SNMenu.GetSubMenu(2)->CheckMenuItem(ID_OPTION_GETCSRFROMDUT, MF_CHECKED | MF_BYCOMMAND);
    }
}

void CSNWriterDlg::OnEnChangeOrderEdit()
{
	GetDlgItemText(IDC_ORDER_EDIT, m_strOrder);
	strcpy_s(g_sMetaComm.strOrder, m_strOrder.GetString());
}


int CSNWriterDlg::UpdateUIMsg(const char * strMsg, ...)
{
    int iRet = 0;
    char szBuf[256] = "";
    va_list varg;

    va_start(varg, strMsg);
    iRet = vsprintf_s(szBuf, strMsg, varg);
    va_end(varg);

    g_pMainDlg->SetDlgItemText(IDC_OPERATE_INFO, szBuf);

    return iRet;
}

void CSNWriterDlg::OnStnClickedOperateInfo()
{
	// TODO: 在此添加控件通知处理程序代码
}


void CSNWriterDlg::OnNMCustomdrawProgressSn(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMCUSTOMDRAW pNMCD = reinterpret_cast<LPNMCUSTOMDRAW>(pNMHDR);
	// TODO: 在此添加控件通知处理程序代码
	*pResult = 0;
}

void CSNWriterDlg::ResetUIItem() 
{
	char title[100];
	const char Version[100] = "NUU_SN_Writer_v2452_20250908";

	if(g_sMetaComm.smesconnect.strMes[0] == 'L')
	{
		sprintf(title, "%s (LA MES)", Version);
	}
	else
	{
		sprintf(title, "%s (HT MES)", Version);
	}

    GetDlgItem(IDC_DIALOG_TITLE)->SetWindowText(title);
}
