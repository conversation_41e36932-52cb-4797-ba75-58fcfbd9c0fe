<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="SN_Writer"
	ProjectGUID="{F60BB342-E756-4D97-A3F8-49347225D770}"
	RootNamespace="SN_Writer"
	Keyword="MFCProj"
	TargetFrameworkVersion="196613"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)temp\$(Projectname)\$(ConfigurationName)\"
			IntermediateDirectory="$(SolutionDir)temp\$(Projectname)\$(ConfigurationName)\"
			ConfigurationType="1"
			UseOfMFC="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="false"
				ValidateParameters="true"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;$(ProjectDir)&quot;;&quot;$(ProjectDir)SNbase\inc&quot;;&quot;$(ProjectDir)Execute&quot;;&quot;$(ProjectDir)Extra\CBtnST&quot;;&quot;$(ProjectDir)Form\About&quot;;&quot;$(ProjectDir)Form\AutoGen&quot;;&quot;$(ProjectDir)Form\ScanData&quot;;&quot;$(ProjectDir)Form\Identify&quot;;&quot;$(ProjectDir)Form\FileSel&quot;;&quot;$(ProjectDir)Form\SystemConfig&quot;;&quot;$(ProjectDir)MtkLib\AT_cmd\inc&quot;;&quot;$(ProjectDir)CBtnST&quot;;&quot;$(ProjectDir)MtkLib\Brom\inc&quot;;&quot;$(ProjectDir)MtkLib\DebugTrace\inc&quot;;&quot;$(ProjectDir)MtkLib\SNFstream\inc&quot;;&quot;$(ProjectDir)Common&quot;;&quot;$(ProjectDir)MtkLib\Eboot\inc&quot;;&quot;$(ProjectDir)MtkLib\Meta\inc&quot;;&quot;$(ProjectDir)MtkLib\Security\inc&quot;;&quot;$(ProjectDir)MtkLib\USBSwitchTool\inc&quot;"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;__META_LTE__"
				MinimalRebuild="true"
				ExceptionHandling="2"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="$(IntDir)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="shlwapi.lib &quot;./mtklib/meta/lib/*.lib&quot; &quot;./mtklib/Eboot/lib/*.lib&quot; &quot;./mtklib/Security/lib/*.lib&quot; &quot;./mtklib/SNFstream/lib/*.lib&quot; &quot;./mtklib/DebugTrace/lib/*.lib&quot; &quot;./mtklib/usbswitchtool/lib/*.lib&quot;"
				OutputFile="$(SolutionDir)Output\$(ProjectName)_d.exe"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				SubSystem="2"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)temp\$(Projectname)\$(ConfigurationName)\"
			IntermediateDirectory="$(SolutionDir)temp\$(Projectname)\$(ConfigurationName)\"
			ConfigurationType="1"
			UseOfMFC="2"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="false"
				ValidateParameters="true"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				EnableIntrinsicFunctions="true"
				AdditionalIncludeDirectories="&quot;$(ProjectDir)&quot;;&quot;$(ProjectDir)SNbase\inc&quot;;&quot;$(ProjectDir)Execute&quot;;&quot;$(ProjectDir)Extra\CBtnST&quot;;&quot;$(ProjectDir)Form\About&quot;;&quot;$(ProjectDir)Form\AutoGen&quot;;&quot;$(ProjectDir)Form\ScanData&quot;;&quot;$(ProjectDir)Form\Identify&quot;;&quot;$(ProjectDir)Form\FileSel&quot;;&quot;$(ProjectDir)Form\SystemConfig&quot;;&quot;$(ProjectDir)MtkLib\AT_cmd\inc&quot;;&quot;$(ProjectDir)CBtnST&quot;;&quot;$(ProjectDir)MtkLib\Brom\inc&quot;;&quot;$(ProjectDir)MtkLib\DebugTrace\inc&quot;;&quot;$(ProjectDir)MtkLib\SNFstream\inc&quot;;&quot;$(ProjectDir)Common&quot;;&quot;$(ProjectDir)MtkLib\Eboot\inc&quot;;&quot;$(ProjectDir)MtkLib\Meta\inc&quot;;&quot;$(ProjectDir)MtkLib\Security\inc&quot;;&quot;$(ProjectDir)MtkLib\USBSwitchTool\inc&quot;"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;__META_LTE__"
				MinimalRebuild="false"
				ExceptionHandling="2"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="$(IntDir)"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="shlwapi.lib &quot;./mtklib/meta/lib/*.lib&quot; &quot;./mtklib/Eboot/lib/*.lib&quot; &quot;./mtklib/Security/lib/*.lib&quot; &quot;./mtklib/SNFstream/lib/*.lib&quot; &quot;./mtklib/DebugTrace/lib/*.lib&quot; &quot;./mtklib/usbswitchtool/lib/*.lib&quot;"
				OutputFile="$(SolutionDir)Output\$(ProjectName).exe"
				LinkIncremental="1"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			>
			<File
				RelativePath="Encryption.cpp"
				>
			</File>
			<File
				RelativePath=".\inttypes.h"
				>
			</File>
			<File
				RelativePath=".\LibKpaUtil.h"
				>
			</File>
			<File
				RelativePath=".\LibKpaUtil.lib"
				>
			</File>
			<File
				RelativePath=".\LibKPHA.h"
				>
			</File>
			<File
				RelativePath=".\LibKPHA.lib"
				>
			</File>
			<File
				RelativePath=".\libturkey.lib"
				>
			</File>
			<File
				RelativePath=".\libuploader.h"
				>
			</File>
			<File
				RelativePath=".\LibUploader.lib"
				>
			</File>
			<File
				RelativePath=".\MesConnectDlg.cpp"
				>
			</File>
			<File
				RelativePath=".\MesConnectDlg.h"
				>
			</File>
			<File
				RelativePath=".\MesServerProxy.cpp"
				>
			</File>
			<File
				RelativePath=".\MesServerProxy.h"
				>
			</File>
			<File
				RelativePath="SN Writer.cpp"
				>
			</File>
			<File
				RelativePath="SN WriterDlg.cpp"
				>
			</File>
			<File
				RelativePath="StdAfx.cpp"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\stdint.h"
				>
			</File>
			<Filter
				Name="Common_SRC"
				>
				<File
					RelativePath="Common\Common.cpp"
					>
				</File>
			</Filter>
			<Filter
				Name="Exec_SRC"
				>
				<File
					RelativePath="Execute\SNbase.cpp"
					>
				</File>
				<File
					RelativePath="Execute\SPexc.cpp"
					>
				</File>
			</Filter>
			<Filter
				Name="MtkLib_SRC"
				>
				<Filter
					Name="ATCmd_SRC"
					>
					<File
						RelativePath="MtkLib\AT_cmd\src\AtProcess.cpp"
						>
					</File>
					<File
						RelativePath="MtkLib\AT_cmd\src\rs232Module.cpp"
						>
					</File>
				</Filter>
				<Filter
					Name="DebugTrace_SRC"
					>
					<File
						RelativePath="MtkLib\DebugTrace\src\xboot_debug.cpp"
						>
					</File>
				</Filter>
				<Filter
					Name="Security_SRC"
					>
					<File
						RelativePath="MtkLib\Security\src\AUTH.cpp"
						>
					</File>
					<File
						RelativePath="MtkLib\Security\src\SCERT.cpp"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="Form_SRC"
				>
				<Filter
					Name="FileSel_SRC"
					>
					<File
						RelativePath="Form\FileSel\FileSelDlg.cpp"
						>
					</File>
				</Filter>
				<Filter
					Name="Identify_SRC"
					>
					<File
						RelativePath="Form\Identify\ChgPasswdDlg.cpp"
						>
					</File>
					<File
						RelativePath="Form\Identify\VerifyPasswdDlg.cpp"
						>
					</File>
				</Filter>
				<Filter
					Name="ScanData_SRC"
					>
					<File
						RelativePath="Form\ScanData\ScanData.cpp"
						>
					</File>
				</Filter>
				<Filter
					Name="SystemConfig_SRC"
					>
					<File
						RelativePath="Form\SystemConfig\SystemConfig.cpp"
						>
					</File>
				</Filter>
				<Filter
					Name="About_SRC"
					>
					<File
						RelativePath="Form\About\AboutSNDlg.cpp"
						>
					</File>
				</Filter>
				<Filter
					Name="AutoGen_SRC"
					>
					<File
						RelativePath="Form\AutoGen\autogendlg.cpp"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="Extra_SRC"
				>
				<Filter
					Name="BtnST_SRC"
					>
					<File
						RelativePath="Extra\CBtnST\BtnST.cpp"
						>
					</File>
					<File
						RelativePath="Extra\CBtnST\CeXDib.cpp"
						>
					</File>
					<File
						RelativePath="Extra\CBtnST\ShadeButtonST.cpp"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="Agenew_SRC"
				>
				<File
					RelativePath=".\CodeDatabase\CodeDatabase.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			>
			<File
				RelativePath="Encryption.h"
				>
			</File>
			<File
				RelativePath="Resource.h"
				>
			</File>
			<File
				RelativePath="SN Writer.h"
				>
			</File>
			<File
				RelativePath="SN WriterDlg.h"
				>
			</File>
			<File
				RelativePath="StdAfx.h"
				>
			</File>
			<Filter
				Name="Common_HDR"
				>
				<File
					RelativePath="Common\Common.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Exec_HDR"
				>
				<File
					RelativePath="Execute\SNbase.h"
					>
				</File>
				<File
					RelativePath="Execute\SPexc.h"
					>
				</File>
			</Filter>
			<Filter
				Name="MtkLib_HDR"
				>
				<Filter
					Name="Meta_HDR"
					>
					<File
						RelativePath="MtkLib\Meta\inc\C2kAgent_api.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\C2kAgent_api_datatype.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\FtaAgent_api.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\HdcpEncryption.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\meta.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\meta_boot_def.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\meta_dll_audio.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\meta_dll_connectivity.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\meta_dll_gps.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\meta_dll_mm.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\meta_dll_nfc.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\mtk_mcu.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\SLA_Challenge.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\sp_brom.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\sp_conn_para.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\sp_md_info.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Meta\inc\xboot.h"
						>
					</File>
				</Filter>
				<Filter
					Name="ATCmd_HDR"
					>
					<File
						RelativePath="MtkLib\AT_cmd\inc\AtProcess.h"
						>
					</File>
					<File
						RelativePath="MtkLib\AT_cmd\inc\rs232Module.h"
						>
					</File>
				</Filter>
				<Filter
					Name="DebugTrace_HDR"
					>
					<File
						RelativePath="MtkLib\DebugTrace\inc\Mdebug.h"
						>
					</File>
					<File
						RelativePath="MtkLib\DebugTrace\inc\xboot_debug.h"
						>
					</File>
				</Filter>
				<Filter
					Name="Security_HDR"
					>
					<File
						RelativePath="MtkLib\Security\inc\AUTH.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Security\inc\des.h"
						>
					</File>
					<File
						RelativePath="MtkLib\Security\inc\SCERT.h"
						>
					</File>
				</Filter>
				<Filter
					Name="SNFstream_HDR"
					>
					<File
						RelativePath="MtkLib\SNFstream\inc\AutoGen.h"
						>
					</File>
					<File
						RelativePath="MtkLib\SNFstream\inc\SNFstream.h"
						>
					</File>
				</Filter>
				<Filter
					Name="USBSwitchTool_HDR"
					>
					<File
						RelativePath="MtkLib\USBSwitchTool\inc\USBSwitchDLLibrary.h"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="Form_HDR"
				>
				<Filter
					Name="FileSel_HDR"
					>
					<File
						RelativePath="Form\FileSel\FileSelDlg.h"
						>
					</File>
				</Filter>
				<Filter
					Name="Identify_HDR"
					>
					<File
						RelativePath="Form\Identify\ChgPasswdDlg.h"
						>
					</File>
					<File
						RelativePath="Form\Identify\VerifyPasswdDlg.h"
						>
					</File>
				</Filter>
				<Filter
					Name="ScanData_HDR"
					>
					<File
						RelativePath="Form\ScanData\ScanData.h"
						>
					</File>
				</Filter>
				<Filter
					Name="SystemConfig_HDR"
					>
					<File
						RelativePath="Form\SystemConfig\SystemConfig.h"
						>
					</File>
				</Filter>
				<Filter
					Name="About_HDR"
					>
					<File
						RelativePath="Form\About\AboutSNDlg.h"
						>
					</File>
				</Filter>
				<Filter
					Name="AutoGen_HDR"
					>
					<File
						RelativePath="Form\AutoGen\autogendlg.h"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="Extra_HDR"
				>
				<Filter
					Name="BtnST_HDR"
					>
					<File
						RelativePath="Extra\CBtnST\BtnST.h"
						>
					</File>
					<File
						RelativePath="Extra\CBtnST\CeXDib.h"
						>
					</File>
					<File
						RelativePath="Extra\CBtnST\ShadeButtonST.h"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="Agenew_HDR"
				>
				<File
					RelativePath=".\CodeDatabase\CodeDatabase.h"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav"
			>
			<File
				RelativePath=".\res\bitmap1.bmp"
				>
			</File>
			<File
				RelativePath="res\close_btn.bmp"
				>
			</File>
			<File
				RelativePath="res\FAIL.bmp"
				>
			</File>
			<File
				RelativePath="res\logo.bmp"
				>
			</File>
			<File
				RelativePath=".\res\logoNuu.bmp"
				>
			</File>
			<File
				RelativePath="res\menu_btn.bmp"
				>
			</File>
			<File
				RelativePath="res\min_btn.bmp"
				>
			</File>
			<File
				RelativePath="res\PASS.bmp"
				>
			</File>
			<File
				RelativePath="res\SN Writer.ico"
				>
			</File>
			<File
				RelativePath="SN Writer.rc"
				>
			</File>
		</Filter>
		<File
			RelativePath="ReadMe.txt"
			>
		</File>
	</Files>
	<Globals>
		<Global
			Name="RESOURCE_FILE"
			Value="SN Writer.rc"
		/>
	</Globals>
</VisualStudioProject>
