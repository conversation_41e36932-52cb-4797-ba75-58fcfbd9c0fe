#ifndef LIBKPAUTIL_H_
#define LIBKPAUTIL_H_

#include <stdint.h>
#include <cstdarg>
#include <string>
#include <vector>
#include <unordered_set>

#if (_MSC_VER < 1300)
   typedef signed char       int8_t;
   typedef signed short      int16_t;
   typedef signed int        int32_t;
   typedef unsigned char     uint8_t;
   typedef unsigned short    uint16_t;
   typedef unsigned int      uint32_t;
#else
   typedef signed __int8     int8_t;
   typedef signed __int16    int16_t;
   typedef signed __int32    int32_t;
   typedef unsigned __int8   uint8_t;
   typedef unsigned __int16  uint16_t;
   typedef unsigned __int32  uint32_t;
#endif
typedef signed __int64       int64_t;
typedef unsigned __int64     uint64_t;

using std::string;
using std::vector;

#ifdef LIBKPAUTIL_EXPORTS
#define LIBKPAUTIL_API __declspec(dllexport)
#else
#define LIBKPAUTIL_API __declspec(dllimport)
#endif

namespace trustkernel {
    class Config;
    class Logger;
    class Licenser;
    class Device;
    class DefaultLicenser;

#ifdef __cplusplus
extern "C" {
#endif
    LIBKPAUTIL_API int unhexLify(const string &str,
                                uint8_t *arr, size_t *array_size);
#ifdef __cplusplus
}
#endif

};

class INIReader;

#pragma warning(push)
#pragma warning(disable:4251)

class LIBKPAUTIL_API trustkernel::Config {
private:
    void initConfiguration();
    Config::Config(string configFile);
    INIReader *reader;
    bool configFileLoaded;
    
    bool forceInit;
    bool secondary;

    bool enableIfaa;
    bool enableSoter;

    bool enableTruststores;
    std::unordered_set<string> truststores;

    bool enableDaccess;
    bool unlockDaccess;
    string daccessUrl;

    /* vturkey related */
    bool enableVTurkey;
    string vTurkeyUrl;
    string keyboxUuid;
    bool installKeybox;

    /* upload */
    bool enableUpload;
    string clientCertPath;
    string clientCertPassword;
    string uploaderUrl;
    string uploadCmd;

    /* errata */
    bool errata1000001;
    bool errata2000001;
    bool errata2000002;

    bool Config::readBoolean(const string &section,
        const string &name, bool defaultValue);
    string Config::readString(const string &section,
        const string &name, const string &defaultValue);

    void buildTruststoreConfiguration(string &strTruststores);

public:
    bool isConfigLoaded() { return configFileLoaded; }

    bool getForceInit() { return forceInit; }
    bool getSecondary() { return secondary; }

    bool getEnableIfaa() { return enableIfaa; }
    bool getEnableSoter() { return enableSoter; }

    bool getEnableTruststores() const { return enableTruststores; }
    const std::unordered_set<string>& getTruststores() const { return truststores; }

    bool getEnableDaccess() { return enableDaccess; }
    string getDaccessUrl() { return daccessUrl; }
    bool getUnlockDaccess() { return unlockDaccess; }

    bool getEnableVTurkey() { return enableVTurkey; }
    string getVTurkeyUrl() { return vTurkeyUrl; }
    string getKeyboxUuid() { return keyboxUuid; }
    bool getInstallKeybox() { return installKeybox; }

    bool getEnableUpload() { return enableUpload; }
    string getClientCertPath() { return clientCertPath; }
    string getClientCertPassword() { return clientCertPassword; }
    string getUploaderUrl() { return uploaderUrl; }
    string getUploadCommand() { return uploadCmd; }

    bool getErrata1000001() { return errata1000001; }
    bool getErrata2000001() { return errata2000001; }
    bool getErrata2000002() { return errata2000002; }

public:
    static Config *getConfig();
    static void setConfigFile(const string &__configFile) {
        configFile = __configFile;
    }

    static const string truststoreToString(unsigned int type);

private:
    static Config *__thisptr;
    static string configFile;


};

class LIBKPAUTIL_API trustkernel::Logger {
private:
    static string __path;
    static Logger *__thisptr;

public:
    enum LogLevel {
        LOGGER_ERR,
        LOGGER_NOTICE,
        LOGGER_WARN,
        LOGGER_LOG
    };

    const char *stringFromLogLevel(LogLevel level);

    void log(const char *function,
        LogLevel level, const char *fmt, ...);

    void logvsfmt(const char *function,
        LogLevel level, const char *fmt, va_list arg);
    
    string formatLogToString(const char *function,
        LogLevel level, const char *fmt, va_list arg);

    static int setLoggerPath(const string &logPath);
    static Logger *getLogger();

private:
    string path;
    Logger(const string &logPath) { path = logPath; }

    void lock();
    void unlock();

    char *format(const char *fmt, va_list va_args);
    void logStr(const char *function, Logger::LogLevel level, char *buf);
};

class LIBKPAUTIL_API trustkernel::Device {
private:
    string teeUuid;
    string reeUuid;
    string ekPubkey;
    string ekPubkeySignature;

    string soterUuid;
    string soterPubkey;
    string soterPubkeySignature;
    string securityLevel;

    string teeOsVersion;
    string teeOsVersionAll;
    string androidVersion;

    string digest;
    uint32_t nonce;
    uint32_t keyId;
    uint32_t revokeId;

    string brandName;
    string deviceModel;
    string socModel;
    string serialNo;
    string tkRpmbblob;
    string buildProduct;
    vector<string> deviceImei;
    string groupUuid;

    int stripPKCS1Key(const string &rawKey, string &KeySignature, string &key);
    int updateField(const string &key, const string &value);

    class ConfigLine {
    private:
        char *key, *value;
        char *trim(const char *str, const char *endptr);

    public:
        ConfigLine(): key(NULL), value(NULL) { }
        ~ConfigLine() { free(key); free(value); }

        char *acquireKey() {
            char *k = key;
            key = NULL;

            return k;
        };

        char *acquireValue() {
            char *val = value;
            value = NULL;

            return val;
        };

        const char *parseLine(const char *str, size_t length);
    };

public:
    Device(): nonce(0), deviceImei(4) { }

    int setEkPubkey(const string &pubkey);
    void setSoterUuid(const string &soterUuid);
    bool isSoterEnable() { return soterUuid.length() != 0; }

    int setSoterPubkey(const string &soterPubkey);
    void setAndroidVersion(const string &sdklevel) { androidVersion = sdklevel; }

    void setLicenseDigest(unsigned char *licenseDigest, size_t digestLen);
    void setLicenseDigest(const string &licenseDigest) { digest = licenseDigest; }
    void setNonce(uint32_t __nonce) { nonce = __nonce; }
    void setRevokeId(uint32_t __revokeId) { revokeId = __revokeId;}

    void setTeeUuid(const string &uuid) { teeUuid = uuid; }
    void setReeUuid(const string &uuid) { reeUuid = uuid; }
    void setKeyId(uint32_t keyid) { keyId = keyid; }
    void setTeeOsVersion(const string &teeOsVersion);

    void setBuildProduct(const string &product) { buildProduct = product; }
    void setSerialNo(const string &sn) { serialNo = sn; }
    void setDeviceImeiByIndex(int index, const string &imei) { deviceImei[index - 1] = imei; }
    void setGroupUuid(const string &__groupUuid) { groupUuid = __groupUuid; }

    string getReeUuid() { return reeUuid; }
    string getTeeUuid() { return teeUuid; }
    string getEkPubkey() { return ekPubkey; }
    string getEkPubkeySignature() { return ekPubkeySignature; }
    string getSoterUuid() { return soterUuid; }
    string getSoterPubkey() { return soterPubkey; }
    string getSoterPubkeySignature() { return soterPubkeySignature; }
    string getSecurityLevel() { return securityLevel; }
    string getTeeOsVersion() { return teeOsVersion; }
    string getTeeOsVersionAll() { return teeOsVersionAll; }
    string getAndroidVersion() { return androidVersion; }
    string getLicenseDigest() { return digest; }
    uint32_t getNonce() { return nonce; }
    string getBrandName() { return brandName; }
    string getDeviceModel() { return deviceModel; }
    string getSocModel() { return socModel; }
    string getSerialNo() { return serialNo; }
    string getTkRpmbblob() { return tkRpmbblob; }
    string getBuildProduct() { return buildProduct; }
    string getGroupUuid() { return groupUuid; }
    uint32_t getKeyId() { return keyId; }

    uint32_t getRevokeId() { return revokeId; }

    string getDeviceImeiByIndex(int index) { return deviceImei[index - 1]; }

    int parseConfigFile(const char *data, size_t length);
};

class LIBKPAUTIL_API trustkernel::Licenser {
public:
    static size_t RSA2048_LENGTH;
    static size_t SHA256_LENGTH;

    static size_t CERT_SIZE;

    static size_t SECONDARY_CERT_SIZE;
    static uint32_t SECONDARY_BLOCK_MAGIC;
    static uint32_t SECONDARY_VERSION;

    static unsigned int HASH_VERSION;

    size_t getSecondaryLicenseLength() {
        return sizeof(uint32_t) + sizeof(uint32_t) + SECONDARY_CERT_SIZE;
    }

    virtual int issueSecondaryLicense(Device *device, unsigned char *buf, size_t bufLength) {
        (void) device;
        (void) bufLength;
        (void) buf;
        return -ENOSYS;
    }

    virtual int issueRevokeLicense(Device *device, unsigned char *buf, size_t bufLength) {
        (void) device;
        (void) bufLength;
        (void) buf;
        return -ENOSYS;
    }

    /* return (1 << 30) - 1 for unlimited */
    virtual int getSecondaryLicenseLeftCount() {
        return (1 << 30) - 1;
    }

    size_t getPrimaryLicenseLength(size_t certBufSize);

    virtual int issuePrimaryLicense(Device *device, unsigned char *certBuf, unsigned int certBufSize,
        unsigned char *buf, size_t bufLength) {
        (void) device;
        (void) bufLength;
        (void) buf;
        (void) certBuf;
        (void) certBufSize;
        return -ENOSYS;
    }
};

class LIBKPAUTIL_API trustkernel::DefaultLicenser: public trustkernel::Licenser {
private:
    string privateKeyPem;
    int sign(void *msg, size_t msg_len,
        void *hdr, size_t hdr_size, unsigned char *signature);
    int sha256(const unsigned char *msg, size_t msg_len,
        void *hdr, size_t hdr_size, unsigned char *digest);

    int issueLicense(Device *device,
                     unsigned char *buf, size_t bufLength,
                     uint32_t type, uint32_t token);
public:
    DefaultLicenser(string privateKey);

    virtual int issuePrimaryLicense(Device *device,
            unsigned char *certBuf, unsigned int certBufSize,
            unsigned char *buf, size_t bufLength);

    virtual int issueSecondaryLicense(Device *device,
            unsigned char *buf, size_t buf_len);

    virtual int issueRevokeLicense(Device *device,
            unsigned char *buf, size_t buf_len);
};

#pragma warning(pop)

#endif